import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from 'express'
import consola from 'consola'
import { Router as ExpressRouter } from 'express'
import { z } from 'zod'
import { clearUserSession, isAuthenticated, setUserSession } from '../../shared/middleware/session-middleware.ts'
import AuthService from '../../shared/services/auth-service.ts'
import DbService from '../../shared/services/db-service.ts'
import { readConfig } from '../../shared/utils/config-utils.ts'
import { sendMail } from '../../shared/utils/mailer-utils.ts'
import { generateResetToken, getTokenExpiry } from '../../shared/utils/token-utils.ts'

const router: Router = ExpressRouter()

/**
 * Convert minutes to milliseconds
 */
const minutesToMs = (minutes: number): number => minutes * 60 * 1000

// Login validation schema
const loginSchema = z
  .object({
    email: z
      .string({
        required_error: 'E-posta adresi zorunludur',
      })
      .email('Geçerli bir e-posta adresi giriniz'),
    password: z
      .string({
        required_error: 'Şifre zorunludur',
      })
      .min(1, 'Şifre giriniz'),
  })
  .strict()

/**
 * @openapi
 * /auth/login:
 *   post:
 *     tags: [Auth]
 *     summary: Kullanıcı girişi yapar
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: Admin123!
 *     responses:
 *       200:
 *         description: Başarılı giriş
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: number
 *                 email:
 *                   type: string
 *                 full_name:
 *                   type: string
 *                 session_expires_at:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Geçersiz istek
 *       401:
 *         description: Kimlik doğrulama hatası
 *       500:
 *         description: Sunucu hatası
 */
const login: RequestHandler = async (req, res) => {
  try {
    const result = loginSchema.safeParse(req.body || {})
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { email, password } = result.data
    const user = await AuthService.verifyCredentials({ email, password })

    if (!user) {
      res.status(401).json({ error: 'E-posta veya şifre hatalı' })
      return
    }

    setUserSession(req, user.id, user.email)
    res.json({
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      session_expires_at: req.session?.cookie?.expires || null,
    })
  }
  catch (error) {
    consola.error('Giriş yapılırken bir sorun oluştu:', error)
    res.status(500).json({ error: 'Beklenmedik bir hata oluştu' })
  }
}

// Session validation schema
const sessionSchema = z.object({
  userId: z.number().int().positive('Geçersiz kullanıcı ID'),
  cookie: z
    .object({
      expires: z.date().optional(),
    })
    .optional(),
})

/**
 * @openapi
 * /auth/logout:
 *   get:
 *     tags: [Auth]
 *     summary: Kullanıcı oturumunu sonlandırır
 *     security:
 *       - session: []
 *     responses:
 *       200:
 *         description: Başarılı çıkış
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Sunucu hatası
 */
const logout: RequestHandler = (req, res) => {
  try {
    clearUserSession(req)
    res.json({ message: 'Oturumunuz sonlandırıldı' })
  }
  catch (error) {
    consola.error('Çıkış yapılırken bir sorun oluştu:', error)
    res.status(500).json({ error: 'Beklenmedik bir hata oluştu' })
  }
}

/**
 * @openapi
 * /auth/get-current-user:
 *   get:
 *     tags: [Auth]
 *     summary: Mevcut kullanıcı bilgilerini getirir
 *     security:
 *       - session: []
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: number
 *                 email:
 *                   type: string
 *                 full_name:
 *                   type: string
 *                 created_at:
 *                   type: string
 *                   format: date-time
 *                 updated_at:
 *                   type: string
 *                   format: date-time
 *                 session_expires_at:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Yetkisiz erişim
 *       404:
 *         description: Kullanıcı bulunamadı
 *       500:
 *         description: Sunucu hatası
 */
const getCurrentUser: RequestHandler = async (req, res) => {
  const sessionResult = sessionSchema.safeParse(req.session)

  if (!sessionResult.success) {
    res.status(401).json({
      error: 'Oturum Bulunamadı',
      message: 'Lütfen giriş yapınız',
    })
    return
  }

  const { userId, cookie } = sessionResult.data

  // Check session expiration
  if (cookie?.expires && new Date() > new Date(cookie.expires)) {
    clearUserSession(req)
    res.status(401).json({
      error: 'Oturum Süresi Doldu',
      message: 'Oturumunuzun süresi doldu, lütfen tekrar giriş yapınız',
    })
    return
  }

  try {
    const user = await AuthService.getUserById(userId)

    if (!user) {
      clearUserSession(req)
      res.status(404).json({
        error: 'Kullanıcı Bulunamadı',
        message: 'Kullanıcı bilgilerinize ulaşılamadı',
      })
      return
    }

    // Extend session if it's close to expiring
    const timeUntilExpiry = cookie?.expires ? new Date(cookie.expires).getTime() - Date.now() : 0

    const config = await readConfig()
    if (timeUntilExpiry < minutesToMs(5)) {
      // Less than 5 minutes until expiry
      req.session.cookie.maxAge = minutesToMs(config.project_settings.session.cookie.max_age)
    }

    res.json({
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      created_at: user.created_at,
      updated_at: user.updated_at,
      session_expires_at: cookie?.expires,
    })
  }
  catch (error) {
    consola.error('Kullanıcı bilgileri alınırken hata:', error)
    res.status(500).json({
      error: 'Sunucu Hatası',
      message: 'Kullanıcı bilgileri alınırken beklenmeyen bir hata oluştu',
    })
  }
}

// Forgot password validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Geçerli bir e-posta adresi giriniz'),
})

// Reset password validation schema
const resetPasswordSchema = z.object({
  email: z.string().email(),
  token: z.string().min(10),
  newPassword: z.string().min(6, 'Şifre en az 6 karakter olmalı'),
})

// Create user validation schema
const createUserSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  full_name: z.string().min(1),
})

// Update user validation schema
const updateUserSchema = z.object({
  email: z.string().email().optional(),
  password: z.string().min(6).optional(),
  full_name: z.string().min(1).optional(),
})

/**
 * @openapi
 * /auth/forgot-password:
 *   post:
 *     tags: [Auth]
 *     summary: Şifre sıfırlama bağlantısı gönderir
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: Şifre sıfırlama bağlantısı gönderildi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Geçersiz istek
 *       404:
 *         description: Kullanıcı bulunamadı
 *       500:
 *         description: Sunucu hatası
 */
const forgotPassword: RequestHandler = async (req, res) => {
  const result = forgotPasswordSchema.safeParse(req.body || {})
  if (!result.success) {
    res.status(400).json({ error: 'Geçersiz istek', details: result.error.issues })
    return
  }
  const { email } = result.data
  const user = await AuthService.getUserByEmail(email)
  if (!user) {
    res.status(404).json({ error: 'Kullanıcı bulunamadı' })
    return
  }
  // Generate token and expiry
  const resetToken = generateResetToken()
  const tokenExpiry = getTokenExpiry(1) // 1 hour expiry
  // Save token and expiry to user (assume columns: reset_token, reset_token_expiry)
  const pool = await DbService.getConnection('db')
  await pool.request()
    .input('email', email)
    .input('reset_token', resetToken)
    .input('reset_token_expiry', tokenExpiry)
    .query('UPDATE users SET reset_token=@reset_token, reset_token_expiry=@reset_token_expiry WHERE email=@email')
  // Send email
  const resetUrl = `${req.protocol}://${req.get('host')}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`
  await sendMail({
    to: email,
    subject: 'Şifre Sıfırlama',
    html: `<p>Şifrenizi sıfırlamak için <a href="${resetUrl}">buraya tıklayın</a>.<br/>Bu bağlantı 1 saat geçerlidir.</p>`,
  })
  res.json({ message: 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi.' })
}

/**
 * @openapi
 * /auth/reset-password:
 *   post:
 *     tags: [Auth]
 *     summary: Şifre sıfırlama işlemini tamamlar
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - token
 *               - newPassword
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               token:
 *                 type: string
 *                 example: 1234567890abcdef
 *               newPassword:
 *                 type: string
 *                 example: YeniSifre123!
 *     responses:
 *       200:
 *         description: Şifre başarıyla sıfırlandı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Geçersiz istek veya token
 *       500:
 *         description: Sunucu hatası
 */
const resetPassword: RequestHandler = async (req, res) => {
  const result = resetPasswordSchema.safeParse(req.body || {})
  if (!result.success) {
    res.status(400).json({ error: 'Geçersiz istek', details: result.error.issues })
    return
  }
  const { email, token, newPassword } = result.data
  const pool = await DbService.getConnection('db')
  const userResult = await pool.request().input('email', email).query('SELECT * FROM users WHERE email=@email')
  const user = userResult.recordset[0]
  if (!user || !user.reset_token || !user.reset_token_expiry) {
    res.status(400).json({ error: 'Şifre sıfırlama isteği bulunamadı.' })
    return
  }
  if (user.reset_token !== token) {
    res.status(400).json({ error: 'Geçersiz şifre sıfırlama anahtarı.' })
    return
  }
  if (new Date(user.reset_token_expiry) < new Date()) {
    res.status(400).json({ error: 'Şifre sıfırlama anahtarının süresi dolmuş.' })
    return
  }
  // Update password and clear token
  const hashed = await AuthService.hashPassword(newPassword)
  await pool.request()
    .input('email', email)
    .input('password', hashed)
    .query('UPDATE users SET password=@password, reset_token=NULL, reset_token_expiry=NULL WHERE email=@email')
  res.json({ message: 'Şifreniz başarıyla sıfırlandı.' })
}

/**
 * @openapi
 * /auth/create-user:
 *   post:
 *     tags: [Auth]
 *     summary: Yeni kullanıcı oluşturur
 *     security:
 *       - session: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - full_name
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: YeniSifre123!
 *               full_name:
 *                 type: string
 *                 example: Yeni Kullanıcı
 *     responses:
 *       201:
 *         description: Kullanıcı oluşturuldu
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       400:
 *         description: Geçersiz istek
 *       409:
 *         description: E-posta zaten kayıtlı
 *       500:
 *         description: Sunucu hatası
 */
const createUser: RequestHandler = async (req, res) => {
  const result = createUserSchema.safeParse(req.body || {})
  if (!result.success) {
    res.status(400).json({ error: 'Geçersiz istek', details: result.error.issues })
    return
  }
  const existing = await AuthService.getUserByEmail(result.data.email)
  if (existing) {
    res.status(409).json({ error: 'Bu e-posta zaten kayıtlı' })
    return
  }
  const user = await AuthService.createUser(result.data)
  if (!user) {
    res.status(500).json({ error: 'Kullanıcı oluşturulamadı' })
    return
  }
  res.status(201).json(user)
}

/**
 * @openapi
 * /auth/users:
 *   get:
 *     tags: [Auth]
 *     summary: Kullanıcı listesini getirir
 *     security:
 *       - session: []
 *     responses:
 *       200:
 *         description: Kullanıcılar başarıyla alındı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *       500:
 *         description: Kullanıcılar alınamadı
 */
const getUsers: RequestHandler = async (req, res) => {
  try {
    const pool = await DbService.getConnection('db')
    const result = await pool.request().query('SELECT id, email, full_name, created_at, updated_at FROM users')
    res.json(result.recordset)
  }
  catch (error) {
    consola.error('Kullanıcılar alınamadı:', error)
    res.status(500).json({ error: 'Kullanıcılar alınamadı' })
  }
}

/**
 * @openapi
 * /auth/users/{id}:
 *   delete:
 *     tags: [Auth]
 *     summary: Kullanıcıyı siler
 *     security:
 *       - session: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Kullanıcı silindi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Geçersiz kullanıcı ID
 *       404:
 *         description: Kullanıcı bulunamadı
 *       500:
 *         description: Kullanıcı silinemedi
 */
const deleteUser: RequestHandler = async (req, res) => {
  const id = Number(req.params.id)
  if (!id) {
    res.status(400).json({ error: 'Geçersiz kullanıcı ID' })
    return
  }
  try {
    const pool = await DbService.getConnection('db')
    const result = await pool.request().input('id', id).query('DELETE FROM users WHERE id=@id')
    if (result.rowsAffected[0] === 0) {
      res.status(404).json({ error: 'Kullanıcı bulunamadı' })
      return
    }
    res.json({ message: 'Kullanıcı silindi' })
  }
  catch (error) {
    consola.error('Kullanıcı silinemedi:', error)
    res.status(500).json({ error: 'Kullanıcı silinemedi' })
  }
}

/**
 * @openapi
 * /auth/users/{id}:
 *   put:
 *     tags: [Auth]
 *     summary: Kullanıcıyı günceller
 *     security:
 *       - session: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               full_name:
 *                 type: string
 *     responses:
 *       200:
 *         description: Kullanıcı güncellendi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Geçersiz kullanıcı ID veya veri
 *       404:
 *         description: Kullanıcı bulunamadı
 *       500:
 *         description: Kullanıcı güncellenemedi
 */
const updateUser: RequestHandler = async (req, res) => {
  const id = Number(req.params.id)
  if (!id) {
    res.status(400).json({ error: 'Geçersiz kullanıcı ID' })
    return
  }
  const result = updateUserSchema.safeParse(req.body || {})
  if (!result.success) {
    res.status(400).json({ error: 'Geçersiz istek', details: result.error.issues })
    return
  }
  try {
    const pool = await DbService.getConnection('db')
    const updates = []
    if (result.data.email)
      updates.push(`email='${result.data.email}'`)
    if (result.data.full_name)
      updates.push(`full_name='${result.data.full_name}'`)
    if (result.data.password) {
      const hashed = await AuthService.hashPassword(result.data.password)
      updates.push(`password='${hashed}'`)
    }
    if (updates.length === 0) {
      res.status(400).json({ error: 'Güncellenecek veri yok' })
      return
    }
    const query = `UPDATE users SET ${updates.join(', ')} WHERE id=${id}`
    const resultDb = await pool.request().query(query)
    if (resultDb.rowsAffected[0] === 0) {
      res.status(404).json({ error: 'Kullanıcı bulunamadı' })
      return
    }
    res.json({ message: 'Kullanıcı güncellendi' })
  }
  catch (error) {
    consola.error('Kullanıcı güncellenemedi:', error)
    res.status(500).json({ error: 'Kullanıcı güncellenemedi' })
  }
}

router.post('/login', login)
router.get('/logout', isAuthenticated, logout)
router.get('/get-current-user', isAuthenticated, getCurrentUser)
router.post('/forgot-password', forgotPassword)
router.post('/reset-password', resetPassword)
router.post('/create-user', isAuthenticated, createUser)
router.get('/users', isAuthenticated, getUsers)
router.delete('/users/:id', isAuthenticated, deleteUser)
router.put('/users/:id', isAuthenticated, updateUser)

export default router
