import type { ConnectionPool } from 'mssql'
import type { DatabaseConfig } from '../utils/config-utils.ts'
import sql from 'mssql'
import { readConfig } from '../utils/config-utils.ts'

const pools = new Map()

/**
 * Gets or creates a database connection pool
 * @param {string} name - The name of the connection pool
 * @param {object} [config] - The configuration object for the pool
 * @returns {Promise<sql.ConnectionPool>} The connection pool
 */
async function getPool(name: string, config: DatabaseConfig) {
  if (!pools.has(name)) {
    if (!config) {
      throw new Error(`Bağlantı ismi "${name}" olan sql server bağlantı havuzu bulunamadı.`)
    }
    const pool = new sql.ConnectionPool(config)
    const close = pool.close.bind(pool)
    pool.close = (...args: unknown[]) => {
      pools.delete(name)
      return close(...(args as []))
    }
    pools.set(name, pool.connect())
  }
  return pools.get(name)
}

/**
 * Closes all connection pools
 * @returns {Promise<void>}
 */
async function closeAllPools() {
  await Promise.all(
    Array.from(pools.values()).map(async (connect) => {
      const pool = await connect
      return pool.close()
    }),
  )
}

async function getConnection(connectionName: string): Promise<ConnectionPool> {
  const config = await readConfig()
  const dbConfig = config.database_connections[connectionName as keyof typeof config.database_connections]
  if (!dbConfig) {
    throw new Error(`Bağlantı adı "${connectionName}" olan bir veritabanı bağlantısı bulunamadı.`)
  }
  if (typeof dbConfig !== 'object') {
    throw new TypeError(`Bağlantı adı "${connectionName}" uygun bir veritabanı bağlantısı değil.`)
  }

  return getPool(connectionName, dbConfig)
}

async function getLogoConnectionById(veritabaniId: string): Promise<ConnectionPool> {
  const config = await readConfig()
  const logoConnection = config.project_settings.logo.db_connections.find(
    conn => conn.id === veritabaniId,
  )

  if (!logoConnection) {
    throw new Error(`Veritabanı numarası "${veritabaniId}" olan Logo tanımlaması bulunamadı.`)
  }

  return getPool(veritabaniId, logoConnection.sql)
}

const DbService = {
  getConnection,
  getLogoConnectionById,
  closeAllPools,
}
export default DbService
