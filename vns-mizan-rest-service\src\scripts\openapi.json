{"openapi": "3.0.0", "info": {"title": "VNS Mizan Entegra<PERSON>on <PERSON>", "version": "1.0.0"}, "servers": [{"url": "/v1", "description": "API V1"}], "components": {"securitySchemes": {"sessionAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "connect.sid", "description": "Session based authentication"}}, "schemas": {"AltGrup": {"type": "object", "required": ["id", "kodu", "adi", "veritabani_id"], "properties": {"id": {"type": "integer", "description": "Alt grup benzersiz tanımlayıcısı"}, "kodu": {"type": "string", "description": "Alt grup kodu"}, "adi": {"type": "string", "description": "Alt grup adı"}, "veritabani_id": {"type": "string", "description": "Veritabanı adı"}}}, "VeritabaniBaglantisiSchema": {"type": "object", "required": ["server", "database", "user", "password", "port"], "properties": {"server": {"type": "string", "description": "Veritabanı sunucusu"}, "database": {"type": "string", "description": "Veritabanı adı"}, "user": {"type": "string", "description": "Kullanıcı adı"}, "password": {"type": "string", "description": "Şifre"}, "port": {"type": "integer", "description": "Port numarası", "example": 1433}, "encrypt": {"type": "boolean", "description": "SSL şifreleme kullanılsın mı?", "example": false}, "trust_server_certificate": {"type": "boolean", "description": "<PERSON><PERSON>u sertifikası doğrulanmasın mı?", "example": true}}}, "LogoBaglantisi": {"type": "object", "required": ["id", "name", "sql", "erp"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Bağlantı ID'si"}, "active": {"type": "boolean", "description": "Bağlantı aktif mi?"}, "name": {"type": "string", "description": "Bağlantı adı"}, "sql": {"$ref": "#/components/schemas/VeritabaniBaglantisiSchema"}, "erp": {"type": "object", "properties": {"logodb_master": {"type": "string"}, "firma_numarasi": {"type": "string", "example": 1}, "donem_numarasi": {"type": "string", "example": 1}, "kullanici_adi": {"type": "string"}, "sifre": {"type": "string"}, "elogo": {"type": "object", "properties": {"web_service_url": {"type": "string", "format": "uri", "example": "https://pb-g.elogo.com.tr/PostBoxService.svc"}}}, "rest_settings": {"type": "object", "properties": {"use_rest": {"type": "boolean", "description": "REST API kullanılsın mı?", "example": true}, "rest_api_url": {"type": "string", "format": "uri", "example": "http://127.0.0.1:32001/api/v1"}, "client_key": {"type": "string"}}}}}}}}}, "paths": {"/alt-gruplar": {"get": {"tags": ["Alt Gruplar"], "summary": "Logo veritabanından alt grup listesini getirir", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AltGrup"}}}}}, "400": {"description": "Geçersiz parametreler", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON>"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/ana-gruplar": {"get": {"tags": ["<PERSON>"], "summary": "Logo veritabanından ana grupları listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["id", "kodu", "adi", "veritabani_id"], "properties": {"id": {"type": "number", "description": "<PERSON> grubun ben<PERSON>'si"}, "kodu": {"type": "string", "description": "Ana grup kodu"}, "adi": {"type": "string", "description": "Ana grup adı"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/bolumler": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Logo veritabanından bölüm bilgilerini listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"bolum_no": {"type": "number"}, "bolum_adi": {"type": "string"}, "veritabani_id": {"type": "string"}}}}}}}, "400": {"description": "Geçersiz istek"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/cari-personel": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Logo veritabanından cari personel bilgilerini listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "kodu": {"type": "string"}, "adi": {"type": "string"}, "update_date": {"type": "string", "format": "date-time"}, "veritabani_id": {"type": "string"}}}}}}}, "400": {"description": "Geçersiz istek"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/cariler": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Cari bilgiler<PERSON>ele<PERSON>", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"title": "CariBilgileriResponse", "description": "Logo veritabanından alınan cari hesap listesi", "type": "array", "items": {"type": "object", "required": ["id", "kodu", "veritabani_id"], "properties": {"id": {"type": "number", "description": "<PERSON>i hesap ID'si"}, "kodu": {"type": "string", "description": "<PERSON>i hesap kodu"}, "create_date": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi"}, "update_date": {"type": "string", "format": "date-time", "description": "<PERSON> g<PERSON><PERSON><PERSON><PERSON> tarihi"}, "unvan1": {"type": "string", "description": "Cari hesap ünvanı 1"}, "unvan2": {"type": "string", "description": "Cari hesap ünvanı 2"}, "muhasebe_kodu": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> kodu"}, "doviz1": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON>i"}, "doviz1_adi": {"type": "string", "description": "Birincil dö<PERSON> adı"}, "vergi_dairesi": {"type": "string", "description": "<PERSON><PERSON><PERSON>i"}, "vergi_no": {"type": "string", "description": "Vergi numa<PERSON>ı/TC Kimlik No"}, "sektor": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "bolge": {"type": "string", "description": "<PERSON><PERSON><PERSON> k<PERSON>"}, "grup": {"type": "string", "description": "Grup kodu"}, "temsilci": {"type": "string", "description": "Temsilci adı"}, "efatura_mi": {"type": "boolean", "description": "E-Fatura mükellefi mi?"}, "kilitli": {"type": "boolean", "description": "Hesap kilitli mi?"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}, "post": {"tags": ["<PERSON><PERSON>"], "summary": "<PERSON>ni cari hesap ka<PERSON><PERSON>r veya <PERSON>", "security": [{"sessionAuth": []}], "description": "Yeni bir cari hesap kaydı oluşturur veya mevcut cari hesabı günceller. Vergi numarası veya T.C. Kimlik numarası ile müşteri kaydı yapılabilir. TCKN kullanıldığında ad ve soyad bilgileri zorunludur.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"title": "CariHesapRequest", "description": "<PERSON>i hesap kayıt veya güncelleme isteği için gere<PERSON> al<PERSON>", "type": "object", "required": ["veritabani_id", "kodu", "vkVeyaTckNo", "unvan", "adres", "il", "ilce", "email", "ozelKod", "post<PERSON><PERSON><PERSON><PERSON>"], "properties": {"veritabani_id": {"type": "string", "title": "Veritabanı ID"}, "kodu": {"type": "string", "title": "<PERSON>i hesap kodu", "minLength": 1, "maxLength": 16}, "vkVeyaTckNo": {"type": "string", "title": "Vergi/TC Kimlik No", "minLength": 10, "maxLength": 11}, "unvan": {"type": "string", "title": "Ünvan", "maxLength": 200}, "ad": {"type": "string", "title": "Ad", "maxLength": 50}, "soyad": {"type": "string", "title": "Soyad", "maxLength": 50}, "adres": {"type": "string", "title": "<PERSON><PERSON>", "maxLength": 400}, "il": {"type": "string", "title": "İl", "maxLength": 50}, "ilce": {"type": "string", "title": "İlçe", "maxLength": 50}, "ulke": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "maxLength": 50}, "ulkeKodu": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "maxLength": 10}, "email": {"type": "string", "title": "E-posta", "format": "email"}, "ozelKod": {"type": "string", "title": "<PERSON><PERSON>", "maxLength": 10}, "postaKodu": {"type": "string", "title": "Posta Kodu", "maxLength": 5}}}}}}, "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "data"], "properties": {"message": {"type": "string", "description": "İşlem sonucu mesajı", "example": "Cari kart başarıyla oluşturuldu"}, "data": {"type": "object", "required": ["code", "vkVeyaTckNo", "logicalref"], "properties": {"code": {"type": "string", "description": "<PERSON>i hesap kodu"}, "vkVeyaTckNo": {"type": "string", "description": "Vergi/TC Kimlik No"}, "logicalref": {"type": "number", "description": "Logo sistemindeki referans numarası"}}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "Validasyon hataları"}, "message": {"type": "string", "description": "<PERSON>a mesajı"}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/isyeri-fabrika-ambar-bilgileri": {"get": {"tags": ["İşyeri Fabrika Ambar"], "summary": "Logo veritabanından i<PERSON>, fabrika ve ambar bilgilerini listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"isyeri_no": {"type": "number"}, "isyeri_adi": {"type": "string"}, "fabrika_no": {"type": "number"}, "fabrika_adi": {"type": "string"}, "ambar_no": {"type": "number"}, "ambar_adi": {"type": "string"}, "veritabani_id": {"type": "string"}}}}}}}, "400": {"description": "Geçersiz istek"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/kategoriler": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Logo veritabanından kategorileri listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"title": "KategorilerResponse", "description": "Logo veritabanından alınan kategori listesi", "type": "array", "items": {"type": "object", "required": ["id", "kodu", "adi", "veritabani_id"], "properties": {"id": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> ben<PERSON>'si"}, "kodu": {"type": "string", "description": "<PERSON><PERSON><PERSON> kodu"}, "adi": {"type": "string", "description": "<PERSON><PERSON><PERSON> adı"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/logo-kullanicilari": {"get": {"tags": ["Logo <PERSON>ı<PERSON>ı<PERSON>ı"], "summary": "Logo veritabanındaki kullanıcıları listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"numara": {"type": "number", "description": "Logo kullanıcı numarası"}, "kullanici_adi": {"type": "string", "description": "Kullanıcı adı"}, "adi": {"type": "string", "description": "Kullanıcının adı"}, "soyadi": {"type": "string", "description": "Kullanıcının soyadı"}, "aktif": {"type": "number", "description": "Kullanıcı aktif mi? (1=Aktif, 0=Pasif)"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz istek"}, "401": {"description": "<PERSON><PERSON><PERSON>"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/logo-veritabani-baglantilari": {"get": {"tags": ["Logo Veritabanı Bağlantıları"], "summary": "Tüm Logo veritabanı bağlantılarını listeler", "security": [{"sessionAuth": []}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogoBaglantisi"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"mesaj": {"type": "string"}}}}}}}}, "post": {"tags": ["Logo Veritabanı Bağlantıları"], "summary": "Yeni Logo veritabanı bağlantısı ekler", "security": [{"sessionAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoBaglantisi"}}}}, "responses": {"201": {"description": "Başarıyla oluşturuldu"}, "400": {"description": "Geçersiz istek"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/logo-veritabani-baglantilari/{id}": {"put": {"tags": ["Logo Veritabanı Bağlantıları"], "summary": "Logo veritabanı bağlantısını günceller", "security": [{"sessionAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoBaglantisi"}}}}, "responses": {"200": {"description": "Başarıyla gü<PERSON>llendi"}, "400": {"description": "Geçersiz istek"}, "404": {"description": "Bağlantı bulunamadı"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}, "delete": {"tags": ["Logo Veritabanı Bağlantıları"], "summary": "Logo veritabanı bağlantısını siler", "security": [{"sessionAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Başar<PERSON><PERSON>"}, "404": {"description": "Bağlantı bulunamadı"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}, "get": {"tags": ["Logo Veritabanı Bağlantıları"], "summary": "Logo veritabanı bağlantısını ID'ye göre getirir", "security": [{"sessionAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoBaglantisi"}}}}, "404": {"description": "Bağlantı bulunamadı", "content": {"application/json": {"schema": {"type": "object", "properties": {"mesaj": {"type": "string"}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"mesaj": {"type": "string"}}}}}}}}}, "/markalar": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Logo veritabanından markaları listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["id", "kodu", "adi", "veritabani_id"], "properties": {"id": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> ben<PERSON>'si"}, "kodu": {"type": "string", "description": "<PERSON><PERSON> kodu"}, "adi": {"type": "string", "description": "<PERSON><PERSON> adı"}, "update_date": {"type": "string", "format": "date-time", "description": "<PERSON> g<PERSON><PERSON><PERSON><PERSON> tarihi"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Kullanıcı girişi yapar", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "Admin123!"}}}}}}, "responses": {"200": {"description": "Başarılı giriş", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "full_name": {"type": "string"}, "session_expires_at": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "Geçersiz istek"}, "401": {"description": "Kimlik doğrulama hatası"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/logout": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Kullanıcı oturumunu <PERSON>landırır", "security": [{"session": []}], "responses": {"200": {"description": "Başarılı çıkış", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/get-current-user": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Mevcut kullanıcı bilgilerini getirir", "security": [{"session": []}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "full_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "session_expires_at": {"type": "string", "format": "date-time"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON>"}, "404": {"description": "Kullanıcı bulunamadı"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Şifre sıfırlama bağlantısı gönderir", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}}}}, "responses": {"200": {"description": "Şifre sıfırlama bağlantısı gönderildi", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "400": {"description": "Geçersiz istek"}, "404": {"description": "Kullanıcı bulunamadı"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Şifre sıfırlama işlemini tamamlar", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "token", "newPassword"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "token": {"type": "string", "example": "1234567890abcdef"}, "newPassword": {"type": "string", "example": "YeniSifre123!"}}}}}}, "responses": {"200": {"description": "Şifre başarıyla sıfırlandı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "400": {"description": "Geçersiz istek veya token"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/create-user": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Yeni kullanıcı oluşturur", "security": [{"session": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "full_name"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "YeniSifre123!"}, "full_name": {"type": "string", "example": "<PERSON><PERSON>"}}}}}}, "responses": {"201": {"description": "Kullanıcı oluşturuldu", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Geçersiz istek"}, "409": {"description": "E-posta zaten kayıtlı"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}, "/auth/users": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Kullanıcı listesini getirir", "security": [{"session": []}], "responses": {"200": {"description": "Kullanıcılar başarıyla alındı", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "500": {"description": "Kullanıcılar alınamadı"}}}}, "/auth/users/{id}": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "Kullanıcıyı siler", "security": [{"session": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Kullan<PERSON><PERSON><PERSON> silindi", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "400": {"description": "Geçersiz kullanıcı ID"}, "404": {"description": "Kullanıcı bulunamadı"}, "500": {"description": "Kullanıcı silinemedi"}}}, "put": {"tags": ["<PERSON><PERSON>"], "summary": "Kullan<PERSON><PERSON><PERSON><PERSON><PERSON>", "security": [{"session": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "full_name": {"type": "string"}}}}}}, "responses": {"200": {"description": "Kullanıcı güncellendi", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "400": {"description": "Geçersiz kullanıcı ID veya veri"}, "404": {"description": "Kullanıcı bulunamadı"}, "500": {"description": "Kullanıcı güncellenemedi"}}}}, "/": {"get": {"tags": ["System"], "summary": "API durumunu ve versiyonunu döndürür", "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "VNS Mizan Entegra<PERSON>on <PERSON>"}, "version": {"type": "string", "example": "1.0.0"}, "timestamp": {"type": "string", "example": "21.12.2023 14:30:45"}}}}}}}}}, "/docs": {"get": {"tags": ["System"], "summary": "Swagger UI dokumentasyon sayfasını sunar", "description": "API dokümantasyonunu interaktif bir arayüz ile gösterir", "responses": {"200": {"description": "Swagger UI HTML sayfası", "content": {"text/html": {"schema": {"type": "string"}}}}}}}, "/swagger.json": {"get": {"tags": ["System"], "summary": "OpenAPI spesifikasyonunu JSON formatında sunar", "description": "API'nin OpenAPI/Swagger spesifikasyonunu JSON formatında döndürür", "responses": {"200": {"description": "OpenAPI spesifikasyonu", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/health": {"get": {"tags": ["System"], "summary": "<PERSON><PERSON><PERSON> du<PERSON>u kontrol eder", "description": "<PERSON><PERSON><PERSON>, bellek kullanımı ve diğer temel bilgileri döndürür", "responses": {"200": {"description": "Sistem aktif", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "Sistem aktif"}, "time": {"type": "string", "example": "21.12.2023 14:30:45"}, "uptime": {"type": "number", "description": "Sistem çalışma süresi (saniye)", "example": 3600}, "memory_usage": {"type": "object", "properties": {"heapTotal": {"type": "number", "description": "Toplam heap bellek (byte)"}, "heapUsed": {"type": "number", "description": "Kullanılan heap bellek (byte)"}, "rss": {"type": "number", "description": "Resident <PERSON> (byte)"}}}, "ip": {"type": "string", "description": "İstemci IP adresi", "example": "***********"}}}}}}}}}, "/reyonlar": {"get": {"tags": ["Reyonlar"], "summary": "Logo veritabanından reyon listesini getirir", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"title": "ReyonlarResponse", "description": "Logo veritabanından alınan reyon listesi", "type": "array", "items": {"type": "object", "required": ["id", "kodu", "adi", "veritabani_id"], "properties": {"id": {"type": "number", "description": "<PERSON><PERSON><PERSON> ben<PERSON>'si"}, "kodu": {"type": "string", "description": "<PERSON><PERSON> kodu"}, "adi": {"type": "string", "description": "Reyon adı"}, "veritabani_id": {"type": "string", "description": "Logo veritabanı ID'si"}}}}}}}, "400": {"description": "Geçersiz parametreler", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON>"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/satinalma-faturalari": {"post": {"tags": ["Satınalma Faturaları"], "summary": "Yeni satınalma faturası oluşturur", "security": [{"sessionAuth": []}], "description": "Logo sisteminde yeni bir satınalma faturası oluşturur. Fatura başlık bilgileri ve kalem detayları eklenir.\n\nBu modül hem REST API hem de doğrudan SQL entegrasyonunu desteklemektedir:\n\n- **REST API Entegrasyonu** (`use_rest=true`): Logo REST API'si kullanılarak fatura oluşturulur\n- **Doğrudan SQL Entegrasyonu** (`use_rest=false`): Logo veritabanı tablolarına doğrudan SQL ile fatura eklenir\n\nEntegrasyon türü, veritabanı yapılandırmasındaki `use_rest` ayarına göre otomatik olarak belirlenir.\n", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["veritabani_id", "fatura_turu", "tarihi", "saati", "fatura_satirlari"], "properties": {"veritabani_id": {"type": "string", "format": "uuid", "description": "Logo veritabanı ID'si"}, "logo": {"type": "object", "description": "Logo kullanıcı bilgileri. Eğer belirtilmezse config.json'daki kullanıcı bilgileri kullanılır", "properties": {"kullanici_adi": {"type": "string", "description": "Logo kullanıcı adı"}, "sifre": {"type": "string", "description": "Logo kullanıcı şifresi"}}}, "fatura_turu": {"type": "integer", "enum": [4], "description": "Fatura türü kodu (4=Alınan Hizmet Faturası)"}, "fatura_no": {"type": "string", "maxLength": 16, "description": "Fatura numarası. <PERSON>ş bırakılırsa veya \"~\" girilirse Logo tarafından otomatik atanır"}, "tarihi": {"type": "string", "format": "date", "description": "<PERSON><PERSON> (YYYY-MM-DD)"}, "saati": {"type": "string", "pattern": "^([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$", "description": "<PERSON><PERSON> (HH:MM:SS)"}, "belge_no": {"type": "string", "maxLength": 32, "description": "Belge numarası"}, "ozel_kod": {"type": "string", "maxLength": 10, "description": "Özel kod"}, "cari_kodu": {"type": "string", "maxLength": 16, "description": "<PERSON>i kodu"}, "ambar_kodu": {"type": "integer", "description": "<PERSON><PERSON> kodu"}, "fabrika_kodu": {"type": "integer", "description": "<PERSON><PERSON><PERSON> kodu"}, "aciklama": {"type": "string", "maxLength": 1800, "description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "doviz_kuru": {"type": "number", "description": "Döviz kuru"}, "isyeri_kodu": {"type": "integer", "description": "İşyeri kodu"}, "bolum_kodu": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu"}, "odeme_kodu": {"type": "string", "maxLength": 16, "description": "<PERSON><PERSON><PERSON> kodu"}, "proje_kodu": {"type": "string", "maxLength": 100, "description": "<PERSON><PERSON> kodu"}, "belge_tarihi": {"type": "string", "format": "date", "description": "<PERSON>ge tarihi (YYYY-MM-DD)"}, "fatura_satirlari": {"type": "array", "minItems": 1, "maxItems": 9999, "description": "<PERSON>ura <PERSON>ı<PERSON>ı", "items": {"type": "object", "required": ["satir_turu"], "properties": {"satir_turu": {"type": "integer", "description": "Satır türü (0=Malzeme, 1=Hizmet, 2=İndirim, 3=Masraf, 4=Hizmet)"}, "malzeme_kodu": {"type": "string", "maxLength": 16, "description": "<PERSON><PERSON><PERSON> kodu"}, "ambar_kodu": {"type": "integer", "description": "<PERSON><PERSON> kodu"}, "fabrika_kodu": {"type": "integer", "description": "<PERSON><PERSON><PERSON> kodu"}, "hareket_ozel_kodu": {"type": "string", "maxLength": 17, "description": "Hareket özel kodu"}, "miktar": {"type": "number", "description": "<PERSON><PERSON><PERSON>"}, "birim_fiyat": {"type": "number", "description": "<PERSON><PERSON><PERSON>"}, "aciklama": {"type": "string", "maxLength": 250, "description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "birim_kodu": {"type": "string", "maxLength": 10, "description": "<PERSON><PERSON><PERSON> kodu"}, "kdv_orani": {"type": "number", "description": "KDV oranı"}, "proje_kodu": {"type": "string", "maxLength": 100, "description": "<PERSON><PERSON> kodu"}, "dovizli_birim_fiyat": {"type": "number", "description": "Dövizli birim fiyat"}, "tevkifat_yapilabilir": {"type": "integer", "enum": [0, 1], "description": "Tevkifat yapılabilir (0=Hayır, 1=Evet)"}, "tevkifat_payi1": {"type": "integer", "description": "Tevkifat payı 1"}, "tevkifat_payi2": {"type": "integer", "description": "Tevkifat payı 2"}, "tevkifat_kodu": {"type": "string", "maxLength": 16, "description": "Tevkifat kodu"}, "tevkifat_aciklamasi": {"type": "string", "maxLength": 250, "description": "Tevkifat açıklaması"}, "masraf_merkezi1": {"type": "string", "maxLength": 25, "description": "Masraf <PERSON> 1"}, "masraf_merkezi3": {"type": "string", "maxLength": 25, "description": "Masraf <PERSON> 3"}, "masraf_merkezi4": {"type": "string", "maxLength": 25, "description": "Masraf <PERSON> 4"}}}}}}}}}, "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "error"], "description": "İşlem durumu"}, "data": {"type": "object", "description": "Başarılı işlem sonucu", "properties": {"message": {"type": "string", "description": "İşlem mesajı"}, "id": {"type": "integer", "description": "Oluşturulan fatura ID'si"}, "useRest": {"type": "boolean", "description": "REST API kullanıldı mı?"}, "ficheNo": {"type": "string", "description": "Logo fatura numarası"}}}, "error": {"type": "string", "description": "Hata mesajı (varsa)"}, "logoRef": {"type": "integer", "description": "Logo referans numarası (INTERNAL_REFERENCE)"}, "ficheNo": {"type": "string", "description": "Logo fatura numarası"}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "<PERSON>a mesajı"}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "<PERSON>a mesajı"}, "veritabani_id": {"type": "string", "description": "Veritabanı ID'si"}}}}}}}}}, "/satis-faturalari": {"post": {"tags": ["Satış Faturaları"], "summary": "Yeni satış faturası oluşturur", "security": [{"sessionAuth": []}], "description": "Logo sisteminde yeni bir satış faturası oluşturur. Fatura başlık bilgileri, kalem ve sevkiyat detayları eklenir.\n\n**Önemli Notlar**:\n\n1. Veritabanı ayarlarında `use_rest=false` olduğunda (doğrudan SQL entegrasyonu):\n   - <PERSON><PERSON><PERSON> `fatura_no=\"~\"` ise, `fatura_numarasi_formati` zorunludur ve en az bir \"_\" karakteri ile bitmelidir.\n   - <PERSON><PERSON><PERSON> `irsaliye_no=\"~\"` ise, `irsaliye_numarasi_formati` zorunludur ve en az bir \"_\" karakteri ile bitmelidir.\n   - Boş fatura veya irsaliye numarası (`\"\"`) kabul edilmez.\n\n2. Fatura ve irsaliye numarası kontrolü:\n   - <PERSON><PERSON><PERSON><PERSON> `fatura_no` de<PERSON>eri, aynı `fatura_turu` için Logo veritabanında zaten mevcutsa hata döner.\n   - Bel<PERSON><PERSON>n `irsaliye_no` de<PERSON><PERSON> Logo veritabanında zaten mevcutsa hata döner.\n   - <PERSON><PERSON>ont<PERSON>, fatura ve irsaliye numaralarının benzersiz olmasını sağlar.\n", "requestBody": {"required": true, "content": {"application/json": {"schema": {"title": "SatisFaturaRequest", "type": "object", "required": ["veritabani_id", "fatura_turu", "tarihi", "saati"], "properties": {"veritabani_id": {"type": "string", "format": "uuid", "description": "Logo veritabanı ID'si"}, "logo": {"type": "object", "description": "Logo kullanıcı bilgileri. Eğer belirtilmezse config.json'daki kullanıcı bilgileri kullanılır", "properties": {"kullanici_adi": {"type": "string", "description": "Logo kullanıcı adı"}, "sifre": {"type": "string", "description": "Logo kullanıcı şifresi"}}}, "fatura_turu": {"type": "integer", "enum": [7, 8], "description": "Fatura türü kodu (7=Perakende Satış, 8=Toptan Satış)"}, "fatura_no": {"type": "string", "maxLength": 16, "description": "Fatura numarası. <PERSON><PERSON>lı şekilde <PERSON>ullanılabilir:\n1. <PERSON><PERSON><PERSON> (örn: \"00131\") - Belirtilen fatura numarası kullanılır\n2. \"~\" - Otomatik numara oluşturulur (bu durumda fatura_numarasi_formati zorunludur)\n3. <PERSON>ş bırakılırsa - Logo tarafından otomatik atanır\n"}, "fatura_numarasi_formati": {"type": "string", "maxLength": 16, "description": "Fatura numarası formatı. fatura_no=\"~\" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:\n1. \"_\" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: \"ABC____\" -> \"ABC0001\")\n2. Format en az bir \"_\" karakteri ile bitmelidir\n3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl\n4. Örnek formatlar:\n   - \"ABC____\" -> \"ABC0001\", \"ABC0002\", ...\n   - \"INV[yyyy]___\" -> \"INV2023001\", \"INV2023002\", ... (2023 yılında)\n   - \"F[yy][mm]___\" -> \"F2305001\", \"F2305002\", ... (Mayıs 2023'te)\n   - \"FT[gg][aa]__\" -> \"FT0105__\" (1 Mayıs'ta)\n"}, "tarihi": {"type": "string", "format": "date", "description": "<PERSON><PERSON> (YYYY-MM-DD)"}, "saati": {"type": "string", "format": "time", "description": "<PERSON><PERSON> (HH:MM:SS)"}, "belge_no": {"type": "string", "maxLength": 32, "description": "Belge numarası"}, "ozel_kod": {"type": "string", "maxLength": 10, "description": "Özel kod"}, "cari_kodu": {"type": "string", "maxLength": 16, "description": "<PERSON>i hesap kodu"}, "ambar_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "<PERSON><PERSON> kodu"}, "fabrika_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "<PERSON><PERSON><PERSON> kodu"}, "aciklama": {"type": "string", "maxLength": 1800, "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "doviz_kuru": {"type": "number", "description": "Döviz kuru"}, "isyeri_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "İşyeri kodu"}, "bolum_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu"}, "satis_elemani": {"type": "string", "maxLength": 24, "description": "Satış elemanı kodu"}, "proje_kodu": {"type": "string", "maxLength": 100, "description": "<PERSON><PERSON> kodu"}, "belge_tarihi": {"type": "string", "format": "date", "description": "<PERSON>ge tarihi (YYYY-MM-DD)"}, "fatura_satirlari": {"type": "array", "minItems": 1, "maxItems": 9999, "description": "<PERSON>ura <PERSON>ı<PERSON>ı", "items": {"type": "object", "required": ["satir_turu"], "properties": {"satir_turu": {"type": "integer", "enum": [0, 2], "description": "Satır türü (0=Malzeme, 2=İskonto)"}, "malzeme_kodu": {"type": "string", "maxLength": 16, "description": "<PERSON><PERSON><PERSON> kodu"}, "ambar_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "<PERSON><PERSON> kodu"}, "fabrika_kodu": {"type": "integer", "minimum": 1, "maximum": 9999, "description": "<PERSON><PERSON><PERSON> kodu"}, "hareket_ozel_kodu": {"type": "string", "maxLength": 16, "description": "Hareket özel kodu"}, "miktar": {"type": "number", "minimum": 0, "maximum": 99999999.99, "description": "<PERSON><PERSON><PERSON>"}, "indirim_tutari": {"type": "number", "minimum": 0, "maximum": 99999999.99, "description": "İndirim tutarı"}, "birim_fiyat": {"type": "number", "minimum": 0, "maximum": 99999999.99, "description": "<PERSON><PERSON><PERSON>"}, "para_birimi": {"type": "string", "maxLength": 10, "description": "Para birimi"}, "dovizli_birim_fiyat": {"type": "number", "minimum": 0, "maximum": 99999999.99, "description": "Dövizli birim fiyat"}, "doviz_kuru": {"type": "number", "description": "Döviz kuru"}, "aciklama": {"type": "string", "maxLength": 250, "description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "indirim_orani": {"type": "number", "minimum": 0, "maximum": 100, "description": "İndirim oranı"}, "birim_kodu": {"type": "string", "maxLength": 10, "description": "<PERSON><PERSON><PERSON> kodu"}, "kdv_orani": {"type": "number", "minimum": 0, "maximum": 100, "description": "KDV oranı"}, "satis_elemani": {"type": "string", "maxLength": 24, "description": "Satış elemanı kodu"}, "proje_kodu": {"type": "string", "maxLength": 100, "description": "<PERSON><PERSON> kodu"}}}}, "irsaliye_no": {"type": "string", "maxLength": 16, "description": "İrsaliye numarası. <PERSON><PERSON> farklı şekilde kullanılabilir:\n1. <PERSON><PERSON><PERSON> (örn: \"D013345\") - Belirtilen irsaliye numarası kullanılır\n2. \"~\" - Otomatik numara oluşturulur (bu durumda irsaliye_numarasi_formati zorunludur)\n3. Boş bırakılırsa - Logo tarafından otomatik atanır\n"}, "irsaliye_numarasi_formati": {"type": "string", "maxLength": 16, "description": "İrsaliye numarası formatı. irsaliye_no=\"~\" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:\n1. \"_\" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: \"XYZ____\" -> \"XYZ0001\")\n2. Format en az bir \"_\" karakteri ile bitmelidir\n3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl\n4. <PERSON><PERSON><PERSON> formatlar:\n   - \"XYZ____\" -> \"XYZ0001\", \"XYZ0002\", ...\n   - \"DSP[yyyy]___\" -> \"DSP2023001\", \"DSP2023002\", ... (2023 yılında)\n   - \"D[yy][mm]___\" -> \"D2305001\", \"D2305002\", ... (Mayıs 2023'te)\n   - \"IR[gg][aa]__\" -> \"IR0105__\" (1 Mayıs'ta)\n"}, "irsaliye_tarihi": {"type": "string", "format": "date", "description": "İrsaliye tarihi (YYYY-MM-DD)"}, "irsaliye_saati": {"type": "string", "format": "time", "description": "İrsaliye saati (HH:MM:SS)"}}}}}}, "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "İşlem sonucu mesajı (örn. \"Satış faturası başarıyla oluşturuldu\" veya \"Satış faturası zaten mevcut\")"}, "data": {"type": "object", "properties": {"fatura_no": {"type": "string", "description": "Logo tarafından atanan fatura numarası"}, "fatura_tarihi": {"type": "string", "description": "<PERSON><PERSON> tari<PERSON> (DD.MM.YYYY formatında)"}, "logicalref": {"type": "integer", "description": "Logo'daki faturanın LOGICALREF değeri"}, "veritabani_id": {"type": "string", "description": "İşlemin yapıldığı veritabanı ID'si"}}}}}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"oneOf": [{"type": "array", "items": {"type": "string"}}, {"type": "string"}]}, "description": "Validasyon hataları"}}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "<PERSON>a mesajı"}, "veritabani_id": {"type": "string", "description": "İşlemin yapıldığı veritabanı ID'si"}}}}}}}}}, "/stoklar": {"get": {"tags": ["Stoklar"], "summary": "Logo veritabanından stok bilgilerini listeler", "security": [{"sessionAuth": []}], "parameters": [{"in": "query", "name": "veritabani_id", "required": true, "schema": {"type": "uuid"}, "description": "Logo veritabanı ID'si"}], "responses": {"200": {"description": "Başarılı", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "kodu": {"type": "string"}, "adi": {"type": "string"}, "birimler": {"type": "array", "items": {"type": "object", "properties": {"birim": {"type": "string"}, "katsayi": {"type": "string"}, "birimSira": {"type": "number"}, "barkod": {"type": "string"}, "barkodSira": {"type": "number"}}}}, "fiyatlar": {"type": "array", "items": {"type": "object", "properties": {"listeNo": {"type": "string"}, "listeAdi": {"type": "string"}, "birim": {"type": "number"}, "depoNo": {"type": "number"}, "doviz": {"type": "number"}, "dovizAdi": {"type": "string"}, "kur": {"type": "number"}, "satisFiyati": {"type": "number"}, "kdvDurumu": {"type": "boolean"}}}}}}}}}}, "400": {"description": "Geçersiz istek"}, "500": {"description": "<PERSON><PERSON><PERSON> hat<PERSON>ı"}}}}}, "tags": []}