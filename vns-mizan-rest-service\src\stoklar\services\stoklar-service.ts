import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StokFiyat } from '../models/stoklar.ts'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Get stok bilgileri and merge with related data
 */
export async function getStokBilgileri({ veritabaniId }: { veritabaniId: string }): Promise<StokBilgisi[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    // Get base stok data
    const stokQuery = `
      SELECT
        items.LOGICALREF AS id,
        items.CODE AS kodu,
        items.NAME AS adi,
        items.SPECODE AS anagrup,
        specodes1.DEFINITION_ AS anaGrupAdi,
        items.SPECODE2 AS altgrup,
        specodes2.DEFINITION_ AS altGrupAdi,
        items.SPECODE3 AS kategori,
        specodes3.DEFINITION_ AS kategoriAdi,
        items.SPECODE4 AS reyon,
        specodes4.DEFINITION_ AS reyonAdi,
        mark.CODE AS marka,
        mark.DESCR AS markaAdi,
        items.NAME2 AS kisa_adi,
        items.NAME3 AS yabanci_adi,
        items.CAPIBLOCK_CREADEDDATE AS create_date,
        items.CAPIBLOCK_MODIFIEDDATE AS update_date,
        CASE WHEN items.ACTIVE = 0 THEN 1 ELSE 0 END AS pasif,
        CASE WHEN items.NODISCOUNT = 0 THEN 1 ELSE 0 END AS isk_yapilamaz,
        items.DISTAMOUNT AS iskonto,
        CASE WHEN items.KEYWORD1 = 'KASA' THEN 1 ELSE 0 END AS fiyat_kasada_belirlenir,
        items.SELLVAT AS toptan_vergi_yuzde,
        items.SELLPRVAT AS perakende_vergi_yuzde,
        0 AS detay_takip,
        0 AS doviz,
        'TL' AS doviz_adi,
        0 AS malkabul_dursun,
        '' AS mensei,
        0 AS perakende_vergi,
        'K.D.V. (%) ' + CONVERT(VARCHAR, items.SELLPRVAT) AS perakende_vergi_adi,
        0 AS satis_dursun,
        0 AS siparis_dursun,
        0 AS terazi_skt,
        0 AS toptan_vergi,
        'K.D.V. (%) ' + CONVERT(VARCHAR, items.SELLVAT) AS toptan_vergi_adi,
        '${veritabaniId}' AS veritabani_id,
        0 AS yerli
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_ITEMS AS items
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES AS specodes1 
          ON specodes1.SPECODE = items.SPECODE 
          AND specodes1.CODETYPE = 1 
          AND specodes1.SPECODETYPE = 1
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES AS specodes2 
          ON specodes2.SPECODE = items.SPECODE2 
          AND specodes2.CODETYPE = 1 
          AND specodes2.SPECODETYPE = 2
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES AS specodes3 
          ON specodes3.SPECODE = items.SPECODE3 
          AND specodes3.CODETYPE = 1 
          AND specodes3.SPECODETYPE = 3
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES AS specodes4 
          ON specodes4.SPECODE = items.SPECODE4 
          AND specodes4.CODETYPE = 1 
          AND specodes4.SPECODETYPE = 4
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_MARK AS mark 
          ON mark.LOGICALREF = items.MARKREF
      WHERE 
        items.CARDTYPE <> 22
      ORDER BY 
        items.CODE
    `

    // Get birimler data
    const birimlerQuery = `
      SELECT
        items.LOGICALREF AS stokId,
        unitsetl.LINENR AS birimSira,
        unitsetl.CODE AS birim,
        ISNULL(CONVERT(nvarchar(MAX),unitsetl.CONVFACT1/unitsetl.CONVFACT2),0) AS katsayi,
        unitbarcode.LINENR AS barkodSira,
        unitbarcode.BARCODE AS barkod
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_ITEMS AS items
      INNER JOIN 
        LG_${logoConfig.erp.firma_numarasi}_UNITSETL AS unitsetl 
          ON unitsetl.UNITSETREF = items.UNITSETREF
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_UNITBARCODE AS unitbarcode 
          ON unitbarcode.ITEMREF = items.LOGICALREF 
          AND unitbarcode.UNITLINEREF = unitsetl.LOGICALREF
      WHERE 
        items.CARDTYPE <> 22
      ORDER BY 
        items.LOGICALREF, unitsetl.LINENR, unitbarcode.LINENR 
    `

    // Get fiyatlar data
    const fiyatlarQuery = `
      SELECT
        items.LOGICALREF AS stokId,
        specodesGrp.SPECODE AS listeNo,
        specodesGrp.DEFINITION_ AS listeAdi,
        prclist.UOMREF AS birim,
        prclist.BRANCH AS depoNo,
        prclist.CURRENCY AS doviz,
        currencylist.CURCODE AS dovizAdi,
        0.0 AS kur,
        prclist.PRICE AS satisFiyati,
        prclist.INCVAT AS kdvDurumu
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_ITEMS AS items
      INNER JOIN 
        LG_${logoConfig.erp.firma_numarasi}_PRCLIST AS prclist 
          ON prclist.CARDREF = items.LOGICALREF
      LEFT JOIN 
        ${logoConfig.erp.logodb_master}..L_CURRENCYLIST AS currencylist 
          ON currencylist.CURTYPE = prclist.CURRENCY 
          and currencylist.FIRMNR = ${logoConfig.erp.firma_numarasi}
      LEFT JOIN 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES AS specodesGrp 
          ON specodesGrp.SPECODE = prclist.GRPCODE 
          AND specodesGrp.CODETYPE = 10 
          AND specodesGrp.SPECODETYPE = 2
      WHERE 
        items.CARDTYPE <> 22
        AND prclist.PTYPE = 2
      ORDER BY 
        items.LOGICALREF, prclist.CODE
    `

    // Execute all queries in parallel
    const [stokResult, birimlerResult, fiyatlarResult] = await Promise.all([
      logoConnection.request().query<StokBilgisi>(stokQuery),
      logoConnection.request().query<StokBirim>(birimlerQuery),
      logoConnection.request().query<StokFiyat>(fiyatlarQuery),
    ])

    // Create lookup maps for birimler and fiyatlar
    const birimlerMap = new Map<number, StokBirim[]>()
    const fiyatlarMap = new Map<number, StokFiyat[]>()

    // Group birimler by stokId
    birimlerResult.recordset.forEach((birim) => {
      if (birim.stokId) {
        if (!birimlerMap.has(birim.stokId)) {
          birimlerMap.set(birim.stokId, [])
        }
        const { stokId, ...birimWithoutId } = birim
        birimlerMap.get(birim.stokId)?.push(birimWithoutId)
      }
    })

    // Group fiyatlar by stokId
    fiyatlarResult.recordset.forEach((fiyat) => {
      if (fiyat.stokId) {
        if (!fiyatlarMap.has(fiyat.stokId)) {
          fiyatlarMap.set(fiyat.stokId, [])
        }
        const { stokId, ...fiyatWithoutId } = fiyat
        fiyatlarMap.get(fiyat.stokId)?.push(fiyatWithoutId)
      }
    })

    // Map the final result
    return stokResult.recordset.map(stok => ({
      ...stok,
      birimler: birimlerMap.get(stok.id) || [],
      fiyatlar: fiyatlarMap.get(stok.id) || [],
    }))
  }
  catch (error) {
    consola.error('Stok bilgileri sorgulanırken hata:', error)
    throw error
  }
}
