import type { SatisFaturaLineItem } from '../models/sales-invoice.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo ERP STLINE table operations
 * Handles insertions into LG_{FIRMA}_{DONEM}_STLINE table with comprehensive column mapping
 * Preserves all 200+ database columns and field assignments from original logo-erp-integration.ts
 */
const LogoErpStline = {
  /**
   * Insert into actual Logo STLINE table (LG_{FFF}_{DD}_STLINE)
   * Preserves all database column mappings and SQL operations from original implementation
   */
  insertLogoActualStlines: async ({
    invoiceRef,
    stficheRef,
    lines,
    veritabaniId,
    invoiceDate,
  }: {
    invoiceRef: number
    stficheRef: number
    lines: SatisFaturaLineItem[]
    veritabaniId: string
    invoiceDate: string
  }): Promise<boolean> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      consola.info(`STLINE ekleme başlatılıyor. invoiceRef: ${invoiceRef}, satır sayısı: ${lines.length}`)

      let lineNo = 0

      for (const line of lines) {
        lineNo++

        // Get stock reference from item code
        let stockRef = null
        if (line.ITEM_CODE) {
          const stockInfo = await LogoLookupService.getStockRefFromCode(line.ITEM_CODE, veritabaniId)
          stockRef = stockInfo?.logicalref
        }

        // Get client reference from ARP_CODE if provided
        let clientRef = null
        if (line.ARP_CODE) {
          const clientInfo = await LogoLookupService.getClientRefFromCode(line.ARP_CODE, veritabaniId)
          clientRef = clientInfo?.logicalref
        }

        // Resolve SALESMANREF from salesman_code if provided
        let salesmanRef = null
        if (line.SALESMAN_CODE) {
          salesmanRef = await LogoLookupService.getSalesmanRefFromCode(line.SALESMAN_CODE, veritabaniId)
        }

        // Resolve PROJECTREF from project_code if provided
        let projectRef = null
        if (line.PROJECT_CODE) {
          projectRef = await LogoLookupService.getProjectRefFromCode(line.PROJECT_CODE, veritabaniId)
        }

        // Get unit reference from unit code
        let uomRef = null
        if (line.UNIT_CODE) {
          uomRef = await LogoLookupService.getUnitRefFromCode(line.UNIT_CODE, veritabaniId)
        }

        // Calculate line totals
        const amount = line.QUANTITY || 0
        const price = line.PRICE || 0
        const total = amount * price
        const vatRate = line.VAT_RATE || 0
        const vatAmount = (total * vatRate) / 100
        const lineNet = total + vatAmount

        consola.info(`STLINE satırı hazırlanıyor. Satır: ${lineNo}, Malzeme: ${line.ITEM_CODE}, Miktar: ${amount}`)

        // Insert the line with comprehensive field mapping based on JSON structure
        const request = logoConnection.request()
        await request
          .input('STOCKREF', sql.Int, stockRef || 0)
          .input('LINETYPE', sql.SmallInt, 0) // 0 based on example
          .input('PREVLINEREF', sql.Int, 0)
          .input('PREVLINENO', sql.SmallInt, 0)
          .input('DETLINE', sql.SmallInt, 0)
          .input('TRCODE', sql.SmallInt, 7) // 7 for sales invoice
          .input('DATE_', sql.DateTime, new Date(invoiceDate))
          .input('FTIME', sql.Int, 185999616) // Based on example
          .input('GLOBTRANS', sql.SmallInt, 0)
          .input('CALCTYPE', sql.SmallInt, 0)
          .input('PRODORDERREF', sql.Int, 0)
          .input('SOURCETYPE', sql.SmallInt, 0)
          .input('SOURCEINDEX', sql.SmallInt, line.SOURCEINDEX || 0)
          .input('SOURCECOSTGRP', sql.SmallInt, 0)
          .input('SOURCEWSREF', sql.Int, 0)
          .input('SOURCEPOLNREF', sql.Int, 0)
          .input('DESTTYPE', sql.SmallInt, 0)
          .input('DESTINDEX', sql.SmallInt, 0)
          .input('DESTCOSTGRP', sql.SmallInt, 0)
          .input('DESTWSREF', sql.Int, 0)
          .input('DESTPOLNREF', sql.Int, 0)
          .input('FACTORYNR', sql.SmallInt, line.FACTORY || 0)
          .input('IOCODE', sql.SmallInt, 3) // 3 for output
          .input('STFICHEREF', sql.Int, stficheRef)
          .input('STFICHELNNO', sql.SmallInt, lineNo)
          .input('INVOICEREF', sql.Int, invoiceRef)
          .input('INVOICELNNO', sql.SmallInt, lineNo)
          .input('CLIENTREF', sql.Int, clientRef || 0)
          .input('ORDTRANSREF', sql.Int, 0)
          .input('ORDFICHEREF', sql.Int, 0)
          .input('CENTERREF', sql.Int, 0)
          .input('ACCOUNTREF', sql.Int, 0)
          .input('VATACCREF', sql.Int, 0)
          .input('VATCENTERREF', sql.Int, 0)
          .input('PRACCREF', sql.Int, 0)
          .input('PRCENTERREF', sql.Int, 0)
          .input('PRVATACCREF', sql.Int, 0)
          .input('PRVATCENREF', sql.Int, 0)
          .input('PROMREF', sql.Int, 0)
          .input('PAYDEFREF', sql.Int, 0)
          .input('SPECODE', sql.VarChar(11), line.AUXIL_CODE || '')
          .input('DELVRYCODE', sql.VarChar(11), '')
          .input('AMOUNT', sql.Float, amount)
          .input('PRICE', sql.Float, price)
          .input('TOTAL', sql.Float, total)
          .input('PRCURR', sql.SmallInt, 0)
          .input('PRPRICE', sql.Float, 0)
          .input('TRCURR', sql.SmallInt, line.CURR_TRANSACTION || 0)
          .input('TRRATE', sql.Float, 1)
          .input('REPORTRATE', sql.Float, 1)
          .input('DISTCOST', sql.Float, 0)
          .input('DISTDISC', sql.Float, 0)
          .input('DISTEXP', sql.Float, 0)
          .input('DISTPROM', sql.Float, 0)
          .input('DISCPER', sql.Float, line.DISCOUNT_RATE || 0)
          .input('LINEEXP', sql.Float, 0)
          .input('UOMREF', sql.Int, uomRef || 0)
          .input('USREF', sql.Int, 0)
          .input('UINFO1', sql.Float, 0)
          .input('UINFO2', sql.Float, 0)
          .input('UINFO3', sql.Float, 0)
          .input('UINFO4', sql.Float, 0)
          .input('UINFO5', sql.Float, 0)
          .input('UINFO6', sql.Float, 0)
          .input('UINFO7', sql.Float, 0)
          .input('UINFO8', sql.Float, 0)
          .input('PLNAMOUNT', sql.Float, 0)
          .input('VATINC', sql.SmallInt, 0)
          .input('VAT', sql.Float, vatRate)
          .input('VATAMNT', sql.Float, vatAmount)
          .input('VATMATRAH', sql.Float, total)
          .input('BILLEDITEM', sql.SmallInt, 1)
          .input('BILLED', sql.SmallInt, 1)
          .input('CPSTFLAG', sql.SmallInt, 0)
          .input('RETCOSTTYPE', sql.SmallInt, 0)
          .input('SOURCELINK', sql.SmallInt, 0)
          .input('RETCOST', sql.Float, 0)
          .input('RETCOSTCURR', sql.SmallInt, 0)
          .input('OUTCOST', sql.Float, 0)
          .input('OUTCOSTCURR', sql.SmallInt, 0)
          .input('RETAMOUNT', sql.Float, 0)
          .input('FAREGREF', sql.Int, 0)
          .input('FAATTRIB', sql.SmallInt, 0)
          .input('CANCELLED', sql.SmallInt, 0)
          .input('LINENET', sql.Float, lineNet)
          .input('DISTADDEXP', sql.Float, 0)
          .input('FADACCREF', sql.Int, 0)
          .input('FADCENTERREF', sql.Int, 0)
          .input('FARACCREF', sql.Int, 0)
          .input('FARCENTERREF', sql.Int, 0)
          .input('DIFFPRICE', sql.Float, 0)
          .input('DIFFPRCOST', sql.Float, 0)
          .input('DECPRDIFF', sql.Float, 0)
          .input('LPRODSTAT', sql.SmallInt, 0)
          .input('PRDEXPTOTAL', sql.Float, 0)
          .input('DIFFREPPRICE', sql.Float, 0)
          .input('DIFFPRCRCOST', sql.Float, 0)
          .input('SALESMANREF', sql.Int, salesmanRef || 0)
          .input('FAPLACCREF', sql.Int, 0)
          .input('FAPLCENTERREF', sql.Int, 0)
          .input('OUTPUTIDCODE', sql.VarChar(25), '')
          .input('DREF', sql.Int, 0)
          .input('COSTRATE', sql.Float, 0)
          .input('XPRICEUPD', sql.SmallInt, 0)
          .input('XPRICE', sql.Float, 0)
          .input('XREPRATE', sql.Float, 0)
          .input('DISTCOEF', sql.Float, 0)
          .input('TRANSQCOK', sql.SmallInt, 0)
          .input('SITEID', sql.SmallInt, 0)
          .input('RECSTATUS', sql.SmallInt, 0)
          .input('ORGLOGICREF', sql.Int, 0)
          .input('WFSTATUS', sql.SmallInt, 0)
          .input('POLINEREF', sql.Int, 0)
          .input('PLNSTTRANSREF', sql.Int, 0)
          .input('NETDISCFLAG', sql.SmallInt, 0)
          .input('NETDISCPERC', sql.Float, 0)
          .input('NETDISCAMNT', sql.Float, 0)
          .input('VATCALCDIFF', sql.Float, 0)
          .input('CONDITIONREF', sql.Int, 0)
          .input('DISTORDERREF', sql.Int, 0)
          .input('DISTORDLINEREF', sql.Int, 0)
          .input('CAMPAIGNREFS1', sql.Int, 0)
          .input('CAMPAIGNREFS2', sql.Int, 0)
          .input('CAMPAIGNREFS3', sql.Int, 0)
          .input('CAMPAIGNREFS4', sql.Int, 0)
          .input('CAMPAIGNREFS5', sql.Int, 0)
          .input('POINTCAMPREF', sql.Int, 0)
          .input('CAMPPOINT', sql.Float, 0)
          .input('PROMCLASITEMREF', sql.Int, 0)
          .input('CMPGLINEREF', sql.Int, 0)
          .input('PLNSTTRANSPERNR', sql.SmallInt, 0)
          .input('PORDCLSPLNAMNT', sql.Float, 0)
          .input('VENDCOMM', sql.Float, 0)
          .input('PREVIOUSOUTCOST', sql.Float, 0)
          .input('COSTOFSALEACCREF', sql.Int, 0)
          .input('PURCHACCREF', sql.Int, 0)
          .input('COSTOFSALECNTREF', sql.Int, 0)
          .input('PURCHCENTREF', sql.Int, 0)
          .input('PREVOUTCOSTCURR', sql.SmallInt, 0)
          .input('ABVATAMOUNT', sql.Float, 0)
          .input('ABVATSTATUS', sql.SmallInt, 0)
          .input('PRRATE', sql.Float, 0)
          .input('ADDTAXRATE', sql.Float, 0)
          .input('ADDTAXCONVFACT', sql.Float, 0)
          .input('ADDTAXAMOUNT', sql.Float, 0)
          .input('ADDTAXPRCOST', sql.Float, 0)
          .input('ADDTAXRETCOST', sql.Float, 0)
          .input('ADDTAXRETCOSTCURR', sql.SmallInt, 0)
          .input('GROSSUINFO1', sql.Float, 0)
          .input('GROSSUINFO2', sql.Float, 0)
          .input('ADDTAXPRCOSTCURR', sql.SmallInt, 0)
          .input('ADDTAXACCREF', sql.Int, 0)
          .input('ADDTAXCENTERREF', sql.Int, 0)
          .input('ADDTAXAMNTISUPD', sql.SmallInt, 0)
          .input('INFIDX', sql.SmallInt, 0)
          .input('ADDTAXCOSACCREF', sql.Int, 0)
          .input('ADDTAXCOSCNTREF', sql.Int, 0)
          .input('PREVIOUSATAXPRCOST', sql.Float, 0)
          .input('PREVATAXPRCOSTCURR', sql.SmallInt, 0)
          .input('PRDORDTOTCOEF', sql.Float, 0)
          .input('DEMPEGGEDAMNT', sql.Float, 0)
          .input('STDUNITCOST', sql.Float, 0)
          .input('STDRPUNITCOST', sql.Float, 0)
          .input('COSTDIFFACCREF', sql.Int, 0)
          .input('COSTDIFFCENREF', sql.Int, 0)
          .input('TEXTINC', sql.SmallInt, 0)
          .input('ADDTAXDISCAMOUNT', sql.Float, 0)
          .input('ORGLOGOID', sql.VarChar(25), '')
          .input('EXIMFICHENO', sql.VarChar(13), '')
          .input('EXIMFCTYPE', sql.SmallInt, 0)
          .input('TRANSEXPLINE', sql.SmallInt, 0)
          .input('INSEXPLINE', sql.SmallInt, 0)
          .input('EXIMWHFCREF', sql.Int, 0)
          .input('EXIMWHLNREF', sql.Int, 0)
          .input('EXIMFILEREF', sql.Int, 0)
          .input('EXIMPROCNR', sql.SmallInt, 0)
          .input('EISRVDSTTYP', sql.SmallInt, 0)
          .input('MAINSTLNREF', sql.Int, 0)
          .input('MADEOFSHRED', sql.SmallInt, 0)
          .input('FROMORDWITHPAY', sql.SmallInt, 0)
          .input('PROJECTREF', sql.Int, projectRef || 0)
          .input('STATUS', sql.SmallInt, 0)
          .input('DORESERVE', sql.SmallInt, 0)
          .input('POINTCAMPREFS1', sql.Int, 0)
          .input('POINTCAMPREFS2', sql.Int, 0)
          .input('POINTCAMPREFS3', sql.Int, 0)
          .input('POINTCAMPREFS4', sql.Int, 0)
          .input('CAMPPOINTS1', sql.Float, 0)
          .input('CAMPPOINTS2', sql.Float, 0)
          .input('CAMPPOINTS3', sql.Float, 0)
          .input('CAMPPOINTS4', sql.Float, 0)
          .input('CMPGLINEREFS1', sql.Int, 0)
          .input('CMPGLINEREFS2', sql.Int, 0)
          .input('CMPGLINEREFS3', sql.Int, 0)
          .input('CMPGLINEREFS4', sql.Int, 0)
          .input('PRCLISTREF', sql.Int, 0)
          .input('PORDSYMOUTLN', sql.SmallInt, 0)
          .input('MONTH_', sql.SmallInt, new Date(invoiceDate).getMonth() + 1)
          .input('YEAR_', sql.SmallInt, new Date(invoiceDate).getFullYear())
          .input('EXADDTAXRATE', sql.Float, 0)
          .input('EXADDTAXCONVF', sql.Float, 0)
          .input('EXADDTAXAREF', sql.Int, 0)
          .input('EXADDTAXCREF', sql.Int, 0)
          .input('OTHRADDTAXAREF', sql.Int, 0)
          .input('OTHRADDTAXCREF', sql.Int, 0)
          .input('EXADDTAXAMNT', sql.Float, 0)
          .input('AFFECTCOLLATRL', sql.SmallInt, 0)
          .input('ALTPROMFLAG', sql.SmallInt, 0)
          .input('EIDISTFLNNR', sql.SmallInt, 0)
          .input('EXIMTYPE', sql.SmallInt, 0)
          .input('VARIANTREF', sql.Int, 0)
          .input('CANDEDUCT', sql.SmallInt, 0)
          .input('OUTREMAMNT', sql.Float, 0)
          .input('OUTREMCOST', sql.Float, 0)
          .input('OUTREMCOSTCURR', sql.SmallInt, 0)
          .input('REFLVATACCREF', sql.Int, 0)
          .input('REFLVATOTHACCREF', sql.Int, 0)
          .input('PARENTLNREF', sql.Int, 0)
          .input('AFFECTRISK', sql.SmallInt, 0)
          .input('INEFFECTIVECOST', sql.SmallInt, 0)
          .input('ADDTAXVATMATRAH', sql.Float, 0)
          .input('REFLACCREF', sql.Int, 0)
          .input('REFLOTHACCREF', sql.Int, 0)
          .input('CAMPPAYDEFREF', sql.Int, 0)
          .input('RELTRANSLNREF', sql.Int, 0)
          .input('FROMTRANSFER', sql.SmallInt, 0)
          .input('COSTDISTPRICE', sql.Float, 0)
          .input('COSTDISTREPPRICE', sql.Float, 0)
          .input('DIFFPRICEUFRS', sql.Float, 0)
          .input('DIFFREPPRICEUFRS', sql.Float, 0)
          .input('OUTCOSTUFRS', sql.Float, 0)
          .input('OUTCOSTCURRUFRS', sql.SmallInt, 0)
          .input('DIFFPRCOSTUFRS', sql.Float, 0)
          .input('DIFFPRCRCOSTUFRS', sql.Float, 0)
          .input('RETCOSTUFRS', sql.Float, 0)
          .input('RETCOSTCURRUFRS', sql.SmallInt, 0)
          .input('OUTREMCOSTUFRS', sql.Float, 0)
          .input('OUTREMCOSTCURRUFRS', sql.SmallInt, 0)
          .input('INFIDXUFRS', sql.SmallInt, 0)
          .input('ADJPRICEUFRS', sql.Float, 0)
          .input('ADJREPPRICEUFRS', sql.Float, 0)
          .input('ADJPRCOSTUFRS', sql.Float, 0)
          .input('ADJPRCRCOSTUFRS', sql.Float, 0)
          .input('COSTDISTPRICEUFRS', sql.Float, 0)
          .input('COSTDISTREPPRICEUFRS', sql.Float, 0)
          .input('PURCHACCREFUFRS', sql.Int, 0)
          .input('PURCHCENTREFUFRS', sql.Int, 0)
          .input('COSACCREFUFRS', sql.Int, 0)
          .input('COSCNTREFUFRS', sql.Int, 0)
          .input('PROUTCOSTUFRSDIFF', sql.Float, 0)
          .input('PROUTCOSTCRUFRSDIFF', sql.Float, 0)
          .input('UNDERDEDUCTLIMIT', sql.SmallInt, 0)
          .input('GLOBALID', sql.VarChar(51), '')
          .input('DEDUCTIONPART1', sql.SmallInt, 0)
          .input('DEDUCTIONPART2', sql.SmallInt, 0)
          .input('GUID', sql.VarChar(37), randomUUID())
          .input('SPECODE2', sql.VarChar(11), '')
          .input('OFFERREF', sql.Int, 0)
          .input('OFFTRANSREF', sql.Int, 0)
          .input('VATEXCEPTREASON', sql.VarChar(51), '')
          .input('PLNDEFSERILOTNO', sql.VarChar(51), '')
          .input('PLNUNRSRVAMOUNT', sql.Float, 0)
          .input('PORDCLSPLNUNRSRVAMNT', sql.Float, 0)
          .input('LPRODRSRVSTAT', sql.SmallInt, 0)
          .input('FALINKTYPE', sql.SmallInt, 0)
          .input('DEDUCTCODE', sql.VarChar(51), '')
          .input('UPDTHISLINE', sql.SmallInt, 0)
          .input('VATEXCEPTCODE', sql.VarChar(51), '')
          .input('PORDERFICHENO', sql.VarChar(17), '')
          .input('QPRODFCREF', sql.Int, 0)
          .input('RELTRANSFCREF', sql.Int, 0)
          .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
          .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
          .input('PRODORDERTYP', sql.SmallInt, 0)
          .input('SUBCONTORDERREF', sql.Int, 0)
          .input('QPRODFCTYP', sql.SmallInt, 0)
          .input('PRDORDSLPLNRESERVE', sql.SmallInt, 0)
          .input('DESTSTATUS', sql.SmallInt, 0)
          .input('REGTYPREF', sql.Int, 0)
          .input('FAPROFITACCREF', sql.Int, 0)
          .input('FAPROFITCENTREF', sql.Int, 0)
          .input('FALOSSACCREF', sql.Int, 0)
          .input('FALOSSCENTREF', sql.Int, 0)
          .input('CPACODE', sql.VarChar(51), '')
          .input('GTIPCODE', sql.VarChar(51), '')
          .input('PUBLICCOUNTRYREF', sql.Int, 0)
          .input('QPRODITEMTYPE', sql.SmallInt, 0)
          .input('FUTMONTHCNT', sql.SmallInt, 0)
          .input('FUTMONTHBEGDATE', sql.DateTime, null)
          .input('QCTRANSFERREF', sql.Int, 0)
          .input('QCTRANSFERAMNT', sql.Float, 0)
          .input('KKEGACCREF', sql.Int, 0)
          .input('KKEGCENTREF', sql.Int, 0)
          .input('MNTORDERFREF', sql.Int, 0)
          .input('FAKKEGAMOUNT', sql.Float, 0)
          .input('MIDDLEMANEXPTYP', sql.SmallInt, 0)
          .input('EXPRACCREF', sql.Int, 0)
          .input('EXPRCNTRREF', sql.Int, 0)
          .input('KKEGVATACCREF', sql.Int, 0)
          .input('KKEGVATCENTREF', sql.Int, 0)
          .input('MARKINGTAGNO', sql.VarChar(51), '')
          .input('OWNER', sql.SmallInt, 0)
          .input('TCKTAXNR', sql.VarChar(51), '')
          .input('ADDTAXVATACCREF', sql.Int, 0)
          .input('ADDTAXVATCENREF', sql.Int, 0)
          .input('EXPDAYS', sql.SmallInt, 0)
          .input('CANCELLEDINVREF1', sql.Int, 0)
          .input('CANCELLEDINVREF2', sql.Int, 0)
          .input('CANCELLEDINVREF3', sql.Int, 0)
          .input('CANCELLEDINVREF4', sql.Int, 0)
          .input('FROMINTEGTYPE', sql.SmallInt, 0)
          .input('FROMINTEGREF', sql.Int, 0)
          .input('QCTRANSFERREF2', sql.Int, 0)
          .input('QCTRANSFERAMNT2', sql.Float, 0)
          .input('EISRVDSTADDTAXINC', sql.SmallInt, 0)
          .input('TAXFREEACCREF', sql.Int, 0)
          .input('TAXFREECNTRREF', sql.Int, 0)
          .input('ADDTAXEFFECTKDV', sql.SmallInt, 0)
          .input('ADDTAXINLINENET', sql.SmallInt, 0)
          .input('ITMDISC', sql.Float, 0)
          .input('ADDTAXREF', sql.Int, 0)
          .input('COSCNTREFINFL', sql.Int, 0)
          .input('PROUTCOSTINFLDIFF', sql.Float, 0)
          .input('PROUTCOSTCRINFLDIFF', sql.Float, 0)
          .input('COSACCREFINFL', sql.Int, 0)
          .input('ORDFICHECMREF', sql.Int, 0)
          .input('PURCHACCREFINFL', sql.Int, 0)
          .input('PURCHCENTREFINFL', sql.Int, 0)
          .input('DIIBLINECODE', sql.VarChar(51), '')
          .input('RETSOURCELINK', sql.SmallInt, 0)
          .input('ORGPRICE', sql.Float, 0)
          .query(`
            INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STLINE (
              STOCKREF, LINETYPE, PREVLINEREF, PREVLINENO, DETLINE, TRCODE, DATE_, FTIME,
              GLOBTRANS, CALCTYPE, PRODORDERREF, SOURCETYPE, SOURCEINDEX, SOURCECOSTGRP,
              SOURCEWSREF, SOURCEPOLNREF, DESTTYPE, DESTINDEX, DESTCOSTGRP, DESTWSREF,
              DESTPOLNREF, FACTORYNR, IOCODE, STFICHEREF, STFICHELNNO, INVOICEREF,
              INVOICELNNO, CLIENTREF, ORDTRANSREF, ORDFICHEREF, CENTERREF, ACCOUNTREF,
              VATACCREF, VATCENTERREF, PRACCREF, PRCENTERREF, PRVATACCREF, PRVATCENREF,
              PROMREF, PAYDEFREF, SPECODE, DELVRYCODE, AMOUNT, PRICE, TOTAL, PRCURR,
              PRPRICE, TRCURR, TRRATE, REPORTRATE, DISTCOST, DISTDISC, DISTEXP, DISTPROM,
              DISCPER, LINEEXP, UOMREF, USREF, UINFO1, UINFO2, UINFO3, UINFO4, UINFO5,
              UINFO6, UINFO7, UINFO8, PLNAMOUNT, VATINC, VAT, VATAMNT, VATMATRAH,
              BILLEDITEM, BILLED, CPSTFLAG, RETCOSTTYPE, SOURCELINK, RETCOST, RETCOSTCURR,
              OUTCOST, OUTCOSTCURR, RETAMOUNT, FAREGREF, FAATTRIB, CANCELLED, LINENET,
              DISTADDEXP, FADACCREF, FADCENTERREF, FARACCREF, FARCENTERREF, DIFFPRICE,
              DIFFPRCOST, DECPRDIFF, LPRODSTAT, PRDEXPTOTAL, DIFFREPPRICE, DIFFPRCRCOST,
              SALESMANREF, FAPLACCREF, FAPLCENTERREF, OUTPUTIDCODE, DREF, COSTRATE,
              XPRICEUPD, XPRICE, XREPRATE, DISTCOEF, TRANSQCOK, SITEID, RECSTATUS,
              ORGLOGICREF, WFSTATUS, POLINEREF, PLNSTTRANSREF, NETDISCFLAG, NETDISCPERC,
              NETDISCAMNT, VATCALCDIFF, CONDITIONREF, DISTORDERREF, DISTORDLINEREF,
              CAMPAIGNREFS1, CAMPAIGNREFS2, CAMPAIGNREFS3, CAMPAIGNREFS4, CAMPAIGNREFS5,
              POINTCAMPREF, CAMPPOINT, PROMCLASITEMREF, CMPGLINEREF, PLNSTTRANSPERNR,
              PORDCLSPLNAMNT, VENDCOMM, PREVIOUSOUTCOST, COSTOFSALEACCREF, PURCHACCREF,
              COSTOFSALECNTREF, PURCHCENTREF, PREVOUTCOSTCURR, ABVATAMOUNT, ABVATSTATUS,
              PRRATE, ADDTAXRATE, ADDTAXCONVFACT, ADDTAXAMOUNT, ADDTAXPRCOST, ADDTAXRETCOST,
              ADDTAXRETCOSTCURR, GROSSUINFO1, GROSSUINFO2, ADDTAXPRCOSTCURR, ADDTAXACCREF,
              ADDTAXCENTERREF, ADDTAXAMNTISUPD, INFIDX, ADDTAXCOSACCREF, ADDTAXCOSCNTREF,
              PREVIOUSATAXPRCOST, PREVATAXPRCOSTCURR, PRDORDTOTCOEF, DEMPEGGEDAMNT,
              STDUNITCOST, STDRPUNITCOST, COSTDIFFACCREF, COSTDIFFCENREF, TEXTINC,
              ADDTAXDISCAMOUNT, ORGLOGOID, EXIMFICHENO, EXIMFCTYPE, TRANSEXPLINE,
              INSEXPLINE, EXIMWHFCREF, EXIMWHLNREF, EXIMFILEREF, EXIMPROCNR, EISRVDSTTYP,
              MAINSTLNREF, MADEOFSHRED, FROMORDWITHPAY, PROJECTREF, STATUS, DORESERVE,
              POINTCAMPREFS1, POINTCAMPREFS2, POINTCAMPREFS3, POINTCAMPREFS4, CAMPPOINTS1,
              CAMPPOINTS2, CAMPPOINTS3, CAMPPOINTS4, CMPGLINEREFS1, CMPGLINEREFS2,
              CMPGLINEREFS3, CMPGLINEREFS4, PRCLISTREF, PORDSYMOUTLN, MONTH_, YEAR_,
              EXADDTAXRATE, EXADDTAXCONVF, EXADDTAXAREF, EXADDTAXCREF, OTHRADDTAXAREF,
              OTHRADDTAXCREF, EXADDTAXAMNT, AFFECTCOLLATRL, ALTPROMFLAG, EIDISTFLNNR,
              EXIMTYPE, VARIANTREF, CANDEDUCT, OUTREMAMNT, OUTREMCOST, OUTREMCOSTCURR,
              REFLVATACCREF, REFLVATOTHACCREF, PARENTLNREF, AFFECTRISK, INEFFECTIVECOST,
              ADDTAXVATMATRAH, REFLACCREF, REFLOTHACCREF, CAMPPAYDEFREF, RELTRANSLNREF,
              FROMTRANSFER, COSTDISTPRICE, COSTDISTREPPRICE, DIFFPRICEUFRS, DIFFREPPRICEUFRS,
              OUTCOSTUFRS, OUTCOSTCURRUFRS, DIFFPRCOSTUFRS, DIFFPRCRCOSTUFRS, RETCOSTUFRS,
              RETCOSTCURRUFRS, OUTREMCOSTUFRS, OUTREMCOSTCURRUFRS, INFIDXUFRS, ADJPRICEUFRS,
              ADJREPPRICEUFRS, ADJPRCOSTUFRS, ADJPRCRCOSTUFRS, COSTDISTPRICEUFRS,
              COSTDISTREPPRICEUFRS, PURCHACCREFUFRS, PURCHCENTREFUFRS, COSACCREFUFRS,
              COSCNTREFUFRS, PROUTCOSTUFRSDIFF, PROUTCOSTCRUFRSDIFF, UNDERDEDUCTLIMIT,
              GLOBALID, DEDUCTIONPART1, DEDUCTIONPART2, GUID, SPECODE2, OFFERREF,
              OFFTRANSREF, VATEXCEPTREASON, PLNDEFSERILOTNO, PLNUNRSRVAMOUNT,
              PORDCLSPLNUNRSRVAMNT, LPRODRSRVSTAT, FALINKTYPE, DEDUCTCODE, UPDTHISLINE,
              VATEXCEPTCODE, PORDERFICHENO, QPRODFCREF, RELTRANSFCREF, ATAXEXCEPTREASON,
              ATAXEXCEPTCODE, PRODORDERTYP, SUBCONTORDERREF, QPRODFCTYP, PRDORDSLPLNRESERVE,
              DESTSTATUS, REGTYPREF, FAPROFITACCREF, FAPROFITCENTREF, FALOSSACCREF,
              FALOSSCENTREF, CPACODE, GTIPCODE, PUBLICCOUNTRYREF, QPRODITEMTYPE,
              FUTMONTHCNT, FUTMONTHBEGDATE, QCTRANSFERREF, QCTRANSFERAMNT, KKEGACCREF,
              KKEGCENTREF, MNTORDERFREF, FAKKEGAMOUNT, MIDDLEMANEXPTYP, EXPRACCREF,
              EXPRCNTRREF, KKEGVATACCREF, KKEGVATCENTREF, MARKINGTAGNO, OWNER, TCKTAXNR,
              ADDTAXVATACCREF, ADDTAXVATCENREF, EXPDAYS, CANCELLEDINVREF1, CANCELLEDINVREF2,
              CANCELLEDINVREF3, CANCELLEDINVREF4, FROMINTEGTYPE, FROMINTEGREF,
              QCTRANSFERREF2, QCTRANSFERAMNT2, EISRVDSTADDTAXINC, TAXFREEACCREF,
              TAXFREECNTRREF, ADDTAXEFFECTKDV, ADDTAXINLINENET, ITMDISC, ADDTAXREF,
              COSCNTREFINFL, PROUTCOSTINFLDIFF, PROUTCOSTCRINFLDIFF, COSACCREFINFL,
              ORDFICHECMREF, PURCHACCREFINFL, PURCHCENTREFINFL, DIIBLINECODE,
              RETSOURCELINK, ORGPRICE
            )
            VALUES (
              @STOCKREF, @LINETYPE, @PREVLINEREF, @PREVLINENO, @DETLINE, @TRCODE, @DATE_, @FTIME,
              @GLOBTRANS, @CALCTYPE, @PRODORDERREF, @SOURCETYPE, @SOURCEINDEX, @SOURCECOSTGRP,
              @SOURCEWSREF, @SOURCEPOLNREF, @DESTTYPE, @DESTINDEX, @DESTCOSTGRP, @DESTWSREF,
              @DESTPOLNREF, @FACTORYNR, @IOCODE, @STFICHEREF, @STFICHELNNO, @INVOICEREF,
              @INVOICELNNO, @CLIENTREF, @ORDTRANSREF, @ORDFICHEREF, @CENTERREF, @ACCOUNTREF,
              @VATACCREF, @VATCENTERREF, @PRACCREF, @PRCENTERREF, @PRVATACCREF, @PRVATCENREF,
              @PROMREF, @PAYDEFREF, @SPECODE, @DELVRYCODE, @AMOUNT, @PRICE, @TOTAL, @PRCURR,
              @PRPRICE, @TRCURR, @TRRATE, @REPORTRATE, @DISTCOST, @DISTDISC, @DISTEXP, @DISTPROM,
              @DISCPER, @LINEEXP, @UOMREF, @USREF, @UINFO1, @UINFO2, @UINFO3, @UINFO4, @UINFO5,
              @UINFO6, @UINFO7, @UINFO8, @PLNAMOUNT, @VATINC, @VAT, @VATAMNT, @VATMATRAH,
              @BILLEDITEM, @BILLED, @CPSTFLAG, @RETCOSTTYPE, @SOURCELINK, @RETCOST, @RETCOSTCURR,
              @OUTCOST, @OUTCOSTCURR, @RETAMOUNT, @FAREGREF, @FAATTRIB, @CANCELLED, @LINENET,
              @DISTADDEXP, @FADACCREF, @FADCENTERREF, @FARACCREF, @FARCENTERREF, @DIFFPRICE,
              @DIFFPRCOST, @DECPRDIFF, @LPRODSTAT, @PRDEXPTOTAL, @DIFFREPPRICE, @DIFFPRCRCOST,
              @SALESMANREF, @FAPLACCREF, @FAPLCENTERREF, @OUTPUTIDCODE, @DREF, @COSTRATE,
              @XPRICEUPD, @XPRICE, @XREPRATE, @DISTCOEF, @TRANSQCOK, @SITEID, @RECSTATUS,
              @ORGLOGICREF, @WFSTATUS, @POLINEREF, @PLNSTTRANSREF, @NETDISCFLAG, @NETDISCPERC,
              @NETDISCAMNT, @VATCALCDIFF, @CONDITIONREF, @DISTORDERREF, @DISTORDLINEREF,
              @CAMPAIGNREFS1, @CAMPAIGNREFS2, @CAMPAIGNREFS3, @CAMPAIGNREFS4, @CAMPAIGNREFS5,
              @POINTCAMPREF, @CAMPPOINT, @PROMCLASITEMREF, @CMPGLINEREF, @PLNSTTRANSPERNR,
              @PORDCLSPLNAMNT, @VENDCOMM, @PREVIOUSOUTCOST, @COSTOFSALEACCREF, @PURCHACCREF,
              @COSTOFSALECNTREF, @PURCHCENTREF, @PREVOUTCOSTCURR, @ABVATAMOUNT, @ABVATSTATUS,
              @PRRATE, @ADDTAXRATE, @ADDTAXCONVFACT, @ADDTAXAMOUNT, @ADDTAXPRCOST, @ADDTAXRETCOST,
              @ADDTAXRETCOSTCURR, @GROSSUINFO1, @GROSSUINFO2, @ADDTAXPRCOSTCURR, @ADDTAXACCREF,
              @ADDTAXCENTERREF, @ADDTAXAMNTISUPD, @INFIDX, @ADDTAXCOSACCREF, @ADDTAXCOSCNTREF,
              @PREVIOUSATAXPRCOST, @PREVATAXPRCOSTCURR, @PRDORDTOTCOEF, @DEMPEGGEDAMNT,
              @STDUNITCOST, @STDRPUNITCOST, @COSTDIFFACCREF, @COSTDIFFCENREF, @TEXTINC,
              @ADDTAXDISCAMOUNT, @ORGLOGOID, @EXIMFICHENO, @EXIMFCTYPE, @TRANSEXPLINE,
              @INSEXPLINE, @EXIMWHFCREF, @EXIMWHLNREF, @EXIMFILEREF, @EXIMPROCNR, @EISRVDSTTYP,
              @MAINSTLNREF, @MADEOFSHRED, @FROMORDWITHPAY, @PROJECTREF, @STATUS, @DORESERVE,
              @POINTCAMPREFS1, @POINTCAMPREFS2, @POINTCAMPREFS3, @POINTCAMPREFS4, @CAMPPOINTS1,
              @CAMPPOINTS2, @CAMPPOINTS3, @CAMPPOINTS4, @CMPGLINEREFS1, @CMPGLINEREFS2,
              @CMPGLINEREFS3, @CMPGLINEREFS4, @PRCLISTREF, @PORDSYMOUTLN, @MONTH_, @YEAR_,
              @EXADDTAXRATE, @EXADDTAXCONVF, @EXADDTAXAREF, @EXADDTAXCREF, @OTHRADDTAXAREF,
              @OTHRADDTAXCREF, @EXADDTAXAMNT, @AFFECTCOLLATRL, @ALTPROMFLAG, @EIDISTFLNNR,
              @EXIMTYPE, @VARIANTREF, @CANDEDUCT, @OUTREMAMNT, @OUTREMCOST, @OUTREMCOSTCURR,
              @REFLVATACCREF, @REFLVATOTHACCREF, @PARENTLNREF, @AFFECTRISK, @INEFFECTIVECOST,
              @ADDTAXVATMATRAH, @REFLACCREF, @REFLOTHACCREF, @CAMPPAYDEFREF, @RELTRANSLNREF,
              @FROMTRANSFER, @COSTDISTPRICE, @COSTDISTREPPRICE, @DIFFPRICEUFRS, @DIFFREPPRICEUFRS,
              @OUTCOSTUFRS, @OUTCOSTCURRUFRS, @DIFFPRCOSTUFRS, @DIFFPRCRCOSTUFRS, @RETCOSTUFRS,
              @RETCOSTCURRUFRS, @OUTREMCOSTUFRS, @OUTREMCOSTCURRUFRS, @INFIDXUFRS, @ADJPRICEUFRS,
              @ADJREPPRICEUFRS, @ADJPRCOSTUFRS, @ADJPRCRCOSTUFRS, @COSTDISTPRICEUFRS,
              @COSTDISTREPPRICEUFRS, @PURCHACCREFUFRS, @PURCHCENTREFUFRS, @COSACCREFUFRS,
              @COSCNTREFUFRS, @PROUTCOSTUFRSDIFF, @PROUTCOSTCRUFRSDIFF, @UNDERDEDUCTLIMIT,
              @GLOBALID, @DEDUCTIONPART1, @DEDUCTIONPART2, @GUID, @SPECODE2, @OFFERREF,
              @OFFTRANSREF, @VATEXCEPTREASON, @PLNDEFSERILOTNO, @PLNUNRSRVAMOUNT,
              @PORDCLSPLNUNRSRVAMNT, @LPRODRSRVSTAT, @FALINKTYPE, @DEDUCTCODE, @UPDTHISLINE,
              @VATEXCEPTCODE, @PORDERFICHENO, @QPRODFCREF, @RELTRANSFCREF, @ATAXEXCEPTREASON,
              @ATAXEXCEPTCODE, @PRODORDERTYP, @SUBCONTORDERREF, @QPRODFCTYP, @PRDORDSLPLNRESERVE,
              @DESTSTATUS, @REGTYPREF, @FAPROFITACCREF, @FAPROFITCENTREF, @FALOSSACCREF,
              @FALOSSCENTREF, @CPACODE, @GTIPCODE, @PUBLICCOUNTRYREF, @QPRODITEMTYPE,
              @FUTMONTHCNT, @FUTMONTHBEGDATE, @QCTRANSFERREF, @QCTRANSFERAMNT, @KKEGACCREF,
              @KKEGCENTREF, @MNTORDERFREF, @FAKKEGAMOUNT, @MIDDLEMANEXPTYP, @EXPRACCREF,
              @EXPRCNTRREF, @KKEGVATACCREF, @KKEGVATCENTREF, @MARKINGTAGNO, @OWNER, @TCKTAXNR,
              @ADDTAXVATACCREF, @ADDTAXVATCENREF, @EXPDAYS, @CANCELLEDINVREF1, @CANCELLEDINVREF2,
              @CANCELLEDINVREF3, @CANCELLEDINVREF4, @FROMINTEGTYPE, @FROMINTEGREF,
              @QCTRANSFERREF2, @QCTRANSFERAMNT2, @EISRVDSTADDTAXINC, @TAXFREEACCREF,
              @TAXFREECNTRREF, @ADDTAXEFFECTKDV, @ADDTAXINLINENET, @ITMDISC, @ADDTAXREF,
              @COSCNTREFINFL, @PROUTCOSTINFLDIFF, @PROUTCOSTCRINFLDIFF, @COSACCREFINFL,
              @ORDFICHECMREF, @PURCHACCREFINFL, @PURCHCENTREFINFL, @DIIBLINECODE,
              @RETSOURCELINK, @ORGPRICE
            )
          `)

        consola.info(`STLINE satırı eklendi. Satır: ${lineNo}, STOCKREF: ${stockRef}`)
      }

      consola.success(`Tüm STLINE satırları başarıyla eklendi. Toplam: ${lines.length}`)
      return true
    }
    catch (error) {
      consola.error('Logo STLINE tablosuna ekleme sırasında hata oluştu:', error)
      return false
    }
  },
}

export default LogoErpStline
