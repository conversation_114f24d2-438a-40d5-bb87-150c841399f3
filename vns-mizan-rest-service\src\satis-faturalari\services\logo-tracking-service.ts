import type { SatisFaturaHeader, SatisFaturaLineItem } from '../models/sales-invoice.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'

/**
 * Service for handling operations with our tracking tables for Logo operations
 */
const LogoTrackingService = {
  /**
   * Insert into LogoInvoice table (our tracking table for Logo invoices)
   */
  insertLogoInvoice: async ({
    invoice,
    veritabaniId,
    satisFaturaId,
  }: {
    invoice: SatisFaturaHeader
    veritabaniId: string
    satisFaturaId?: number
  }): Promise<number | undefined> => {
    try {
      // Use our application database connection
      const appConnection = await DbService.getConnection('db')
      const request = appConnection.request()

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // We're using default values for CLIENTREF and ACCOUNTREF
      // No need to resolve them from ARP_CODE

      // Determine currency type
      let currInvoice = 0 // Default: TRY
      // Use CURR_INVOICE directly as it should already be resolved in the routes
      if (invoice.CURR_INVOICE) {
        currInvoice = invoice.CURR_INVOICE
      }

      // Get exchange rate
      const rcXrate = invoice.RC_XRATE || 0

      // Generate a GUID for the invoice
      const guid = randomUUID().toUpperCase()

      // Calculate totals if not provided
      const totalDiscounted = 0 // This would need to be calculated from line items
      const totalVat = 0 // This would need to be calculated from line items
      const grossTotal = 0 // This would need to be calculated from line items
      const netTotal = 0 // This would need to be calculated from line items

      // Map invoice fields to LogoInvoice table structure
      const result = await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, invoice.TYPE)
        .input('FICHENO', sql.VarChar(17), invoice.NUMBER)
        .input('DATE_', sql.Date, invoice.DATE)
        .input('TIME_', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE)
        .input('CLIENTREF', sql.Int, 0) // Use 0 as a default value
        .input('ACCOUNTREF', sql.Int, 0) // Use 0 as a default value
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('TOTALDISCOUNTED', sql.Float, totalDiscounted)
        .input('TOTALVAT', sql.Float, totalVat)
        .input('GROSSTOTAL', sql.Float, grossTotal)
        .input('NETTOTAL', sql.Float, netTotal)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1)
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2)
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3)
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4)
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5)
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6)
        .input('TRCURR', sql.SmallInt, currInvoice)
        .input('TRRATE', sql.Float, invoice.TC_XRATE || 0)
        .input('TRNET', sql.Float, netTotal)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, netTotal / (rcXrate || 1))
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('SALESMANREF', sql.Int, salesmanRef)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('PROJECTREF', sql.Int, projectRef)
        .input('DOCDATE', sql.Date, invoice.DOC_DATE || invoice.DATE)
        .input('PROFILEID', sql.SmallInt, invoice.PROFILE_ID || 2)
        .input('GUID', sql.VarChar(37), guid)
        .input('INSTEADOFDESP', sql.SmallInt, 1) // Always 1 for invoices created instead of dispatches
        .input('veritabani_id', sql.VarChar(37), veritabaniId)
        .input('satis_fatura_id', sql.Int, satisFaturaId)
        .query(`
          INSERT INTO LogoInvoice (
            GRPCODE, TRCODE, FICHENO, DATE_, TIME_, DOCODE, SPECODE, CLIENTREF, ACCOUNTREF,
            SOURCEINDEX, SOURCECOSTGRP, TOTALDISCOUNTED, TOTALVAT, GROSSTOTAL, NETTOTAL,
            GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6,
            TRCURR, TRRATE, TRNET, REPORTRATE, REPORTNET, BRANCH, DEPARTMENT, SALESMANREF,
            FACTORYNR, PROJECTREF, DOCDATE, PROFILEID, GUID, INSTEADOFDESP,
            veritabani_id, satis_fatura_id
          )
          VALUES (
            @GRPCODE, @TRCODE, @FICHENO, @DATE_, @TIME_, @DOCODE, @SPECODE, @CLIENTREF, @ACCOUNTREF,
            @SOURCEINDEX, @SOURCECOSTGRP, @TOTALDISCOUNTED, @TOTALVAT, @GROSSTOTAL, @NETTOTAL,
            @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6,
            @TRCURR, @TRRATE, @TRNET, @REPORTRATE, @REPORTNET, @BRANCH, @DEPARTMENT, @SALESMANREF,
            @FACTORYNR, @PROJECTREF, @DOCDATE, @PROFILEID, @GUID, @INSTEADOFDESP,
            @veritabani_id, @satis_fatura_id
          );
          SELECT SCOPE_IDENTITY() AS LOGICALREF;
        `)

      return result.recordset[0].LOGICALREF
    }
    catch (error) {
      consola.error('LogoInvoice tablosuna eklenirken hata oluştu:', error)
      return undefined
    }
  },

  /**
   * Insert into LogoStfiche table (our tracking table for Logo stock fiches)
   */
  insertLogoStfiche: async ({
    invoice,
    invoiceRef,
    veritabaniId,
    satisFaturaId,
    irsaliyeNo,
  }: {
    invoice: SatisFaturaHeader
    invoiceRef: number
    veritabaniId: string
    satisFaturaId?: number
    irsaliyeNo?: string
  }): Promise<number | undefined> => {
    try {
      // Use our application database connection
      const appConnection = await DbService.getConnection('db')
      const request = appConnection.request()

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // We're using default values for CLIENTREF and ACCOUNTREF
      // No need to resolve them from ARP_CODE

      // Get source cost group from SOURCE_WH
      let sourceCostGrp = null
      if (invoice.SOURCE_WH) {
        sourceCostGrp = await LogoLookupService.getCostGroupFromWarehouse(invoice.SOURCE_WH, veritabaniId)
      }

      // Generate a GUID for the stfiche
      const guid = randomUUID().toUpperCase()

      // Calculate totals if not provided
      const totalDiscounted = 0 // This would need to be calculated from line items
      const totalVat = 0 // This would need to be calculated from line items
      const grossTotal = 0 // This would need to be calculated from line items
      const netTotal = 0 // This would need to be calculated from line items
      const rcXrate = invoice.RC_XRATE || 0

      // Map invoice fields to LogoStfiche table structure
      const result = await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, 7) // 7 is for sales dispatch
        .input('IOCODE', sql.SmallInt, 3) // 3 is for output
        .input('FICHENO', sql.VarChar(17), irsaliyeNo || '')
        .input('DATE_', sql.Date, invoice.DATE)
        .input('FTIME', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER)
        .input('INVNO', sql.VarChar(17), invoice.NUMBER)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE)
        .input('INVOICEREF', sql.Int, invoiceRef)
        .input('CLIENTREF', sql.Int, 0) // Use 0 as a default value
        .input('ACCOUNTREF', sql.Int, 0) // Use 0 as a default value
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCECOSTGRP', sql.SmallInt, sourceCostGrp || 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('BILLED', sql.SmallInt, 1) // Always 1 for billed dispatches
        .input('TOTALDISCOUNTED', sql.Float, totalDiscounted)
        .input('TOTALVAT', sql.Float, totalVat)
        .input('GROSSTOTAL', sql.Float, grossTotal)
        .input('NETTOTAL', sql.Float, netTotal)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1)
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2)
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3)
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4)
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5)
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, netTotal / (rcXrate || 1))
        .input('SALESMANREF', sql.Int, salesmanRef)
        .input('PROJECTREF', sql.Int, projectRef)
        .input('GUID', sql.VarChar(37), guid)
        .input('DOCDATE', sql.DateTime, invoice.DOC_DATE ? new Date(invoice.DOC_DATE) : new Date(invoice.DATE))
        .input('DOCTIME', sql.Int, invoice.TIME)
        .input('veritabani_id', sql.VarChar(37), veritabaniId)
        .input('satis_fatura_id', sql.Int, satisFaturaId)
        .query(`
          INSERT INTO LogoStfiche (
            GRPCODE, TRCODE, IOCODE, FICHENO, DATE_, FTIME, DOCODE, INVNO, SPECODE,
            INVOICEREF, CLIENTREF, ACCOUNTREF, SOURCEINDEX, SOURCECOSTGRP, FACTORYNR,
            BRANCH, DEPARTMENT, BILLED, TOTALDISCOUNTED, TOTALVAT, GROSSTOTAL, NETTOTAL,
            GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6,
            REPORTRATE, REPORTNET, SALESMANREF, PROJECTREF, GUID, DOCDATE, DOCTIME,
            veritabani_id, satis_fatura_id
          )
          VALUES (
            @GRPCODE, @TRCODE, @IOCODE, @FICHENO, @DATE_, @FTIME, @DOCODE, @INVNO, @SPECODE,
            @INVOICEREF, @CLIENTREF, @ACCOUNTREF, @SOURCEINDEX, @SOURCECOSTGRP, @FACTORYNR,
            @BRANCH, @DEPARTMENT, @BILLED, @TOTALDISCOUNTED, @TOTALVAT, @GROSSTOTAL, @NETTOTAL,
            @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6,
            @REPORTRATE, @REPORTNET, @SALESMANREF, @PROJECTREF, @GUID, @DOCDATE, @DOCTIME,
            @veritabani_id, @satis_fatura_id
          );
          SELECT SCOPE_IDENTITY() AS LOGICALREF;
        `)

      return result.recordset[0].LOGICALREF
    }
    catch (error) {
      consola.error('LogoStfiche tablosuna eklenirken hata oluştu:', error)
      return undefined
    }
  },

  /**
   * Insert into LogoStline table (our tracking table for Logo stock lines)
   */
  insertLogoStlines: async ({
    invoiceRef,
    stficheRef,
    lines,
    veritabaniId,
    satisFaturaId,
    satisFaturaSatirIds,
  }: {
    invoiceRef: number
    stficheRef: number
    lines: SatisFaturaLineItem[]
    veritabaniId: string
    satisFaturaId?: number
    satisFaturaSatirIds?: number[]
  }): Promise<boolean> => {
    try {
      // Use our application database connection
      const appConnection = await DbService.getConnection('db')

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        if (!line)
          continue

        const request = appConnection.request()

        // Map line fields to LogoStline table structure
        // Resolve SALESMANREF from salesman_code if provided
        let salesmanRef = null
        if (line.SALEMANCODE) {
          salesmanRef = await LogoLookupService.getSalesmanRefFromCode(line.SALEMANCODE, veritabaniId)
        }

        // Resolve PROJECTREF from project_code if provided
        let projectRef = null
        if (line.PROJECT_CODE) {
          projectRef = await LogoLookupService.getProjectRefFromCode(line.PROJECT_CODE, veritabaniId)
        }

        // Get STOCKREF from MASTER_CODE
        let stockRef = null
        if (line.MASTER_CODE) {
          if (line.TYPE === 0) { // Regular item
            stockRef = await LogoLookupService.getItemRefFromCode(line.MASTER_CODE, veritabaniId)
          }
          else if (line.TYPE === 2) { // Service item
            stockRef = await LogoLookupService.getServiceRefFromCode(line.MASTER_CODE, veritabaniId)
          }
        }

        // Get source cost group from SOURCEINDEX
        let sourceCostGrp = null
        if (line.SOURCEINDEX) {
          sourceCostGrp = await LogoLookupService.getCostGroupFromWarehouse(line.SOURCEINDEX, veritabaniId)
        }

        // Get UOMREF from UNIT_CODE if provided
        let uomRef = null
        if (line.UNIT_CODE) {
          try {
            uomRef = await LogoLookupService.getUnitRefFromCode(line.UNIT_CODE, veritabaniId)
          }
          catch (error) {
            consola.warn(`Could not resolve UOMREF for UNIT_CODE ${line.UNIT_CODE}:`, error)
          }
        }

        // Generate a GUID for the stline
        const guid = randomUUID().toUpperCase()

        const lineNo = i + 1 // Line numbers start from 1
        const satisFaturaSatirId = satisFaturaSatirIds && satisFaturaSatirIds.length > i ? satisFaturaSatirIds[i] : null

        // Calculate line totals
        const vatRate = line.VAT_RATE || 0
        const quantity = line.QUANTITY || 0
        const price = line.PRICE || 0
        const vatIncluded = line.VAT_INCLUDED || 0
        const discountRate = line.DISCOUNT_RATE || 0

        // Calculate line net and VAT amounts
        let lineNet = quantity * price
        if (discountRate > 0) {
          lineNet = lineNet * (1 - discountRate / 100)
        }

        let vatAmount = 0
        let vatMatrah = 0

        if (vatIncluded === 1) {
          // VAT included in price
          vatMatrah = lineNet / (1 + vatRate / 100)
          vatAmount = lineNet - vatMatrah
        }
        else {
          // VAT not included in price
          vatMatrah = lineNet
          vatAmount = lineNet * (vatRate / 100)
        }

        const total = vatMatrah + vatAmount

        await request
          .input('STOCKREF', sql.Int, stockRef)
          .input('LINETYPE', sql.SmallInt, line.TYPE)
          .input('TRCODE', sql.SmallInt, 7) // 7 for sales dispatch
          .input('DATE_', sql.DateTime, new Date())
          .input('FTIME', sql.Int, 0) // Default time
          .input('IOCODE', sql.SmallInt, 4) // 4 for output
          .input('SOURCEINDEX', sql.SmallInt, line.SOURCEINDEX || 0)
          .input('SOURCECOSTGRP', sql.SmallInt, sourceCostGrp || 0)
          .input('STFICHEREF', sql.Int, stficheRef)
          .input('INVOICEREF', sql.Int, invoiceRef)
          .input('STFICHELNNO', sql.SmallInt, lineNo)
          .input('INVOICELNNO', sql.SmallInt, lineNo)
          .input('SPECODE', sql.VarChar(17), line.AUXIL_CODE)
          .input('AMOUNT', sql.Float, quantity)
          .input('PRICE', sql.Float, price)
          .input('TOTAL', sql.Float, total)
          .input('PRCURR', sql.SmallInt, 0) // Default currency
          .input('PRPRICE', sql.Float, line.EDT_PRICE || 0)
          .input('TRCURR', sql.SmallInt, 0) // Default currency
          .input('TRRATE', sql.Float, line.TC_XRATE || 0)
          .input('LINEEXP', sql.VarChar(251), line.DESCRIPTION)
          .input('DISCPER', sql.Float, discountRate)
          .input('UOMREF', sql.Int, uomRef)
          .input('VATINC', sql.SmallInt, vatIncluded)
          .input('VAT', sql.Float, vatRate)
          .input('VATAMNT', sql.Float, vatAmount)
          .input('VATMATRAH', sql.Float, vatMatrah)
          .input('BILLED', sql.SmallInt, 1) // Always 1 for billed lines
          .input('LINENET', sql.Float, lineNet)
          .input('SALESMANREF', sql.Int, salesmanRef)
          .input('PROJECTREF', sql.Int, projectRef)
          .input('GUID', sql.VarChar(37), guid)
          .input('FACTORYNR', sql.SmallInt, 0) // Default factory
          .input('veritabani_id', sql.VarChar(37), veritabaniId)
          .input('satis_fatura_id', sql.Int, satisFaturaId)
          .input('satis_fatura_satir_id', sql.Int, satisFaturaSatirId)
          .query(`
            INSERT INTO LogoStline (
              STOCKREF, LINETYPE, TRCODE, DATE_, FTIME, IOCODE, SOURCEINDEX, SOURCECOSTGRP,
              STFICHEREF, INVOICEREF, STFICHELNNO, INVOICELNNO, SPECODE,
              AMOUNT, PRICE, TOTAL, PRCURR, PRPRICE, TRCURR, TRRATE, LINEEXP,
              DISCPER, UOMREF, VATINC, VAT, VATAMNT, VATMATRAH, BILLED, LINENET,
              SALESMANREF, PROJECTREF, GUID, FACTORYNR,
              veritabani_id, satis_fatura_id
            )
            VALUES (
              @STOCKREF, @LINETYPE, @TRCODE, @DATE_, @FTIME, @IOCODE, @SOURCEINDEX, @SOURCECOSTGRP,
              @STFICHEREF, @INVOICEREF, @STFICHELNNO, @INVOICELNNO, @SPECODE,
              @AMOUNT, @PRICE, @TOTAL, @PRCURR, @PRPRICE, @TRCURR, @TRRATE, @LINEEXP,
              @DISCPER, @UOMREF, @VATINC, @VAT, @VATAMNT, @VATMATRAH, @BILLED, @LINENET,
              @SALESMANREF, @PROJECTREF, @GUID, @FACTORYNR,
              @veritabani_id, @satis_fatura_id
            );
          `)
      }

      return true
    }
    catch (error) {
      consola.error('LogoStline tablosuna eklenirken hata oluştu:', error)
      return false
    }
  },
}

export default LogoTrackingService
