import type { z } from 'zod'
import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Converts HH:MM:SS time string to Logo integer format.
 * Formula: HH * 16777216 + MM * 65536 + SS * 256
 * @param timeString Time in HH:MM:SS format
 * @returns Logo integer representation of time, or 0 if invalid.
 */
function convertTimeToLogoInt(timeString: string | undefined): number {
  if (!timeString || !timeFormatRegex.test(timeString)) {
    return 0
  }
  const parts = timeString.split(':')
  if (parts.length !== 3 || !parts[0] || !parts[1] || !parts[2]) {
    return 0
  }
  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    return 0
  }

  return hours * 16777216 + minutes * 65536 + seconds * 256
}

/**
 * Transforms Mizan request data to REST API format for Logo REST API integration
 * Used when use_rest=true
 */
export async function transformToRestFormat(
  requestData: z.infer<typeof satisFaturalariSchema>,
  veritabaniId: string,
): Promise<SatisFaturaInput> {
  // Get currency information - use default TL since para_birimi is not in main schema
  const currencyInfo = await LogoLookupService.getCurrencyTypeFromCode('TL', veritabaniId)

  // Get salesman information if provided
  let salesmanRef: number | undefined
  if (requestData.satis_elemani) {
    salesmanRef = await LogoLookupService.getSalesmanRefFromCode(requestData.satis_elemani, veritabaniId)
  }

  // Transform header data for REST API format
  const invoice = {
    TYPE: requestData.fatura_turu,
    NUMBER: requestData.fatura_no || '~',
    DATE: requestData.tarihi,
    TIME: convertTimeToLogoInt(requestData.saati),
    DOC_TRACK_NR: requestData.belge_no || '', // Use belge_no instead of belge_takip_no
    ARP_CODE: requestData.cari_kodu || '',
    GL_CODE: '', // muhasebe_kodu not in schema
    CURR_INVOICE: currencyInfo || 0,
    TC_XRATE: requestData.doviz_kuru || 1,
    RC_XRATE: 1, // raporlama_doviz_kuru not in schema
    NOTES1: requestData.aciklama || '',
    NOTES2: '', // aciklama2 not in schema
    NOTES3: '', // aciklama3 not in schema
    NOTES4: '', // aciklama4 not in schema
    GENEXP1: requestData.ozel_kod || '',
    GENEXP2: '', // ozel_kod2 not in schema
    GENEXP3: '', // ozel_kod3 not in schema
    GENEXP4: '', // ozel_kod4 not in schema
    GENEXP5: '', // ozel_kod5 not in schema
    GENEXP6: '', // ozel_kod6 not in schema
    PROJECT_CODE: requestData.proje_kodu || '',
    PAYMENT_CODE: '', // odeme_plani not in schema
    SALESMAN_CODE: requestData.satis_elemani || '',
    SALESMANREF: salesmanRef || 0,
    SOURCEINDEX: requestData.ambar_kodu || 0,
    FACTORY: requestData.fabrika_kodu || 0,
    DIVISION: requestData.isyeri_kodu || 0,
    DEPARTMENT: requestData.bolum_kodu || 0,
    AUXIL_CODE: '', // hareket_ozel_kodu not in main schema
    SHIP_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of sevk_tarihi
    SHIP_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of sevk_saati
    DELIVERY_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of teslim_tarihi
    DELIVERY_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of teslim_saati
    EINVOICE: 0, // e_fatura not in schema
    EINVOICE_TYPE: 0, // e_fatura_tipi not in schema
    PROFILE_ID: 0, // profil_id not in schema
    AFFECT_RISK: 1,
    // REST API specific fields
    INVOICE_SERIES: '', // fatura_seri not in schema
    INVOICE_NUMBER_FORMAT: requestData.fatura_numarasi_formati || '',
    DISPATCH_SERIES: '', // irsaliye_seri not in schema
    DISPATCH_NUMBER_FORMAT: requestData.irsaliye_numarasi_formati || '',
  }

  // Transform line items for REST API format
  const transactions = {
    items: await Promise.all(
      (requestData.fatura_satirlari || []).map(async (satir, index) => {
        // Get currency info for line item
        const lineCurrencyInfo = await LogoLookupService.getCurrencyTypeFromCode(satir.para_birimi || 'TL', veritabaniId)

        // Get salesman info for line item if provided
        let lineSalesmanRef: number | undefined
        if (satir.satis_elemani) {
          lineSalesmanRef = await LogoLookupService.getSalesmanRefFromCode(satir.satis_elemani, veritabaniId)
        }

        return {
          TYPE: satir.satir_turu || 0,
          MASTER_CODE: satir.malzeme_kodu || '',
          QUANTITY: satir.miktar || 0,
          PRICE: satir.birim_fiyat || 0,
          TOTAL: (satir.miktar || 0) * (satir.birim_fiyat || 0),
          CURR_TRANSACTION: lineCurrencyInfo || 0,
          TC_XRATE: satir.doviz_kuru || 1,
          RC_XRATE: 1, // raporlama_doviz_kuru not in line schema
          DESCRIPTION: satir.aciklama || '',
          UNIT_CODE: satir.birim_kodu || 'ADET',
          UNIT_CONV1: 1,
          UNIT_CONV2: 1,
          VAT_RATE: satir.kdv_orani || 0,
          VAT_INCLUDED: 0, // kdv_dahil not in line schema
          DISCOUNT_RATE: satir.indirim_orani || 0,
          SOURCEINDEX: satir.ambar_kodu || requestData.ambar_kodu || 0,
          FACTORY: satir.fabrika_kodu || requestData.fabrika_kodu || 0,
          PROJECT_CODE: satir.proje_kodu || requestData.proje_kodu || '',
          AUXIL_CODE: satir.hareket_ozel_kodu || '',
          SALEMANCODE: satir.satis_elemani || requestData.satis_elemani || '',
          SALESMANREF: lineSalesmanRef || salesmanRef || 0,
          EDT_PRICE: satir.dovizli_birim_fiyat || satir.birim_fiyat || 0,
          LINE_NR: index + 1,
          // REST API specific fields
          DISCOUNT_AMOUNT: satir.indirim_tutari || 0,
          NET_PRICE: (satir.birim_fiyat || 0) - (satir.indirim_tutari || 0),
        }
      }),
    ),
  }

  // Transform dispatch data for REST API format
  const dispatches = {
    items: [{
      TYPE: 7, // Standard dispatch type for sales
      NUMBER: requestData.irsaliye_no || '~',
      DATE: requestData.tarihi,
      TIME: convertTimeToLogoInt(requestData.saati),
      ARP_CODE: requestData.cari_kodu || '',
      SOURCEINDEX: requestData.ambar_kodu || 0,
      FACTORY: requestData.fabrika_kodu || 0,
      DIVISION: requestData.isyeri_kodu || 0,
      DEPARTMENT: requestData.bolum_kodu || 0,
      SHIP_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of sevk_tarihi
      SHIP_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of sevk_saati
      DELIVERY_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of teslim_tarihi
      DELIVERY_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of teslim_saati
      NOTES1: requestData.aciklama || '',
      PROJECT_CODE: requestData.proje_kodu || '',
      SALESMAN_CODE: requestData.satis_elemani || '',
      SALESMANREF: salesmanRef || 0,
      // REST API specific fields
      DISPATCH_SERIES: '', // irsaliye_seri not in schema
      DISPATCH_NUMBER_FORMAT: requestData.irsaliye_numarasi_formati || '',
    }],
  }

  return {
    INVOICE: invoice,
    TRANSACTIONS: transactions,
    DISPATCHES: dispatches,
    requestData, // Keep original request data for reference
  }
}
