import LogoCurrencyService from './logo-currency-service.ts'
import LogoNumberingService from './logo-numbering-service.ts'
import LogoReferenceService from './logo-reference-service.ts'
/**
 * Simplified Logo Lookup Service that coordinates specialized services
 * This replaces the massive original logo-lookup-service.ts file
 */
import LogoWarehouseService from './logo-warehouse-service.ts'

const LogoLookupService = {
  // Re-export warehouse functions
  getCostGroupFromWarehouse: LogoWarehouseService.getCostGroupFromWarehouse,

  // Re-export currency functions
  getCurrencyTypeFromCode: LogoCurrencyService.getCurrencyTypeFromCode,
  getFirmRepCurr: LogoCurrencyService.getFirmRepCurr,
  getExchangeRate: LogoCurrencyService.getExchangeRate,

  // Re-export reference functions
  getSalesmanRefFromCode: LogoReferenceService.getSalesmanRefFromCode,
  getProjectRefFromCode: LogoReferenceService.getProjectRefFromCode,
  getClientRefFromCode: LogoReferenceService.getClientRefFromCode,
  getItemRefFromCode: LogoReferenceService.getItemRefFromCode,
  getServiceRefFromCode: LogoReferenceService.getServiceRefFromCode,
  getUnitRefFromCode: LogoReferenceService.getUnitRefFromCode,
  getUserRefFromCapiuser: LogoReferenceService.getUserRefFromCapiuser,

  // Re-export numbering functions
  generateNewFicheNo: LogoNumberingService.generateNewFicheNo,
  generateFormattedFicheNo: LogoNumberingService.generateFormattedFicheNo,
  validateFicheNoFormat: LogoNumberingService.validateFicheNoFormat,
}

export default LogoLookupService
