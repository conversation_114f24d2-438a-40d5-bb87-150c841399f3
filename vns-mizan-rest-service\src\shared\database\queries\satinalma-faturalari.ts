/**
 * SQL queries for 'SatinalmaFaturalari' module initialization and operations
 *
 * This uses a simplified logging approach where instead of separate complex Logo tables,
 * we store JSON data in varchar(max) columns for easier maintenance:
 * - invoice_data: LogoInvoice data as JSON string
 * - stfiche_data: LogoStfiche data as JSON string
 * - stline_data: LogoStline data as JSON string (in SatinalmaFaturalariSatirlari)
 */
export const query = `
-- Table: SatinalmaFaturalari
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatinalmaFaturalari' and xtype='U')
BEGIN
    CREATE TABLE SatinalmaFaturalari (
        id int IDENTITY(1,1) PRIMARY KEY,
        fatura_turu int NOT NULL,
        fatura_no varchar(16) NOT NULL DEFAULT '~',
        tarihi date NOT NULL,
        saati varchar(8) NOT NULL,
        belge_no varchar(32) NULL,
        ozel_kod varchar(10) NULL,
        cari_kodu varchar(16) NULL,
        ambar_kodu int NULL,
        fabrika_kodu int NULL,
        aciklama varchar(1800) NULL,
        doviz_kuru float NULL,
        isyeri_kodu int NULL,
        bolum_kodu int NULL,
        odeme_kodu varchar(16) NULL,
        proje_kodu varchar(100) NULL,
        belge_tarihi date NULL,
        logo_fatura_no varchar(16) NULL,
        logo_fatura_logicalref int NULL,
        invoice_data varchar(max) NULL,
        stfiche_data varchar(max) NULL,
        error varchar(max) NULL,
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        veritabani_id VARCHAR(37),
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'SatinalmaFaturalari tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'SatinalmaFaturalari tablosu zaten mevcut';
END;

-- Table: SatinalmaFaturalariSatirlari
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatinalmaFaturalariSatirlari' and xtype='U')
BEGIN
    CREATE TABLE SatinalmaFaturalariSatirlari (
        id int IDENTITY(1,1) PRIMARY KEY,
        satinalma_fatura_id int NOT NULL,
        satir_turu int NOT NULL,
        malzeme_kodu varchar(16) NULL,
        ambar_kodu int NULL,
        fabrika_kodu int NULL,
        hareket_ozel_kodu varchar(17) NULL,
        miktar float NULL,
        birim_fiyat float NULL,
        aciklama varchar(250) NULL,
        birim_kodu varchar(10) NULL,
        kdv_orani float NULL,
        proje_kodu varchar(100) NULL,
        dovizli_birim_fiyat float NULL,
        tevkifat_yapilabilir int NULL,
        tevkifat_payi1 int NULL,
        tevkifat_payi2 int NULL,
        tevkifat_kodu varchar(16) NULL,
        tevkifat_aciklamasi varchar(250) NULL,
        masraf_merkezi1 varchar(25) NULL,
        masraf_merkezi3 varchar(25) NULL,
        masraf_merkezi4 varchar(25) NULL,
        stline_data varchar(max) NULL,
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (satinalma_fatura_id) REFERENCES SatinalmaFaturalari(id)
    );
    PRINT 'SatinalmaFaturalariSatirlari tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'SatinalmaFaturalariSatirlari tablosu zaten mevcut';
END;

-- Logo tables are replaced with simple data columns in main tables for easier logging

-- Check primary table creations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatinalmaFaturalari' and xtype='U')
    THROW 50008, 'SatinalmaFaturalari tablosu düzgün oluşturulmadı', 1;
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatinalmaFaturalariSatirlari' and xtype='U')
    THROW 50009, 'SatinalmaFaturalariSatirlari tablosu düzgün oluşturulmadı', 1;
`
