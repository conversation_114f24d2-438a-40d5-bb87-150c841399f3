import nodemailer from 'nodemailer'
import { readConfig } from './config-utils.ts'

export async function getTransporter() {
  const config = await readConfig()
  const mail = config.project_settings.mail
  if (!mail)
    throw new Error('Mail ayarları config.json dosyasında bulunamadı.')
  return nodemailer.createTransport({
    host: mail.host,
    port: mail.port,
    secure: mail.secure,
    auth: {
      user: mail.user,
      pass: mail.pass,
    },
  })
}

export async function sendMail({ to, subject, text, html }: { to: string, subject: string, text?: string, html?: string }) {
  const transporter = await getTransporter()
  const config = await readConfig()
  const from = config.project_settings.mail.from
  return transporter.sendMail({ from, to, subject, text, html })
}
