import { consola } from 'consola'
import DbService from '../../shared/services/db-service.ts'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo SQL validation operations
 * Contains functions that validate data against Logo ERP
 */
const LogoSqlValidation = {

  checkServiceCodeExists: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
            WHERE srvcard.CODE = @code
        `)

      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Hizmet kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkCustomerExists: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
            WHERE CODE = @code
        `)

      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Cari hesap kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkMasrafMerkeziExists: async ({ masraf_merkezi_kodu, veritabaniId }: { masraf_merkezi_kodu: string, veritabaniId: string }): Promise<boolean> => {
    if (!masraf_merkezi_kodu) {
      return true
    }
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', masraf_merkezi_kodu)
        .query(`
          SELECT CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_EMCENTER
          WHERE CODE = @code
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Masraf merkezi kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkProjeKoduExists: async ({ proje_kodu, veritabaniId }: { proje_kodu: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', proje_kodu)
        .query(`
          SELECT CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_PROJECT
          WHERE CODE = @code
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Proje kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkWarehouseExists: async ({ ambar, veritabaniId }: { ambar: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', ambar)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIWHOUSE
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Ambar kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkDivisionExists: async ({ isyeri, veritabaniId }: { isyeri: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', isyeri)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDIV
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('İşyeri kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkDepartmentExists: async ({ bolum, veritabaniId }: { bolum: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', bolum)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDEPT
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Bölüm kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkFactoryExists: async ({ fabrika, veritabaniId }: { fabrika: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', fabrika)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIFACTORY
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Fabrika kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkInvoiceNumberExists: async ({
    fatura_no,
    trcode,
    veritabaniId,
  }: {
    fatura_no: string
    trcode: number
    veritabaniId: string
  }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('ficheno', fatura_no)
        .input('trcode', trcode)
        .query(`
          SELECT FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE FICHENO = @ficheno AND TRCODE = @trcode
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Fatura numarası kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  checkDispatchNumberExists: async ({
    irsaliye_no,
    veritabaniId,
  }: {
    irsaliye_no: string
    veritabaniId: string
  }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('ficheno', irsaliye_no)
        .input('trcode', 7) // 7 is for sales invoice in STFICHE
        .query(`
          SELECT FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
          WHERE FICHENO = @ficheno AND TRCODE = @trcode
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('İrsaliye numarası kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  // Re-export shared configuration function
  getUseRestFlag: LogoConfigService.getUseRestFlag,
}

export default LogoSqlValidation
