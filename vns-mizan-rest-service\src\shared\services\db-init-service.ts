import type { IResult } from 'mssql'
import { consola } from 'consola'
import { query as carilerQuery } from '../database/queries/cariler.ts'
import { query as dbQuery } from '../database/queries/db.ts'
import { query as satinalmaFaturalariQuery } from '../database/queries/satinalma-faturalari.ts'
import { query as satisFaturalariQuery } from '../database/queries/satis-faturalari.ts'
import AuthService from './auth-service.ts'
import DbService from './db-service.ts'

/**
 * Interface for SQL message result
 */
interface SqlMessage {
  Message: string
}

/**
 * Interface for database initialization functions
 */
interface DbInitializer {
  connectionName: string
  query: string
  isRequired: boolean
  migrationName: string
}
/**
 * List of business tables initializers
 */
const businessTableInitializers: DbInitializer[] = [
  {
    connectionName: 'db',
    query: dbQuery,
    isRequired: true, // Main application database is required
    migrationName: 'Ana tablolar',
  },
  {
    connectionName: 'db',
    query: carilerQuery,
    isRequired: true, // cari hesaplar tables are required
    migrationName: 'Cari hesap tabloları',
  },
  {
    connectionName: 'db',
    query: satisFaturalariQuery,
    isRequired: true, // Satış faturaları tables are required
    migrationName: 'Satış fatura tabloları',
  },
  {
    connectionName: 'db',
    query: satinalmaFaturalariQuery,
    isRequired: true, // Satınalma faturaları tables are required
    migrationName: 'Satınalma fatura tabloları',
  },
]

/**
 * Service for handling database initialization
 */
const DbInitService = {
  /**
   * Initialize all databases
   * @throws {Error} If a required database initialization fails
   */
  async initializeAll() {
    const errors: Error[] = []
    // Then, initialize business tables
    for (const initializer of businessTableInitializers) {
      try {
        const pool = await DbService.getConnection(initializer.connectionName)
        // Execute initialization query
        const result: IResult<SqlMessage> = await pool.request().query(initializer.query)
        consola.success(`${initializer.connectionName} bağlantısı için ${initializer.migrationName} tamamlandı`)
        // Log any messages from the SQL script
        const messages = result.recordsets?.[0]
        if (messages && messages.length > 0) {
          consola.info(`${initializer.connectionName} bağlantısı ${initializer.migrationName} için SQL Mesajları:`, messages)
        }
      }
      catch (error) {
        const errorMessage = `${initializer.connectionName} bağlantısı ${initializer.migrationName} iş tabloları başlatılamadı: ${error instanceof Error ? error.message : String(error)}`
        if (initializer.isRequired) {
          consola.error(`${initializer.connectionName} bağlantısı ${initializer.migrationName} iş tabloları başlatılırken kritik hata:`, error)
          throw new Error(errorMessage)
        }
        errors.push(new Error(errorMessage))
        consola.warn(errorMessage)
      }
    }
    // Create initial admin user
    if (errors.length === 0) {
      try {
        await AuthService.createInitialAdminUser()
      }
      catch (error) {
        consola.error('Admin kullanıcısı oluşturulurken hata:', error)
        errors.push(new Error(`Admin kullanıcı oluşturulamadı: ${error instanceof Error ? error.message : String(error)}`))
      }
    }
    if (errors.length > 0) {
      consola.warn('Bazı kritik olmayan veritabanı başlatmaları başarısız oldu:', errors)
    }
  },
  /**
   * Initialize main application database and create initial admin user
   * @throws {Error} If the initialization fails
   */
  async init(): Promise<void> {
    try {
      consola.info('Ana veritabanı başlatılıyor...')
      await AuthService.createInitialAdminUser()
      consola.info('İlk yönetici kullanıcısı başarıyla oluşturuldu')
    }
    catch (error) {
      const errorMessage = `Veritabanı başlatma hatası: ${error instanceof Error ? error.message : String(error)}`
      consola.error('Kritik başlatma hatası:', error)
      throw new Error(errorMessage)
    }
  },
}

export default DbInitService
