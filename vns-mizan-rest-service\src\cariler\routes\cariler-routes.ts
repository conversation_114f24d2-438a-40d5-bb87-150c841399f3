import type { RequestHandler } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import { getCariBilgileri } from '../services/cari-bilgileri-service.ts'
import { sendCariHesap } from '../services/cari-hesaplar-service.ts'

const router = Router()

/**
 * Get cari bilgileri validation schema
 */
const getCariBilgileriSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * Cari hesaplar validation schema for POST endpoint
 */
const cariHesaplarSchema = z
  .object({
    veritabani_id: z
      .string({
        required_error: 'Veritabanı ID girilmelidir',
      })
      .min(1, 'Veritabanı ID girilmelidir'),
    kodu: z
      .string({
        required_error: 'Cari hesap kodu girilmelidir',
      })
      .min(1, 'Cari hesap kodu en az 1 karakter olmalıdır')
      .max(16, 'Cari hesap kodu en fazla 16 karakter olabilir')
      .toUpperCase(),
    vkVeyaTckNo: z
      .string({
        required_error: 'Vergi numarası veya T.C Kimlik No girilmelidir',
      })
      .min(10, 'Vergi numarası veya T.C Kimlik No en az 10 karakter olmalıdır')
      .max(11, 'Vergi numarası veya T.C Kimlik No en fazla 11 karakter olabilir'),
    unvan: z
      .string({
        required_error: 'Unvan girilmelidir',
      })
      .min(1, 'Unvan girilmelidir')
      .max(200, 'Unvan en fazla 200 karakter olabilir'),
    ad: z.string().max(50, 'Ad en fazla 50 karakter olabilir').optional(),
    soyad: z.string().max(50, 'Soyad en fazla 50 karakter olabilir').optional(),
    adres: z
      .string({
        required_error: 'Adres girilmelidir',
      })
      .min(1, 'Adres girilmelidir')
      .max(400, 'Adres en fazla 400 karakter olabilir'),
    il: z
      .string({
        required_error: 'İl girilmelidir',
      })
      .min(1, 'İl girilmelidir')
      .max(50, 'İl en fazla 50 karakter olabilir'),
    ilce: z
      .string({
        required_error: 'İlçe girilmelidir',
      })
      .min(1, 'İlçe girilmelidir')
      .max(50, 'İlçe en fazla 50 karakter olabilir'),
    ulke: z.string().max(50, 'Ülke en fazla 50 karakter olabilir').optional(),
    ulkeKodu: z.string().max(10, 'Ülke kodu en fazla 10 karakter olabilir').optional(),
    email: z
      .string({
        required_error: 'E-posta adresi girilmelidir',
      })
      .email('Geçerli bir e-posta adresi giriniz'),
    ozelKod: z.string().max(10, 'Özel kod en fazla 10 karakter olabilir').optional(),
    postaKodu: z.string({ required_error: 'Posta kodu girilmelidir' }).min(1, 'Posta kod girilmelidir').max(5, 'Posta kod en fazla 5 karakter olabilir'),
  })
  .strict()
  .refine(
    (data) => {
      // If TCKN (11 digits), require name and surname
      if (data.vkVeyaTckNo.length === 11) {
        return data.ad && data.soyad
      }
      return true
    },
    {
      message: 'T.C. Kimlik No girildiğinde ad ve soyad zorunludur',
      path: ['ad', 'soyad'],
    },
  )

/**
 * @openapi
 * /cariler:
 *   get:
 *     tags: [Cari Hesaplar]
 *     summary: Cari bilgilerini listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               title: CariBilgileriResponse
 *               description: Logo veritabanından alınan cari hesap listesi
 *               type: array
 *               items:
 *                 type: object
 *                 required: [id, kodu, veritabani_id]
 *                 properties:
 *                   id:
 *                     type: number
 *                     description: Cari hesap ID'si
 *                   kodu:
 *                     type: string
 *                     description: Cari hesap kodu
 *                   create_date:
 *                     type: string
 *                     format: date-time
 *                     description: Oluşturulma tarihi
 *                   update_date:
 *                     type: string
 *                     format: date-time
 *                     description: Son güncelleme tarihi
 *                   unvan1:
 *                     type: string
 *                     description: Cari hesap ünvanı 1
 *                   unvan2:
 *                     type: string
 *                     description: Cari hesap ünvanı 2
 *                   muhasebe_kodu:
 *                     type: string
 *                     description: Muhasebe kodu
 *                   doviz1:
 *                     type: number
 *                     description: Birincil döviz tipi
 *                   doviz1_adi:
 *                     type: string
 *                     description: Birincil döviz adı
 *                   vergi_dairesi:
 *                     type: string
 *                     description: Vergi dairesi
 *                   vergi_no:
 *                     type: string
 *                     description: Vergi numarası/TC Kimlik No
 *                   sektor:
 *                     type: string
 *                     description: Sektör bilgisi
 *                   bolge:
 *                     type: string
 *                     description: Bölge kodu
 *                   grup:
 *                     type: string
 *                     description: Grup kodu
 *                   temsilci:
 *                     type: string
 *                     description: Temsilci adı
 *                   efatura_mi:
 *                     type: boolean
 *                     description: E-Fatura mükellefi mi?
 *                   kilitli:
 *                     type: boolean
 *                     description: Hesap kilitli mi?
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       500:
 *         description: Sunucu hatası
 */
const getCarilerHandler: RequestHandler = async (req, res) => {
  try {
    const result = getCariBilgileriSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const cariBilgileri = await getCariBilgileri({ veritabaniId: veritabani_id })
    res.json(cariBilgileri)
  }
  catch (error) {
    consola.error('Cariler alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Cariler alınırken bir hata oluştu',
    })
  }
}

/**
 * @openapi
 * /cariler:
 *   post:
 *     tags: [Cari Hesaplar]
 *     summary: Yeni cari hesap kaydeder veya günceller
 *     security:
 *       - sessionAuth: []
 *     description: Yeni bir cari hesap kaydı oluşturur veya mevcut cari hesabı günceller. Vergi numarası veya T.C. Kimlik numarası ile müşteri kaydı yapılabilir. TCKN kullanıldığında ad ve soyad bilgileri zorunludur.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             title: CariHesapRequest
 *             description: Cari hesap kayıt veya güncelleme isteği için gerekli alanlar
 *             type: object
 *             required:
 *               - veritabani_id
 *               - kodu
 *               - vkVeyaTckNo
 *               - unvan
 *               - adres
 *               - il
 *               - ilce
 *               - email
 *               - ozelKod
 *               - postaKodu
 *             properties:
 *               veritabani_id:
 *                 type: string
 *                 title: Veritabanı ID
 *               kodu:
 *                 type: string
 *                 title: Cari hesap kodu
 *                 minLength: 1
 *                 maxLength: 16
 *               vkVeyaTckNo:
 *                 type: string
 *                 title: Vergi/TC Kimlik No
 *                 minLength: 10
 *                 maxLength: 11
 *               unvan:
 *                 type: string
 *                 title: Ünvan
 *                 maxLength: 200
 *               ad:
 *                 type: string
 *                 title: Ad
 *                 maxLength: 50
 *               soyad:
 *                 type: string
 *                 title: Soyad
 *                 maxLength: 50
 *               adres:
 *                 type: string
 *                 title: Adres
 *                 maxLength: 400
 *               il:
 *                 type: string
 *                 title: İl
 *                 maxLength: 50
 *               ilce:
 *                 type: string
 *                 title: İlçe
 *                 maxLength: 50
 *               ulke:
 *                 type: string
 *                 title: Ülke
 *                 maxLength: 50
 *               ulkeKodu:
 *                 type: string
 *                 title: Ülke Kodu
 *                 maxLength: 10
 *               email:
 *                 type: string
 *                 title: E-posta
 *                 format: email
 *               ozelKod:
 *                 type: string
 *                 title: Özel Kod
 *                 maxLength: 10
 *               postaKodu:
 *                 type: string
 *                 title: Posta Kodu
 *                 maxLength: 5
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required: [message, data]
 *               properties:
 *                 message:
 *                   type: string
 *                   description: İşlem sonucu mesajı
 *                   example: Cari kart başarıyla oluşturuldu
 *                 data:
 *                   type: object
 *                   required: [code, vkVeyaTckNo, logicalref]
 *                   properties:
 *                     code:
 *                       type: string
 *                       description: Cari hesap kodu
 *                     vkVeyaTckNo:
 *                       type: string
 *                       description: Vergi/TC Kimlik No
 *                     logicalref:
 *                       type: number
 *                       description: Logo sistemindeki referans numarası
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *                   description: Validasyon hataları
 *                 message:
 *                   type: string
 *                   description: Hata mesajı
 *       500:
 *         description: Sunucu hatası
 */
const postCarilerHandler: RequestHandler = async (req, res) => {
  try {
    const result = cariHesaplarSchema.safeParse(req.body || {})

    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path.join('.')
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    // Validate that the veritabani_id exists in the config
    try {
      await getLogoConfigById(result.data.veritabani_id)
    }
    catch (error) {
      res.status(400).json({
        errors: {
          veritabani_id: ['Geçersiz veritabanı ID'],
          message: error instanceof Error ? error.message : 'Bir hata oluştu.',
        },
      })
      return
    }

    // Extract veritabani_id from the data
    const { veritabani_id, ...cariHesapData } = result.data
    const response = await sendCariHesap({
      cariHesapData,
      veritabaniId: veritabani_id,
    })

    res.status(200).json({
      message: response.isExisting ? 'Cari kart zaten mevcut' : 'Cari kart başarıyla oluşturuldu',
      data: {
        code: response.code,
        vkVeyaTckNo: response.vkVeyaTckNo,
        logicalref: response.logicalref,
      },
    })
  }
  catch (error) {
    consola.error('Cari kartı kaydedilirken hata oluştu:', error)
    res.status(500).json({ message: error instanceof Error ? error.message : 'Bir hata oluştu.' })
  }
}

router.get('/', getCarilerHandler)
router.post('/', postCarilerHandler)

export default router
