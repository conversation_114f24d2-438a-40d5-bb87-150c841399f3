import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { addLogoVeritabaniBaglantisi, deleteLogoVeritabaniBaglantisi, getLogoVeritabaniBaglantilari, getLogoVeritabaniBaglantisiById, updateLogoVeritabaniBaglantisi } from '../services/logo-veritabani-baglantilari-service.ts'

const router = Router()

const veritabaniBaglantisiSchema = z.object({
  server: z.string().min(1, 'Sunucu adı zorunludur'),
  database: z.string().min(1, 'Veritabanı adı zorunludur'),
  user: z.string().min(1, 'Kullanıcı adı zorunludur'),
  password: z.string().min(1, 'Şifre zorunludur'),
  port: z.number().int().positive('Port numarası geçerli olmalıdır'),
  encrypt: z.boolean(),
  trust_server_certificate: z.boolean(),
})

const logoBaglantisiSchema = z.object({
  id: z.string().uuid('Geçerli bir UUID giriniz'),
  active: z.boolean(),
  name: z.string().min(1, 'Bağlantı adı zorunludur'),
  sql: veritabaniBaglantisiSchema,
  erp: z.object({
    logodb_master: z.string(),
    firma_numarasi: z.string(),
    donem_numarasi: z.string(),
    kullanici_adi: z.string(),
    sifre: z.string(),
    elogo: z.object({
      web_service_url: z.string().url('Geçerli bir URL giriniz'),
    }),
    rest_settings: z.object({
      use_rest: z.boolean(),
      rest_api_url: z.string().url('Geçerli bir URL giriniz'),
      client_key: z.string(),
    }),
  }),
})

/**
 * @openapi
 * components:
 *   schemas:
 *     VeritabaniBaglantisiSchema:
 *       type: object
 *       required:
 *         - server
 *         - database
 *         - user
 *         - password
 *         - port
 *       properties:
 *         server:
 *           type: string
 *           description: Veritabanı sunucusu
 *         database:
 *           type: string
 *           description: Veritabanı adı
 *         user:
 *           type: string
 *           description: Kullanıcı adı
 *         password:
 *           type: string
 *           description: Şifre
 *         port:
 *           type: integer
 *           description: Port numarası
 *           example: 1433
 *         encrypt:
 *           type: boolean
 *           description: SSL şifreleme kullanılsın mı?
 *           example: false
 *         trust_server_certificate:
 *           type: boolean
 *           description: Sunucu sertifikası doğrulanmasın mı?
 *           example: true
 *     LogoBaglantisi:
 *       type: object
 *       required:
 *         - id
 *         - name
 *         - sql
 *         - erp
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Bağlantı ID'si
 *         active:
 *           type: boolean
 *           description: Bağlantı aktif mi?
 *         name:
 *           type: string
 *           description: Bağlantı adı
 *         sql:
 *           $ref: '#/components/schemas/VeritabaniBaglantisiSchema'
 *         erp:
 *           type: object
 *           properties:
 *             logodb_master:
 *               type: string
 *             firma_numarasi:
 *               type: string
 *               example: 001
 *             donem_numarasi:
 *               type: string
 *               example: 01
 *             kullanici_adi:
 *               type: string
 *             sifre:
 *               type: string
 *             elogo:
 *               type: object
 *               properties:
 *                 web_service_url:
 *                   type: string
 *                   format: uri
 *                   example: https://pb-g.elogo.com.tr/PostBoxService.svc
 *             rest_settings:
 *               type: object
 *               properties:
 *                 use_rest:
 *                   type: boolean
 *                   description: REST API kullanılsın mı?
 *                   example: true
 *                 rest_api_url:
 *                   type: string
 *                   format: uri
 *                   example: http://127.0.0.1:32001/api/v1
 *                 client_key:
 *                   type: string
 */

/**
 * @openapi
 * /logo-veritabani-baglantilari:
 *   get:
 *     tags: [Logo Veritabanı Bağlantıları]
 *     summary: Tüm Logo veritabanı bağlantılarını listeler
 *     security:
 *       - sessionAuth: []
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/LogoBaglantisi'
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 mesaj:
 *                   type: string
 */

const getLogoVeritabaniBaglantilariHandler: RequestHandler = async (_req, res) => {
  try {
    const baglantiler = await getLogoVeritabaniBaglantilari()
    res.json(baglantiler)
  }
  catch (error) {
    consola.error('Bağlantılar listelenirken hata:', error)
    res.status(500).json({ mesaj: error instanceof Error ? error.message : 'Sunucu hatası' })
  }
}

/**
 * @openapi
 * /logo-veritabani-baglantilari:
 *   post:
 *     tags: [Logo Veritabanı Bağlantıları]
 *     summary: Yeni Logo veritabanı bağlantısı ekler
 *     security:
 *       - sessionAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LogoBaglantisi'
 *     responses:
 *       201:
 *         description: Başarıyla oluşturuldu
 *       400:
 *         description: Geçersiz istek
 *       500:
 *         description: Sunucu hatası
 */
const addLogoVeritabaniBaglantisiHandler: RequestHandler = async (req, res) => {
  try {
    const result = logoBaglantisiSchema.safeParse(req.body)
    if (!result.success) {
      const hatalar = result.error.issues.reduce(
        (acc, issue) => {
          const alan = issue.path[0] as string
          if (!acc[alan])
            acc[alan] = []
          acc[alan].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ hatalar })
      return
    }

    const yeniBaglanti = await addLogoVeritabaniBaglantisi({ baglanti: result.data })
    res.status(201).json(yeniBaglanti)
  }
  catch (error) {
    consola.error('Bağlantı eklenirken hata:', error)
    res.status(500).json({ mesaj: error instanceof Error ? error.message : 'Sunucu hatası' })
  }
}

/**
 * @openapi
 * /logo-veritabani-baglantilari/{id}:
 *   put:
 *     tags: [Logo Veritabanı Bağlantıları]
 *     summary: Logo veritabanı bağlantısını günceller
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LogoBaglantisi'
 *     responses:
 *       200:
 *         description: Başarıyla güncellendi
 *       400:
 *         description: Geçersiz istek
 *       404:
 *         description: Bağlantı bulunamadı
 *       500:
 *         description: Sunucu hatası
 */
const updateLogoVeritabaniBaglantisiHandler: RequestHandler = async (req, res) => {
  try {
    const id = req.params.id as string
    const result = logoBaglantisiSchema.safeParse(req.body)
    if (!result.success) {
      const hatalar = result.error.issues.reduce(
        (acc, issue) => {
          const alan = issue.path[0] as string
          if (!acc[alan])
            acc[alan] = []
          acc[alan].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ hatalar })
      return
    }

    const guncelBaglanti = await updateLogoVeritabaniBaglantisi({ id, baglanti: result.data })
    res.json(guncelBaglanti)
  }
  catch (error) {
    consola.error('Bağlantı güncellenirken hata:', error)
    res.status(500).json({ mesaj: error instanceof Error ? error.message : 'Sunucu hatası' })
  }
}

/**
 * @openapi
 * /logo-veritabani-baglantilari/{id}:
 *   delete:
 *     tags: [Logo Veritabanı Bağlantıları]
 *     summary: Logo veritabanı bağlantısını siler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       204:
 *         description: Başarıyla silindi
 *       404:
 *         description: Bağlantı bulunamadı
 *       500:
 *         description: Sunucu hatası
 */
const deleteLogoVeritabaniBaglantisiHandler: RequestHandler = async (req, res) => {
  try {
    const id = req.params.id as string
    await deleteLogoVeritabaniBaglantisi({ id })
    res.status(204).send()
  }
  catch (error) {
    consola.error('Bağlantı silinirken hata:', error)
    res.status(500).json({ mesaj: error instanceof Error ? error.message : 'Sunucu hatası' })
  }
}

/**
 * @openapi
 * /logo-veritabani-baglantilari/{id}:
 *   get:
 *     tags: [Logo Veritabanı Bağlantıları]
 *     summary: Logo veritabanı bağlantısını ID'ye göre getirir
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LogoBaglantisi'
 *       404:
 *         description: Bağlantı bulunamadı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 mesaj:
 *                   type: string
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 mesaj:
 *                   type: string
 */

const getLogoVeritabaniBaglantisiByIdHandler: RequestHandler = async (req, res) => {
  try {
    const id = req.params.id as string
    const baglanti = await getLogoVeritabaniBaglantisiById({ id })
    res.json(baglanti)
  }
  catch (error) {
    consola.error('Bağlantı getirilirken hata:', error)
    res.status(404).json({ mesaj: error instanceof Error ? error.message : 'Bağlantı bulunamadı' })
  }
}

router.get('/', getLogoVeritabaniBaglantilariHandler)
router.get('/:id', getLogoVeritabaniBaglantisiByIdHandler)
router.post('/', addLogoVeritabaniBaglantisiHandler)
router.put('/:id', updateLogoVeritabaniBaglantisiHandler)
router.delete('/:id', deleteLogoVeritabaniBaglantisiHandler)

export default router
