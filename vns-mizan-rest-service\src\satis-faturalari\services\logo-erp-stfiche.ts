import type { SatisFaturaHeader } from '../models/sales-invoice.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo ERP STFICHE table operations
 * Handles insertions into LG_{FIRMA}_{DONEM}_STFICHE table with comprehensive column mapping
 * Preserves all 170+ database columns and field assignments from original logo-erp-integration.ts
 */
const LogoErpStfiche = {
  /**
   * Insert into actual Logo STFICHE table (LG_{FFF}_{DD}_STFICHE)
   * Preserves all database column mappings and SQL operations from original implementation
   */
  insertLogoActualStfiche: async ({
    invoice,
    invoiceRef,
    ficheNo,
    veritabaniId,
    requestData,
    totals,
    logoCredentials,
  }: {
    invoice: SatisFaturaHeader
    invoiceRef: number
    ficheNo: string
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId,
        })
      }

      // Generate a unique stfiche number
      let stficheno
      // Check if a format is provided for irsaliye_no
      const irsaliyeFormat = requestData?.irsaliye_numarasi_formati

      if (irsaliyeFormat && irsaliyeFormat.includes('_')) {
        // Use formatted fiche number generation
        stficheno = await LogoLookupService.generateFormattedFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          format: irsaliyeFormat,
          veritabaniId,
        })
      }
      else {
        // Use default fiche number generation
        stficheno = await LogoLookupService.generateNewFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          veritabaniId,
        })
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided (not used in STFICHE but kept for consistency)
      const totalVat = totals?.totalVat || 0
      const totalNet = totals?.totalNet || 0
      const totalGross = totals?.totalGross || 0
      const totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(
          invoice.DATE,
          invoice.CURR_INVOICE,
          veritabaniId,
        )
        tcXrate = exchangeRate || 1
        rcXrate = exchangeRate || 1
      }

      consola.info(`Logo STFICHE tablosuna ekleme başlatılıyor. İrsaliye No: ${stficheno}`)

      // Map invoice fields to Logo STFICHE table structure based on the provided JSON example
      await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, 7) // 7 is for sales invoice in STFICHE
        .input('IOCODE', sql.SmallInt, 3) // 3 is for output
        .input('FICHENO', sql.VarChar(17), stficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('FTIME', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('INVNO', sql.VarChar(17), ficheNo)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE || '')
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('INVOICEREF', sql.Int, invoiceRef)
        .input('CLIENTREF', sql.Int, clientRef || 0)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('ACCOUNTREF', sql.Int, 0) // Account reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('PRODORDERREF', sql.Int, 0) // Production order reference
        .input('PORDERFICHENO', sql.VarChar(17), '') // Production order fiche number
        .input('SOURCETYPE', sql.SmallInt, 0)
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCEWSREF', sql.Int, 0)
        .input('SOURCEPOLNREF', sql.Int, 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('DESTTYPE', sql.SmallInt, 0)
        .input('DESTINDEX', sql.SmallInt, 0)
        .input('DESTWSREF', sql.Int, 0)
        .input('DESTPOLNREF', sql.Int, 0)
        .input('DESTCOSTGRP', sql.SmallInt, 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('COMPBRANCH', sql.SmallInt, 0)
        .input('COMPDEPARTMENT', sql.SmallInt, 0)
        .input('COMPFACTORY', sql.SmallInt, 0)
        .input('PRODSTAT', sql.SmallInt, 0)
        .input('DEVIR', sql.SmallInt, 0)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('BILLED', sql.SmallInt, 1)
        .input('ACCOUNTED', sql.SmallInt, 0)
        .input('UPDCURR', sql.SmallInt, 0)
        .input('INUSE', sql.SmallInt, 0)
        .input('INVKIND', sql.SmallInt, 0)
        .input('ADDDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTED', sql.Float, totals?.totalDiscounted || 0)
        .input('ADDEXPENSES', sql.Float, 0)
        .input('TOTALEXPENSES', sql.Float, 0)
        .input('TOTALDEPOZITO', sql.Float, 0)
        .input('TOTALPROMOTIONS', sql.Float, 0)
        .input('TOTALVAT', sql.Float, totals?.totalVat || 0)
        .input('GROSSTOTAL', sql.Float, totals?.totalGross || 0)
        .input('NETTOTAL', sql.Float, totals?.totalNet || 0)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1 || '')
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2 || '')
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3 || '')
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4 || '')
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5 || '')
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6 || '')
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, (totals?.totalNet || 0) / rcXrate)
        .input('EXTENREF', sql.Int, 0)
        .input('PAYDEFREF', sql.Int, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('FICHECNT', sql.SmallInt, 1)
        .input('ACCFICHEREF', sql.Int, 0)
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREADEDDATE', sql.DateTime, now) // Note: CREADEDDATE not CREATEDDATE
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .input('SALESMANREF', sql.Int, salesmanRef || 0)
        .input('CANCELLEDACC', sql.SmallInt, 0)
        .input('SHPTYPCOD', sql.VarChar(13), '')
        .input('SHPAGNCOD', sql.VarChar(13), '')
        .input('TRACKNR', sql.VarChar(17), '')
        .input('GENEXCTYP', sql.SmallInt, 0)
        .input('LINEEXCTYP', sql.SmallInt, 0)
        .input('TRADINGGRP', sql.VarChar(17), '')
        .input('TEXTINC', sql.SmallInt, 0)
        .input('SITEID', sql.SmallInt, 0)
        .input('RECSTATUS', sql.SmallInt, 0)
        .input('ORGLOGICREF', sql.Int, 0)
        .input('WFSTATUS', sql.SmallInt, 0)
        .input('SHIPINFOREF', sql.Int, 0)
        .input('DISTORDERREF', sql.Int, 0)
        .input('SENDCNT', sql.SmallInt, 0)
        .input('DLVCLIENT', sql.Int, 0)
        .input('DOCTRACKINGNR', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('ADDTAXCALC', sql.SmallInt, 0)
        .input('TOTALADDTAX', sql.Float, 0)
        .input('UGIRTRACKINGNO', sql.VarChar(51), '')
        .input('QPRODFCREF', sql.Int, 0)
        .input('VAACCREF', sql.Int, 0)
        .input('VACENTERREF', sql.Int, 0)
        .input('ORGLOGOID', sql.VarChar(25), '')
        .input('FROMEXIM', sql.SmallInt, 0)
        .input('FRGTYPCOD', sql.VarChar(17), '')
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('TRNET', sql.Float, (totals?.totalNet || 0) / tcXrate)
        .input('EXIMWHFCREF', sql.Int, 0)
        .input('EXIMFCTYPE', sql.SmallInt, 0)
        .input('MAINSTFCREF', sql.Int, 0)
        .input('FROMORDWITHPAY', sql.SmallInt, 0)
        .input('PROJECTREF', sql.Int, projectRef || 0)
        .input('WFLOWCRDREF', sql.Int, 0)
        .input('STATUS', sql.SmallInt, 0)
        .input('UPDTRCURR', sql.SmallInt, 0)
        .input('TOTALEXADDTAX', sql.Float, 0)
        .input('AFFECTCOLLATRL', sql.SmallInt, 0)
        .input('DEDUCTIONPART1', sql.SmallInt, 0)
        .input('DEDUCTIONPART2', sql.SmallInt, 0)
        .input('GRPFIRMTRANS', sql.SmallInt, 0)
        .input('AFFECTRISK', sql.SmallInt, 0)
        .input('DISPSTATUS', sql.SmallInt, 0)
        .input('APPROVE', sql.SmallInt, 0)
        .input('CANTCREDEDUCT', sql.SmallInt, 0)
        .input('SHIPDATE', sql.DateTime, requestData?.irsaliye_tarihi ? new Date(requestData.irsaliye_tarihi) : new Date(invoice.DATE))
        .input('SHIPTIME', sql.Int, invoice.TIME)
        .input('ENTRUSTDEVIR', sql.SmallInt, 0)
        .input('RELTRANSFCREF', sql.Int, 0)
        .input('FROMTRANSFER', sql.SmallInt, 0)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('GLOBALID', sql.VarChar(51), '')
        .input('COMPSTFCREF', sql.Int, 0)
        .input('COMPINVREF', sql.Int, 0)
        .input('TOTALSERVICES', sql.Float, 0)
        .input('CAMPAIGNCODE', sql.VarChar(51), '')
        .input('OFFERREF', sql.Int, 0)
        .input('EINVOICETYP', sql.SmallInt, 0)
        .input('EINVOICE', sql.SmallInt, 0)
        .input('NOCALCULATE', sql.SmallInt, 0)
        .input('PRODORDERTYP', sql.SmallInt, 0)
        .input('QPRODFCTYP', sql.SmallInt, 0)
        .input('PRDORDSLPLNRESERVE', sql.SmallInt, 0)
        .input('CONTROLINFO', sql.SmallInt, 0)
        .input('EDESPATCH', sql.SmallInt, 0)
        .input('DOCTIME', sql.Int, invoice.TIME)
        .input('EDESPSTATUS', sql.SmallInt, 0)
        .input('PROFILEID', sql.SmallInt, 0)
        .input('DELIVERYCODE', sql.VarChar(51), '')
        .input('DESTSTATUS', sql.SmallInt, 0)
        .input('CANCELEXP', sql.VarChar(51), '')
        .input('UNDOEXP', sql.VarChar(51), '')
        .input('CREATEWHERE', sql.SmallInt, 0)
        .input('PUBLICBNACCREF', sql.Int, 0)
        .input('ACCEPTEINVPUBLIC', sql.SmallInt, 0)
        .input('VATEXCEPTCODE', sql.VarChar(51), '')
        .input('VATEXCEPTREASON', sql.VarChar(51), '')
        .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
        .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
        .input('TAXFREECHX', sql.SmallInt, 0)
        .input('MNTORDERFREF', sql.Int, 0)
        .input('PRINTEDDESPFCNO', sql.VarChar(17), '')
        .input('OKCFICHE', sql.SmallInt, 0)
        .input('NOTIFYCRDREF', sql.Int, 0)
        .input('CANCELLEDINVREF1', sql.Int, 0)
        .input('CANCELLEDINVREF2', sql.Int, 0)
        .input('CANCELLEDINVREF3', sql.Int, 0)
        .input('CANCELLEDINVREF4', sql.Int, 0)
        .input('FROMINTEGTYPE', sql.SmallInt, 0)
        .input('FROMINTEGREF', sql.Int, 0)
        .input('EPRINTCNT', sql.SmallInt, 0)
        .input('CLNOTREFLACNTRREF', sql.Int, 0)
        .input('CLNOTREFLAACCREF', sql.Int, 0)
        .input('FORENTRUST', sql.SmallInt, 0)
        .input('PAYERCRKEY', sql.VarChar(51), '')
        .input('PAYERCRPROVIDER', sql.VarChar(51), '')
        .input('ORDFICHECMREF', sql.Int, 0)
        .input('ESENDTIME', sql.Int, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE (
            GRPCODE, TRCODE, IOCODE, FICHENO, DATE_, FTIME, DOCODE, INVNO, SPECODE, CYPHCODE,
            INVOICEREF, CLIENTREF, RECVREF, ACCOUNTREF, CENTERREF, PRODORDERREF, PORDERFICHENO,
            SOURCETYPE, SOURCEINDEX, SOURCEWSREF, SOURCEPOLNREF, SOURCECOSTGRP, DESTTYPE,
            DESTINDEX, DESTWSREF, DESTPOLNREF, DESTCOSTGRP, FACTORYNR, BRANCH, DEPARTMENT,
            COMPBRANCH, COMPDEPARTMENT, COMPFACTORY, PRODSTAT, DEVIR, CANCELLED, BILLED,
            ACCOUNTED, UPDCURR, INUSE, INVKIND, ADDDISCOUNTS, TOTALDISCOUNTS, TOTALDISCOUNTED,
            ADDEXPENSES, TOTALEXPENSES, TOTALDEPOZITO, TOTALPROMOTIONS, TOTALVAT, GROSSTOTAL,
            NETTOTAL, GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6, REPORTRATE,
            REPORTNET, EXTENREF, PAYDEFREF, PRINTCNT, FICHECNT, ACCFICHEREF,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREADEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC, SALESMANREF, CANCELLEDACC, SHPTYPCOD, SHPAGNCOD,
            TRACKNR, GENEXCTYP, LINEEXCTYP, TRADINGGRP, TEXTINC, SITEID, RECSTATUS,
            ORGLOGICREF, WFSTATUS, SHIPINFOREF, DISTORDERREF, SENDCNT, DLVCLIENT,
            DOCTRACKINGNR, ADDTAXCALC, TOTALADDTAX, UGIRTRACKINGNO, QPRODFCREF,
            VAACCREF, VACENTERREF, ORGLOGOID, FROMEXIM, FRGTYPCOD, TRCURR, TRRATE,
            TRNET, EXIMWHFCREF, EXIMFCTYPE, MAINSTFCREF, FROMORDWITHPAY, PROJECTREF,
            WFLOWCRDREF, STATUS, UPDTRCURR, TOTALEXADDTAX, AFFECTCOLLATRL, DEDUCTIONPART1,
            DEDUCTIONPART2, GRPFIRMTRANS, AFFECTRISK, DISPSTATUS, APPROVE, CANTCREDEDUCT,
            SHIPDATE, SHIPTIME, ENTRUSTDEVIR, RELTRANSFCREF, FROMTRANSFER, GUID, GLOBALID,
            COMPSTFCREF, COMPINVREF, TOTALSERVICES, CAMPAIGNCODE, OFFERREF, EINVOICETYP,
            EINVOICE, NOCALCULATE, PRODORDERTYP, QPRODFCTYP, PRDORDSLPLNRESERVE,
            CONTROLINFO, EDESPATCH, DOCTIME, EDESPSTATUS, PROFILEID, DELIVERYCODE,
            DESTSTATUS, CANCELEXP, UNDOEXP, CREATEWHERE, PUBLICBNACCREF, ACCEPTEINVPUBLIC,
            VATEXCEPTCODE, VATEXCEPTREASON, ATAXEXCEPTCODE, ATAXEXCEPTREASON, TAXFREECHX,
            MNTORDERFREF, PRINTEDDESPFCNO, OKCFICHE, NOTIFYCRDREF, CANCELLEDINVREF1,
            CANCELLEDINVREF2, CANCELLEDINVREF3, CANCELLEDINVREF4, FROMINTEGTYPE,
            FROMINTEGREF, EPRINTCNT, CLNOTREFLACNTRREF, CLNOTREFLAACCREF, FORENTRUST,
            PAYERCRKEY, PAYERCRPROVIDER, ORDFICHECMREF, ESENDTIME
          )
          VALUES (
            @GRPCODE, @TRCODE, @IOCODE, @FICHENO, @DATE_, @FTIME, @DOCODE, @INVNO, @SPECODE, @CYPHCODE,
            @INVOICEREF, @CLIENTREF, @RECVREF, @ACCOUNTREF, @CENTERREF, @PRODORDERREF, @PORDERFICHENO,
            @SOURCETYPE, @SOURCEINDEX, @SOURCEWSREF, @SOURCEPOLNREF, @SOURCECOSTGRP, @DESTTYPE,
            @DESTINDEX, @DESTWSREF, @DESTPOLNREF, @DESTCOSTGRP, @FACTORYNR, @BRANCH, @DEPARTMENT,
            @COMPBRANCH, @COMPDEPARTMENT, @COMPFACTORY, @PRODSTAT, @DEVIR, @CANCELLED, @BILLED,
            @ACCOUNTED, @UPDCURR, @INUSE, @INVKIND, @ADDDISCOUNTS, @TOTALDISCOUNTS, @TOTALDISCOUNTED,
            @ADDEXPENSES, @TOTALEXPENSES, @TOTALDEPOZITO, @TOTALPROMOTIONS, @TOTALVAT, @GROSSTOTAL,
            @NETTOTAL, @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6, @REPORTRATE,
            @REPORTNET, @EXTENREF, @PAYDEFREF, @PRINTCNT, @FICHECNT, @ACCFICHEREF,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREADEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC, @SALESMANREF, @CANCELLEDACC, @SHPTYPCOD, @SHPAGNCOD,
            @TRACKNR, @GENEXCTYP, @LINEEXCTYP, @TRADINGGRP, @TEXTINC, @SITEID, @RECSTATUS,
            @ORGLOGICREF, @WFSTATUS, @SHIPINFOREF, @DISTORDERREF, @SENDCNT, @DLVCLIENT,
            @DOCTRACKINGNR, @ADDTAXCALC, @TOTALADDTAX, @UGIRTRACKINGNO, @QPRODFCREF,
            @VAACCREF, @VACENTERREF, @ORGLOGOID, @FROMEXIM, @FRGTYPCOD, @TRCURR, @TRRATE,
            @TRNET, @EXIMWHFCREF, @EXIMFCTYPE, @MAINSTFCREF, @FROMORDWITHPAY, @PROJECTREF,
            @WFLOWCRDREF, @STATUS, @UPDTRCURR, @TOTALEXADDTAX, @AFFECTCOLLATRL, @DEDUCTIONPART1,
            @DEDUCTIONPART2, @GRPFIRMTRANS, @AFFECTRISK, @DISPSTATUS, @APPROVE, @CANTCREDEDUCT,
            @SHIPDATE, @SHIPTIME, @ENTRUSTDEVIR, @RELTRANSFCREF, @FROMTRANSFER, @GUID, @GLOBALID,
            @COMPSTFCREF, @COMPINVREF, @TOTALSERVICES, @CAMPAIGNCODE, @OFFERREF, @EINVOICETYP,
            @EINVOICE, @NOCALCULATE, @PRODORDERTYP, @QPRODFCTYP, @PRDORDSLPLNRESERVE,
            @CONTROLINFO, @EDESPATCH, @DOCTIME, @EDESPSTATUS, @PROFILEID, @DELIVERYCODE,
            @DESTSTATUS, @CANCELEXP, @UNDOEXP, @CREATEWHERE, @PUBLICBNACCREF, @ACCEPTEINVPUBLIC,
            @VATEXCEPTCODE, @VATEXCEPTREASON, @ATAXEXCEPTCODE, @ATAXEXCEPTREASON, @TAXFREECHX,
            @MNTORDERFREF, @PRINTEDDESPFCNO, @OKCFICHE, @NOTIFYCRDREF, @CANCELLEDINVREF1,
            @CANCELLEDINVREF2, @CANCELLEDINVREF3, @CANCELLEDINVREF4, @FROMINTEGTYPE,
            @FROMINTEGREF, @EPRINTCNT, @CLNOTREFLACNTRREF, @CLNOTREFLAACCREF, @FORENTRUST,
            @PAYERCRKEY, @PAYERCRPROVIDER, @ORDFICHECMREF, @ESENDTIME
          )
        `)

      // Get the inserted record's LOGICALREF
      const selectResult = await logoConnection.request()
        .input('FICHENO', sql.VarChar(17), stficheno)
        .query(`
          SELECT TOP 1 LOGICALREF, FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
          WHERE FICHENO = @FICHENO
          ORDER BY LOGICALREF DESC
        `)

      if (selectResult.recordset.length > 0 && selectResult.recordset[0]) {
        const insertedRecord = selectResult.recordset[0]
        consola.success(`Logo STFICHE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO,
        }
      }

      throw new Error('STFICHE tablosuna ekleme başarısız oldu - LOGICALREF alınamadı')
    }
    catch (error) {
      consola.error('Logo STFICHE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },
}

export default LogoErpStfiche
