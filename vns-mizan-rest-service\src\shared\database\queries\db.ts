/**
 * SQL queries for 'db' database initialization and operations
 */
export const query = `
BEGIN TRY
    -- 'users' tablosu yoksa oluştur
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' and xtype='U')
    BEGIN
        CREATE TABLE users (
            id INT PRIMARY KEY IDENTITY(1,1),
            email NVARCHAR(255) NOT NULL,
            password NVARCHAR(255) NOT NULL,
            full_name NVARCHAR(255) NOT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NOT NULL DEFAULT GETDATE()
        );

        CREATE UNIQUE INDEX IX_users_email ON users(email);
        PRINT 'Users tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'Users tablosu zaten mevcut';
    END;

    -- 'sessions' tablosu yoksa oluştur
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sessions' and xtype='U')
    BEGIN
        CREATE TABLE sessions (
            sid varchar(255) NOT NULL PRIMARY KEY,
            session nvarchar(max) NOT NULL,
            expires datetimeoffset(7) NOT NULL
        );
        
        CREATE INDEX IX_sessions_expires ON sessions(expires);
        PRINT 'Sessions tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'Sessions tablosu zaten mevcut';
    END;

    -- 'CariHesaplarSql' tablosu yoksa oluştur
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CariHesaplarSql' and xtype='U')
    BEGIN
        CREATE TABLE CariHesaplarSql (
            id INT IDENTITY(1,1) PRIMARY KEY,
            CARDTYPE INT,
            DEFINITION_ NVARCHAR(255),
            SPECODE NVARCHAR(50),
            ADDR1 NVARCHAR(200),
            ADDR2 NVARCHAR(200),
            CITY NVARCHAR(50),
            COUNTRY NVARCHAR(50),
            POSTCODE NVARCHAR(20),
            EMAILADDR NVARCHAR(100),
            CAPIBLOCK_CREATEDMIN INT,
            PURCHBRWS INT,
            SALESBRWS INT,
            IMPBRWS INT,
            EXPBRWS INT,
            FINBRWS INT,
            ISPERSCOMP INT,
            CODE NVARCHAR(32),
            TAXNR NVARCHAR(20),
            TCKNO NVARCHAR(20),
            NAME NVARCHAR(50),
            SURNAME NVARCHAR(50),
            INSTEADOFDESP INT,
            COUNTRYCODE NVARCHAR(10),
            TOWN NVARCHAR(50),
            -- All additional CLCARD fields, default 0
            ACTIVE INT DEFAULT 0,
            DISCRATE INT DEFAULT 0,
            EXTENREF INT DEFAULT 0,
            PAYMENTREF INT DEFAULT 0,
            WARNMETHOD INT DEFAULT 0,
            CLANGUAGE INT DEFAULT 0,
            BLOCKED INT DEFAULT 0,
            CCURRENCY INT DEFAULT 0,
            TEXTINC INT DEFAULT 0,
            SITEID INT DEFAULT 0,
            RECSTATUS INT DEFAULT 0,
            ORGLOGICREF INT DEFAULT 0,
            CAPIBLOCK_MODIFIEDBY INT DEFAULT 0,
            CAPIBLOCK_MODIFIEDHOUR INT DEFAULT 0,
            CAPIBLOCK_MODIFIEDMIN INT DEFAULT 0,
            CAPIBLOCK_MODIFIEDSEC INT DEFAULT 0,
            PAYMENTPROC INT DEFAULT 0,
            CRATEDIFFPROC INT DEFAULT 0,
            WFSTATUS INT DEFAULT 0,
            PPGROUPREF INT DEFAULT 0,
            ORDSENDMETHOD INT DEFAULT 0,
            DSPSENDMETHOD INT DEFAULT 0,
            INVSENDMETHOD INT DEFAULT 0,
            SUBSCRIBERSTAT INT DEFAULT 0,
            PAYMENTTYPE INT DEFAULT 0,
            LASTSENDREMLEV INT DEFAULT 0,
            EXTACCESSFLAGS INT DEFAULT 0,
            ORDSENDFORMAT INT DEFAULT 0,
            DSPSENDFORMAT INT DEFAULT 0,
            INVSENDFORMAT INT DEFAULT 0,
            REMSENDFORMAT INT DEFAULT 0,
            CLORDFREQ INT DEFAULT 0,
            ORDDAY INT DEFAULT 0,
            LIDCONFIRMED INT DEFAULT 0,
            EXPBUSTYPREF INT DEFAULT 0,
            INVPRINTCNT INT DEFAULT 0,
            PIECEORDINFLICT INT DEFAULT 0,
            COLLECTINVOICING INT DEFAULT 0,
            EBUSDATASENDTYPE INT DEFAULT 0,
            INISTATUSFLAGS INT DEFAULT 0,
            SLSORDERSTATUS INT DEFAULT 0,
            SLSORDERPRICE INT DEFAULT 0,
            LTRSENDMETHOD INT DEFAULT 0,
            LTRSENDFORMAT INT DEFAULT 0,
            IMAGEINC INT DEFAULT 0,
            SAMEITEMCODEUSE INT DEFAULT 0,
            WFLOWCRDREF INT DEFAULT 0,
            PARENTCLREF INT DEFAULT 0,
            LOWLEVELCODES2 INT DEFAULT 0,
            LOWLEVELCODES3 INT DEFAULT 0,
            LOWLEVELCODES4 INT DEFAULT 0,
            LOWLEVELCODES5 INT DEFAULT 0,
            LOWLEVELCODES6 INT DEFAULT 0,
            LOWLEVELCODES7 INT DEFAULT 0,
            LOWLEVELCODES8 INT DEFAULT 0,
            LOWLEVELCODES9 INT DEFAULT 0,
            LOWLEVELCODES10 INT DEFAULT 0,
            ADDTOREFLIST INT DEFAULT 0,
            TEXTREFTR INT DEFAULT 0,
            TEXTREFEN INT DEFAULT 0,
            ARPQUOTEINC INT DEFAULT 0,
            CLCRM INT DEFAULT 0,
            GRPFIRMNR INT DEFAULT 0,
            CONSCODEREF INT DEFAULT 0,
            OFFSENDMETHOD INT DEFAULT 0,
            OFFSENDFORMAT INT DEFAULT 0,
            EBANKNO INT DEFAULT 0,
            LOANGRPCTRL INT DEFAULT 0,
            LDXFIRMNR INT DEFAULT 0,
            EXTSENDMETHOD INT DEFAULT 0,
            EXTSENDFORMAT INT DEFAULT 0,
            CASHREF INT DEFAULT 0,
            USEDINPERIODS INT DEFAULT 0,
            RSKLIMCR INT DEFAULT 0,
            RSKDUEDATECR INT DEFAULT 0,
            RSKAGINGCR INT DEFAULT 0,
            RSKAGINGDAY INT DEFAULT 0,
            ACCEPTEINV INT DEFAULT 0,
            PROFILEID INT DEFAULT 0,
            PURCORDERSTATUS INT DEFAULT 0,
            PURCORDERPRICE INT DEFAULT 0,
            ISFOREIGN INT DEFAULT 0,
            SHIPBEGTIME1 INT DEFAULT 0,
            SHIPBEGTIME2 INT DEFAULT 0,
            SHIPBEGTIME3 INT DEFAULT 0,
            SHIPENDTIME1 INT DEFAULT 0,
            SHIPENDTIME2 INT DEFAULT 0,
            SHIPENDTIME3 INT DEFAULT 0,
            DBSLIMIT1 INT DEFAULT 0,
            DBSLIMIT2 INT DEFAULT 0,
            DBSLIMIT3 INT DEFAULT 0,
            DBSLIMIT4 INT DEFAULT 0,
            DBSLIMIT5 INT DEFAULT 0,
            DBSLIMIT6 INT DEFAULT 0,
            DBSLIMIT7 INT DEFAULT 0,
            DBSTOTAL1 INT DEFAULT 0,
            DBSTOTAL2 INT DEFAULT 0,
            DBSTOTAL3 INT DEFAULT 0,
            DBSTOTAL4 INT DEFAULT 0,
            DBSTOTAL5 INT DEFAULT 0,
            DBSTOTAL6 INT DEFAULT 0,
            DBSTOTAL7 INT DEFAULT 0,
            DBSBANKNO1 INT DEFAULT 0,
            DBSBANKNO2 INT DEFAULT 0,
            DBSBANKNO3 INT DEFAULT 0,
            DBSBANKNO4 INT DEFAULT 0,
            DBSBANKNO5 INT DEFAULT 0,
            DBSBANKNO6 INT DEFAULT 0,
            DBSBANKNO7 INT DEFAULT 0,
            DBSRISKCNTRL1 INT DEFAULT 0,
            DBSRISKCNTRL2 INT DEFAULT 0,
            DBSRISKCNTRL3 INT DEFAULT 0,
            DBSRISKCNTRL4 INT DEFAULT 0,
            DBSRISKCNTRL5 INT DEFAULT 0,
            DBSRISKCNTRL6 INT DEFAULT 0,
            DBSRISKCNTRL7 INT DEFAULT 0,
            DBSBANKCURRENCY1 INT DEFAULT 0,
            DBSBANKCURRENCY2 INT DEFAULT 0,
            DBSBANKCURRENCY3 INT DEFAULT 0,
            DBSBANKCURRENCY4 INT DEFAULT 0,
            DBSBANKCURRENCY5 INT DEFAULT 0,
            DBSBANKCURRENCY6 INT DEFAULT 0,
            DBSBANKCURRENCY7 INT DEFAULT 0,
            EINVOICETYPE INT DEFAULT 0,
            DUEDATECOUNT INT DEFAULT 0,
            DUEDATELIMIT INT DEFAULT 0,
            DUEDATETRACK INT DEFAULT 0,
            DUEDATECONTROL1 INT DEFAULT 0,
            DUEDATECONTROL2 INT DEFAULT 0,
            DUEDATECONTROL3 INT DEFAULT 0,
            DUEDATECONTROL4 INT DEFAULT 0,
            DUEDATECONTROL5 INT DEFAULT 0,
            DUEDATECONTROL6 INT DEFAULT 0,
            DUEDATECONTROL7 INT DEFAULT 0,
            DUEDATECONTROL8 INT DEFAULT 0,
            DUEDATECONTROL9 INT DEFAULT 0,
            DUEDATECONTROL10 INT DEFAULT 0,
            DUEDATECONTROL11 INT DEFAULT 0,
            DUEDATECONTROL12 INT DEFAULT 0,
            DUEDATECONTROL13 INT DEFAULT 0,
            DUEDATECONTROL14 INT DEFAULT 0,
            DUEDATECONTROL15 INT DEFAULT 0,
            CLOSEDATECOUNT INT DEFAULT 0,
            CLOSEDATETRACK INT DEFAULT 0,
            DEGACTIVE INT DEFAULT 0,
            DEGCURR INT DEFAULT 0,
            LABELINFO INT DEFAULT 0,
            DEFBNACCREF INT DEFAULT 0,
            PROJECTREF INT DEFAULT 0,
            DISCTYPE INT DEFAULT 0,
            SENDMOD INT DEFAULT 0,
            ISPERCURR INT DEFAULT 0,
            CURRATETYPE INT DEFAULT 0,
            EINVOICETYP INT DEFAULT 0,
            FBSSENDMETHOD INT DEFAULT 0,
            FBSSENDFORMAT INT DEFAULT 0,
            FBASENDMETHOD INT DEFAULT 0,
            FBASENDFORMAT INT DEFAULT 0,
            SECTORMAINREF INT DEFAULT 0,
            SECTORSUBREF INT DEFAULT 0,
            PERSONELCOSTS INT DEFAULT 0,
            FACTORYDIVNR INT DEFAULT 0,
            FACTORYNR INT DEFAULT 0,
            ININVENNR INT DEFAULT 0,
            OUTINVENNR INT DEFAULT 0,
            QTYDEPDURATION INT DEFAULT 0,
            QTYINDEPDURATION INT DEFAULT 0,
            OVERLAPTYPE INT DEFAULT 0,
            OVERLAPAMNT INT DEFAULT 0,
            OVERLAPPERC INT DEFAULT 0,
            BROKERCOMP INT DEFAULT 0,
            CREATEWHFICHE INT DEFAULT 0,
            EINVCUSTOM INT DEFAULT 0,
            SUBCONT INT DEFAULT 0,
            ORDPRIORITY INT DEFAULT 0,
            ACCEPTEDESP INT DEFAULT 0,
            PROFILEIDDESP INT DEFAULT 0,
            LABELINFODESP INT DEFAULT 0,
            ACCEPTEINVPUBLIC INT DEFAULT 0,
            PUBLICBNACCREF INT DEFAULT 0,
            PAYMENTPROCBRANCH INT DEFAULT 0,
            KVKKPERMSTATUS INT DEFAULT 0,
            KVKKANONYSTATUS INT DEFAULT 0,
            EXIMSENDMETHOD INT DEFAULT 0,
            EXIMSENDFORMAT INT DEFAULT 0,
            CLCCANDEDUCT INT DEFAULT 0,
            DRIVERREF INT DEFAULT 0,
            NOTIFYCRDREF INT DEFAULT 0,
            EXCNTRYTYP INT DEFAULT 0,
            EXCNTRYREF INT DEFAULT 0,
            IMCNTRYTYP INT DEFAULT 0,
            IMCNTRYREF INT DEFAULT 0,
            EXIMPAYTYPREF INT DEFAULT 0,
            EXIMBRBANKREF INT DEFAULT 0,
            EXIMCUSTOMREF INT DEFAULT 0,
            EXIMREGTYPREF INT DEFAULT 0,
            EXIMNTFYCLREF INT DEFAULT 0,
            EXIMCNSLTCLREF INT DEFAULT 0,
            EXIMFRGHTCLREF INT DEFAULT 0,
            DISPPRINTCNT INT DEFAULT 0,
            ORDPRINTCNT INT DEFAULT 0,
            CLPTYPEFORPPAYDT INT DEFAULT 0,
            CLSTYPEFORPPAYDT INT DEFAULT 0,
            createdAt DATETIME DEFAULT GETDATE(),
            updatedAt DATETIME DEFAULT GETDATE(),
            veritabani_id NVARCHAR(50),
            CYPHCODE NVARCHAR(255) DEFAULT '',
            TELNRS1 NVARCHAR(255) DEFAULT '',
            TELNRS2 NVARCHAR(255) DEFAULT '',
            FAXNR NVARCHAR(255) DEFAULT '',
            TAXOFFICE NVARCHAR(255) DEFAULT '',
            INCHARGE NVARCHAR(255) DEFAULT '',
            WEBADDR NVARCHAR(255) DEFAULT '',
            WARNEMAILADDR NVARCHAR(255) DEFAULT '',
            WARNFAXNR NVARCHAR(255) DEFAULT '',
            VATNR NVARCHAR(255) DEFAULT '',
            BANKBRANCHS1 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS2 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS3 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS4 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS5 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS6 NVARCHAR(255) DEFAULT '',
            BANKBRANCHS7 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS1 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS2 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS3 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS4 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS5 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS6 NVARCHAR(255) DEFAULT '',
            BANKACCOUNTS7 NVARCHAR(255) DEFAULT '',
            DELIVERYMETHOD NVARCHAR(255) DEFAULT '',
            DELIVERYFIRM NVARCHAR(255) DEFAULT '',
            EDINO NVARCHAR(255) DEFAULT '',
            TRADINGGRP NVARCHAR(255) DEFAULT '',
            PPGROUPCODE NVARCHAR(255) DEFAULT '',
            TAXOFFCODE NVARCHAR(255) DEFAULT '',
            TOWNCODE NVARCHAR(255) DEFAULT '',
            DISTRICTCODE NVARCHAR(255) DEFAULT '',
            DISTRICT NVARCHAR(255) DEFAULT '',
            CITYCODE NVARCHAR(255) DEFAULT '',
            ORDSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            ORDSENDFAXNR NVARCHAR(255) DEFAULT '',
            DSPSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            DSPSENDFAXNR NVARCHAR(255) DEFAULT '',
            INVSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            INVSENDFAXNR NVARCHAR(255) DEFAULT '',
            SUBSCRIBEREXT NVARCHAR(255) DEFAULT '',
            AUTOPAIDBANK NVARCHAR(255) DEFAULT '',
            STORECREDITCARDNO NVARCHAR(255) DEFAULT '',
            LOGOID NVARCHAR(255) DEFAULT '',
            EXPREGNO NVARCHAR(255) DEFAULT '',
            EXPDOCNO NVARCHAR(255) DEFAULT '',
            LTRSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            LTRSENDFAXNR NVARCHAR(255) DEFAULT '',
            CELLPHONE NVARCHAR(255) DEFAULT '',
            STATECODE NVARCHAR(255) DEFAULT '',
            STATENAME NVARCHAR(255) DEFAULT '',
            TELCODES1 NVARCHAR(255) DEFAULT '',
            TELCODES2 NVARCHAR(255) DEFAULT '',
            FAXCODE NVARCHAR(255) DEFAULT '',
            ORGLOGOID NVARCHAR(255) DEFAULT '',
            SPECODE2 NVARCHAR(255) DEFAULT '',
            SPECODE3 NVARCHAR(255) DEFAULT '',
            SPECODE4 NVARCHAR(255) DEFAULT '',
            SPECODE5 NVARCHAR(255) DEFAULT '',
            OFFSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            OFFSENDFAXNR NVARCHAR(255) DEFAULT '',
            BANKNAMES1 NVARCHAR(255) DEFAULT '',
            BANKNAMES2 NVARCHAR(255) DEFAULT '',
            BANKNAMES3 NVARCHAR(255) DEFAULT '',
            BANKNAMES4 NVARCHAR(255) DEFAULT '',
            BANKNAMES5 NVARCHAR(255) DEFAULT '',
            BANKNAMES6 NVARCHAR(255) DEFAULT '',
            BANKNAMES7 NVARCHAR(255) DEFAULT '',
            MAPID NVARCHAR(255) DEFAULT '',
            LONGITUDE NVARCHAR(255) DEFAULT '',
            LATITUTE NVARCHAR(255) DEFAULT '',
            CITYID NVARCHAR(255) DEFAULT '',
            TOWNID NVARCHAR(255) DEFAULT '',
            BANKIBANS1 NVARCHAR(255) DEFAULT '',
            BANKIBANS2 NVARCHAR(255) DEFAULT '',
            BANKIBANS3 NVARCHAR(255) DEFAULT '',
            BANKIBANS4 NVARCHAR(255) DEFAULT '',
            BANKIBANS5 NVARCHAR(255) DEFAULT '',
            BANKIBANS6 NVARCHAR(255) DEFAULT '',
            BANKIBANS7 NVARCHAR(255) DEFAULT '',
            EXTSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            EXTSENDFAXNR NVARCHAR(255) DEFAULT '',
            BANKBICS1 NVARCHAR(255) DEFAULT '',
            BANKBICS2 NVARCHAR(255) DEFAULT '',
            BANKBICS3 NVARCHAR(255) DEFAULT '',
            BANKBICS4 NVARCHAR(255) DEFAULT '',
            BANKBICS5 NVARCHAR(255) DEFAULT '',
            BANKBICS6 NVARCHAR(255) DEFAULT '',
            BANKBICS7 NVARCHAR(255) DEFAULT '',
            INCHARGE2 NVARCHAR(255) DEFAULT '',
            INCHARGE3 NVARCHAR(255) DEFAULT '',
            EMAILADDR2 NVARCHAR(255) DEFAULT '',
            EMAILADDR3 NVARCHAR(255) DEFAULT '',
            EINVOICEID NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY1 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY2 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY3 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY4 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY5 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY6 NVARCHAR(255) DEFAULT '',
            BANKBCURRENCY7 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC1 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC2 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC3 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC4 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC5 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC6 NVARCHAR(255) DEFAULT '',
            BANKCORRPACC7 NVARCHAR(255) DEFAULT '',
            BANKVOEN1 NVARCHAR(255) DEFAULT '',
            BANKVOEN2 NVARCHAR(255) DEFAULT '',
            BANKVOEN3 NVARCHAR(255) DEFAULT '',
            BANKVOEN4 NVARCHAR(255) DEFAULT '',
            BANKVOEN5 NVARCHAR(255) DEFAULT '',
            BANKVOEN6 NVARCHAR(255) DEFAULT '',
            BANKVOEN7 NVARCHAR(255) DEFAULT '',
            DEFINITION2 NVARCHAR(255) DEFAULT '',
            TELEXTNUMS1 NVARCHAR(255) DEFAULT '',
            TELEXTNUMS2 NVARCHAR(255) DEFAULT '',
            FAXEXTNUM NVARCHAR(255) DEFAULT '',
            FACEBOOKURL NVARCHAR(255) DEFAULT '',
            TWITTERURL NVARCHAR(255) DEFAULT '',
            APPLEID NVARCHAR(255) DEFAULT '',
            SKYPEID NVARCHAR(255) DEFAULT '',
            GLOBALID NVARCHAR(255) DEFAULT '',
            ADRESSNO NVARCHAR(255) DEFAULT '',
            POSTLABELCODE NVARCHAR(255) DEFAULT '',
            SENDERLABELCODE NVARCHAR(255) DEFAULT '',
            FBSSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            FBSSENDFAXNR NVARCHAR(255) DEFAULT '',
            FBASENDEMAILADDR NVARCHAR(255) DEFAULT '',
            FBASENDFAXNR NVARCHAR(255) DEFAULT '',
            EARCEMAILADDR1 NVARCHAR(255) DEFAULT '',
            EARCEMAILADDR2 NVARCHAR(255) DEFAULT '',
            EARCEMAILADDR3 NVARCHAR(255) DEFAULT '',
            POSTLABELCODEDESP NVARCHAR(255) DEFAULT '',
            SENDERLABELCODEDESP NVARCHAR(255) DEFAULT '',
            EXIMSENDEMAILADDR NVARCHAR(255) DEFAULT '',
            EXIMSENDFAXNR NVARCHAR(255) DEFAULT '',
            INCHTELCODES1 NVARCHAR(255) DEFAULT '',
            INCHTELCODES2 NVARCHAR(255) DEFAULT '',
            INCHTELCODES3 NVARCHAR(255) DEFAULT '',
            INCHTELNRS1 NVARCHAR(255) DEFAULT '',
            INCHTELNRS2 NVARCHAR(255) DEFAULT '',
            INCHTELNRS3 NVARCHAR(255) DEFAULT '',
            INCHTELEXTNUMS1 NVARCHAR(255) DEFAULT '',
            INCHTELEXTNUMS2 NVARCHAR(255) DEFAULT '',
            INCHTELEXTNUMS3 NVARCHAR(255) DEFAULT '',
            MERSISNO NVARCHAR(255) DEFAULT '',
            COMMRECORDNO NVARCHAR(255) DEFAULT '',
            WHATSAPPID NVARCHAR(255) DEFAULT '',
            LINKEDINURL NVARCHAR(255) DEFAULT '',
            INSTAGRAMURL NVARCHAR(255) DEFAULT '',
            GUID NVARCHAR(36) DEFAULT ''
        );
        PRINT 'CariHesaplarSql tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'CariHesaplarSql tablosu zaten mevcut';
    END;

    -- Tabloların oluşturulup oluşturulmadığını kontrol et
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' and xtype='U')
        THROW 50001, 'Users tablosu düzgün oluşturulmadı', 1;
    
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sessions' and xtype='U')
        THROW 50002, 'Sessions tablosu düzgün oluşturulmadı', 1;

    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CariHesaplarSql' and xtype='U')
        THROW 50003, 'CariHesaplarSql tablosu düzgün oluşturulmadı', 1;

    PRINT 'Veritabanı başlatma işlemi başarıyla tamamlandı';
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();

    PRINT 'Veritabanı başlatma işlemi başarısız oldu:';
    PRINT @ErrorMessage;
    
    THROW;
END CATCH;`
