import type { IResult } from 'mssql'
import consola from 'consola'
import sql from 'mssql'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

interface CostGroupResult {
  COSTGRP: number
}

/**
 * Service for warehouse-related Logo ERP operations
 */
const LogoWarehouseService = {
  /**
   * Fetches the COSTGRP from L_CAPIWHOUSE based on warehouse number.
   */
  getCostGroupFromWarehouse: async (
    warehouseNr: number | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (warehouseNr == null) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Use the new query format as specified
      const query = `
        SELECT COSTGRP
        FROM ${logoConfig.erp.logodb_master}..L_CAPIWHOUSE
        WHERE FIRMNR=${logoConfig.erp.firma_numarasi}
        AND NR=@warehouseNr
      `

      const result: IResult<CostGroupResult[]> = await logoConnection.request()
        .input('warehouseNr', sql.Int, warehouseNr)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].COSTGRP
      }
      consola.warn(`Ambar ${warehouseNr} için maliyet grubu bulunamadı, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Ambar ${warehouseNr} için maliyet grubu alınırken hata oluştu:`, error)
      return undefined
    }
  },
}

export default LogoWarehouseService
