import type { Request, Response, Router } from 'express'
import express from 'express'
import altGruplarRoutes from '../../alt-gruplar/routes/alt-gruplar-routes.ts'
import anaGruplarRoutes from '../../ana-gruplar/routes/ana-gruplar-routes.ts'
import bolumlerRoutes from '../../bolum-bilgileri/routes/bolum-routes.ts'
import cariPersonelRoutes from '../../cari-personel/routes/cari-personel-routes.ts'
import carilerRoutes from '../../cariler/routes/cariler-routes.ts'
import isyeriFabrikaAmbarRoutes from '../../isyeri-fabrika-ambar-bilgileri/routes/isyeri-fabrika-ambar-routes.ts'
import kategorilerRoutes from '../../kategoriler/routes/kategoriler-routes.ts'
import logoKullanicilariRoutes from '../../logo-kullanicilari/routes/logo-kullanicilari-routes.ts'
import logoVeritabaniBaglantilariRoutes from '../../logo-veritabani-baglantilari/routes/logo-veritabani-baglantilari-routes.ts'
import markalarRoutes from '../../markalar/routes/markalar-routes.ts'
import reyonlarRoutes from '../../reyonlar/routes/reyonlar-routes.ts'
import satinAlmaFaturalariRoutes from '../../satinalma-faturalari/routes/satinalma-faturalari-routes.ts'
import satisFaturalariRoutes from '../../satis-faturalari/routes/satis-faturalari-routes.ts'
import { isAuthenticated } from '../../shared/middleware/session-middleware.ts'
import stoklarRoutes from '../../stoklar/routes/stoklar-routes.ts'
import authRoutes from './auth-routes.ts'
import swaggerRoutes from './swagger-routes.ts'

/**
 * @openapi
 * /:
 *   get:
 *     tags: [System]
 *     summary: API durumunu ve versiyonunu döndürür
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: VNS Mizan Entegrasyon API
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 *                 timestamp:
 *                   type: string
 *                   example: 21.12.2023 14:30:45
 */

/**
 * Creates and configures the main API router
 * @returns {Router} Configured Express router
 */
export function createMainRouter(): Router {
  const router = express.Router()

  // Welcome route
  router.get('/', (req: Request, res: Response) => {
    res.json({
      message: 'VNS Mizan Entegrasyon API',
      version: '1.0.0',
      timestamp: new Date().toLocaleString('tr-TR', { timeZone: 'Europe/Istanbul' }),
    })
  })

  router.use('/auth', authRoutes)

  // Authenticated routes
  router.use('/cariler', isAuthenticated, carilerRoutes)
  router.use('/reyonlar', isAuthenticated, reyonlarRoutes)
  router.use('/alt-gruplar', isAuthenticated, altGruplarRoutes)
  router.use('/ana-gruplar', isAuthenticated, anaGruplarRoutes)
  router.use('/kategoriler', isAuthenticated, kategorilerRoutes)
  router.use('/markalar', isAuthenticated, markalarRoutes)
  router.use('/cari-personel', isAuthenticated, cariPersonelRoutes)
  router.use('/stoklar', isAuthenticated, stoklarRoutes)
  router.use('/logo-kullanicilari', isAuthenticated, logoKullanicilariRoutes)
  router.use('/logo-veritabani-baglantilari', isAuthenticated, logoVeritabaniBaglantilariRoutes)
  router.use('/isyeri-fabrika-ambar-bilgileri', isAuthenticated, isyeriFabrikaAmbarRoutes)
  router.use('/bolumler', isAuthenticated, bolumlerRoutes)
  router.use('/satis-faturalari', isAuthenticated, satisFaturalariRoutes)
  router.use('/satinalma-faturalari', isAuthenticated, satinAlmaFaturalariRoutes)

  // Move swagger routes to root level of v1
  router.use('/', swaggerRoutes)
  return router
}
