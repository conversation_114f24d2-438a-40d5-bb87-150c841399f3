import fs from 'node:fs/promises'
import path from 'node:path'
import { consola } from 'consola'
import iconv from 'iconv-lite'

/**
 * Cleans up a folder by deleting all files within it.
 * @param {string} folderPath - The path to the folder.
 */
export async function cleanupFolder(folderPath: string) {
  try {
    const files = await fs.readdir(folderPath)
    await Promise.all(files.map(file => fs.unlink(path.join(folderPath, file))))
    consola.success(`Klasör temizlendi: ${folderPath}`)
  }
  catch (error) {
    consola.error(`Klasör temizlenirken hata oluştu: ${folderPath}`, error)
  }
}

/**
 * Deletes a file at the specified path.
 * @param {string} filePath - The path to the file.
 */
export async function deleteFile(filePath: string) {
  try {
    await fs.unlink(filePath)
    consola.info(`Dosya silindi: ${filePath}`)
  }
  catch (error) {
    consola.error(`Dosya silinirken hata oluştu: ${filePath}`, error)
  }
}

/**
 * Ensures that a folder exists, creating it if necessary.
 * @param {string} folderPath - The path to the folder.
 */
export async function ensureFolderExists(folderPath: string) {
  try {
    await fs.mkdir(folderPath, { recursive: true })
  }
  catch (error) {
    if (error instanceof Error && 'code' in error && error.code !== 'EEXIST') {
      consola.error(`Klasör oluşturulurken hata oluştu: ${folderPath}`, error)
      throw error
    }
  }
}

/**
 * Gets all file names in a folder with specified extensions.
 * @param {object} params - The parameters object.
 * @param {string} params.folderPath - The path to the folder.
 * @param {Array<string>} params.extensions - The list of file extensions to filter by.
 * @returns {Array<string>} The list of file names with the specified extensions.
 */
export async function getFilesWithExtensions({ folderPath, extensions }: { folderPath: string, extensions: string[] }) {
  try {
    const files = await fs.readdir(folderPath)
    return files.filter(file => extensions.includes(path.extname(file).toLowerCase()))
  }
  catch (error) {
    consola.error(`Klasördeki dosyalar alınırken hata oluştu: ${folderPath}`, error)
    throw error
  }
}

/**
 * Creates a new file with the specified content.
 * @param {object} params - The parameters object.
 * @param {string} params.filePath - The path to the file.
 * @param {string} params.content - The content to write to the file.
 * @param {boolean} [params.encode] - Whether to encode the content in 'ISO-8859-9'.
 */
export async function createFile({ filePath, content, encode = true }: { filePath: string, content: string, encode: boolean }) {
  try {
    const fileContent = encode ? iconv.encode(content, 'ISO-8859-9') : content
    await fs.writeFile(filePath, fileContent, encode ? undefined : 'utf8')
    consola.success(`Dosya oluşturuldu: ${filePath}`)
  }
  catch (error) {
    consola.error(`Dosya oluşturulurken hata oluştu: ${filePath}`, error)
    throw error
  }
}

/**
 * Moves a file to another folder.
 * @param {object} params - The parameters object.
 * @param {string} params.sourcePath - The path to the source file.
 * @param {string} params.destinationFolder - The path to the destination folder.
 */
export async function moveFile({ sourcePath, destinationFolder }: { sourcePath: string, destinationFolder: string }) {
  try {
    const fileName = path.basename(sourcePath)
    const destinationPath = path.join(destinationFolder, fileName)
    await fs.rename(sourcePath, destinationPath)
    consola.success(`Dosya taşındı: ${sourcePath} -> ${destinationPath}`)
  }
  catch (error) {
    consola.error(`Dosya taşınırken hata oluştu: ${sourcePath} -> ${destinationFolder}`, error)
    throw error
  }
}

/**
 * Reads the content of a file.
 * @param {object} params - The parameters object.
 * @param {string} params.filePath - The path to the file.
 * @param {string} [params.encoding] - The encoding of the file.
 */
export async function readFile({ filePath, encoding = 'utf8' }: { filePath: string, encoding?: BufferEncoding }) {
  try {
    return await fs.readFile(filePath, encoding)
  }
  catch (error) {
    consola.error(`Dosya okunurken hata oluştu: ${filePath}`, error)
    throw error
  }
}
