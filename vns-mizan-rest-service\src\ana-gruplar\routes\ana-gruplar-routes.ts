import type { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getAnaGruplar } from '../services/ana-gruplar-service.ts'

const router = Router()

/**
 * Get ana gruplar validation schema
 */
const getAnaGruplarSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /ana-gruplar:
 *   get:
 *     tags:
 *       - Ana Gruplar
 *     summary: Logo veritabanından ana grupları listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 required: [id, kodu, adi, veritabani_id]
 *                 properties:
 *                   id:
 *                     type: number
 *                     description: Ana grubun benzersiz Logo ID'si
 *                   kodu:
 *                     type: string
 *                     description: Ana grup kodu
 *                   adi:
 *                     type: string
 *                     description: Ana grup adı
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
const getAnaGruplarHandler: RequestHandler = async (req, res) => {
  try {
    const result = getAnaGruplarSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const anaGruplar = await getAnaGruplar({ veritabaniId: veritabani_id })
    res.json(anaGruplar)
  }
  catch (error) {
    consola.error('Ana gruplar alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Ana gruplar alınırken bir hata oluştu',
    })
  }
}

router.get('/', getAnaGruplarHandler)

export default router
