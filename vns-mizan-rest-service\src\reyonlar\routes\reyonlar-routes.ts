import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getReyonlar } from '../services/reyonlar-service.ts'

const router = Router()

/**
 * Get reyonlar validation schema
 */
const getReyonlarSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /reyonlar:
 *   get:
 *     tags:
 *       - Reyonlar
 *     summary: Logo veritabanından reyon listesini getirir
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               title: ReyonlarResponse
 *               description: Logo veritabanından alınan reyon listesi
 *               type: array
 *               items:
 *                 type: object
 *                 required: [id, kodu, adi, veritabani_id]
 *                 properties:
 *                   id:
 *                     type: number
 *                     description: <PERSON><PERSON><PERSON> benzersiz <PERSON>go ID'si
 *                   kodu:
 *                     type: string
 *                     description: <PERSON>on kodu
 *                   adi:
 *                     type: string
 *                     description: Reyon adı
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz parametreler
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       401:
 *         description: Yetkisiz erişim
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
const getReyonlarHandler: RequestHandler = async (req, res) => {
  try {
    const result = getReyonlarSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const reyonlar = await getReyonlar({ veritabaniId: veritabani_id })
    res.json(reyonlar)
  }
  catch (error) {
    consola.error('Reyonlar alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Reyonlar alınırken bir hata oluştu',
    })
  }
}

router.get('/', getReyonlarHandler)

export default router
