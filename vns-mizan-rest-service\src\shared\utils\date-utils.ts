/**
 * Date and time utility functions
 */

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Converts a time string in HH:MM:SS format to total seconds
 *
 * @param timeStr Time string in format HH:MM:SS
 * @returns Total seconds (hours*3600 + minutes*60 + seconds)
 */
export function timeStringToSeconds(timeStr: string): number {
  const parts = timeStr.split(':')

  if (parts.length !== 3) {
    throw new Error('Zaman formatı HH:MM:SS olmalıdır')
  }

  const hours = Number.parseInt(parts[0]!, 10)
  const minutes = Number.parseInt(parts[1]!, 10)
  const seconds = Number.parseInt(parts[2]!, 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    throw new TypeError('Zaman formatı HH:MM:SS olmalıdır ve geçerli sayılar içermelidir')
  }

  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
    throw new Error('Geçersiz saat, dakika veya saniye değeri')
  }

  return hours * 3600 + minutes * 60 + seconds
}

/**
 * Converts seconds to a time string in HH:MM:SS format
 *
 * @param seconds Total seconds
 * @returns Time string in format HH:MM:SS
 */
export function secondsToTimeString(seconds: number): string {
  if (seconds < 0 || seconds > 86399) { // 23:59:59 = 86399 seconds
    throw new Error('Saniye değeri 0-86399 arasında olmalıdır')
  }

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * Converts HH:MM:SS time string to Logo integer format.
 * Formula: HH * 16777216 + MM * 65536 + SS * 256
 */
export function convertTimeToLogoInt(timeString: string | undefined): number {
  if (!timeString) {
    const now = new Date()
    const hours = now.getHours()
    const minutes = now.getMinutes()
    const seconds = now.getSeconds()
    return hours * 16777216 + minutes * 65536 + seconds * 256
  }

  // Validate time format
  if (!timeFormatRegex.test(timeString)) {
    throw new Error(`Geçersiz saat formatı: "${timeString}". Beklenen format: HH:MM:SS`)
  }

  const parts = timeString.split(':')
  const hours = Number.parseInt(parts[0]!, 10)
  const minutes = Number.parseInt(parts[1]!, 10)
  const seconds = Number.parseInt(parts[2]!, 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    throw new TypeError(`Geçersiz saat değerleri: "${timeString}"`)
  }

  return hours * 16777216 + minutes * 65536 + seconds * 256
}

/**
 * Validates time format (HH:MM:SS)
 */
export function validateTimeFormat(timeString: string): boolean {
  return timeFormatRegex.test(timeString)
}

/**
 * Gets current time in Logo integer format
 */
export function getCurrentTimeAsLogoInt(): number {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()
  return hours * 16777216 + minutes * 65536 + seconds * 256
}
