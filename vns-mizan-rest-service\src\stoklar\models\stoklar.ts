export interface StokBilgisi {
  id: number
  kodu: string
  adi: string
  anagrup: string
  anaGrupAdi: string
  altgrup: string
  altGrupAdi: string
  kategori: string
  kategoriAdi: string
  reyon: string
  reyonAdi: string
  marka: string
  markaAdi: string
  kisa_adi: string
  yabanci_adi: string
  create_date: Date
  update_date: Date
  pasif: boolean
  isk_yapilamaz: boolean
  iskonto: number
  fiyat_kasada_belirlenir: boolean
  toptan_vergi_yuzde: number
  perakende_vergi_yuzde: number
  detay_takip: boolean
  doviz: number
  doviz_adi: string
  malkabul_dursun: boolean
  mensei: string
  perakende_vergi: number
  perakende_vergi_adi: string
  satis_dursun: boolean
  siparis_dursun: boolean
  terazi_skt: boolean
  toptan_vergi: number
  toptan_vergi_adi: string
  veritabani_id: string
  yerli: boolean
  birimler: StokBirim[]
  fiyatlar: StokFiyat[]
}

export interface StokBirim {
  id: number
  stokId?: number
  birimSira: number
  birim: string
  katsayi: string
  barkodSira: number
  barkod: string
}

export interface StokFiyat {
  id: number
  stokId?: number
  listeNo: string
  listeAdi: string
  birim: number
  depoNo: number
  doviz: number
  dovizAdi: string
  kur: number
  satisFiyati: number
  kdvDurumu: boolean
}
