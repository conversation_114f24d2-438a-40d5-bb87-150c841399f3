{"name": "vns-mizan-rest-service", "type": "module", "version": "1.0.0", "description": "Mizan Rest Service", "author": "", "license": "ISC", "keywords": [], "main": "dist/index.js", "scripts": {"generate:openapi": "node -e \"import('./src/scripts/generate-openapi.ts').then(m => m.default())\"", "generate:certs": "node -e \"import('./src/scripts/generate-certificates.ts').then(m => m.default())\"", "dev": "pnpm generate:openapi && node index.ts", "dev:watch": "pnpm generate:openapi && node --watch index.ts", "dev:https": "pnpm generate:openapi && pnpm generate:certs && node index.ts", "lint": "eslint", "lint:fix": "eslint --fix", "build": "tsup", "build:pkg": "tsup && node --experimental-sea-config sea-config.json && node --experimental-sea-config cert-sea-config.json && node -e \"require('fs').copyFileSync(process.execPath, './dist/vns-mizan-rest-service.exe')\" && npx postject ./dist/vns-mizan-rest-service.exe NODE_SEA_BLOB ./dist/sea-prep.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2 && node -e \"require('fs').copyFileSync(process.execPath, './dist/vns-mizan-sertifika-olusturucu.exe')\" && npx postject ./dist/vns-mizan-sertifika-olusturucu.exe NODE_SEA_BLOB ./dist/sea-prep-cert.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2 && node -e \"require('fs').copyFileSync('./config.json', './dist/config.json')\" && node -e \"if (!require('fs').existsSync('./dist/certs')) require('fs').mkdirSync('./dist/certs'); try { require('fs').unlinkSync('./dist/sea-prep.blob'); } catch (e) {}; try { require('fs').unlinkSync('./dist/sea-prep-cert.blob'); } catch (e) {};\""}, "dependencies": {"@scalar/express-api-reference": "^0.7.3", "consola": "^3.4.2", "cors": "^2.8.5", "express": "^5.1.0", "express-session": "^1.18.1", "helmet": "^8.1.0", "iconv-lite": "^0.6.3", "morgan": "^1.10.0", "mssql": "^11.0.1", "node-forge": "^1.3.1", "nodemailer": "^6.10.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.24.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-session": "^1.18.1", "@types/morgan": "^1.9.9", "@types/mssql": "^9.1.7", "@types/node": "^22.15.3", "@types/node-forge": "^1.3.11", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "tsup": "^8.4.0", "typescript": "^5.8.3"}}