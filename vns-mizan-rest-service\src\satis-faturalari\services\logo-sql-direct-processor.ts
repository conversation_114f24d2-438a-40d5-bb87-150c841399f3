import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import DatabaseService from './database-service.ts'
import LogoErpInvoice from './logo-erp-invoice.ts'
import LogoErpStfiche from './logo-erp-stfiche.ts'
import LogoErpStline from './logo-erp-stline.ts'

/**
 * Service for processing direct SQL operations to Logo ERP
 * Used when use_rest=false
 */
const LogoSqlDirectProcessor = {

  processDirectSql: async ({
    invoice,
    veritabaniId,
    logoCredentials,
  }: {
    invoice: SatisFaturaInput
    veritabaniId: string
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ success: boolean, error?: string, logoRef?: number | undefined, ficheNo?: string, irsaliyeNo?: string }> => {
    let invoiceRef: number | undefined
    let ficheNo: string | undefined
    let stficheRef: number | undefined
    let stficheNo: string | undefined

    try {
      // Calculate totals from line items
      // Based on the example:
      // - TOTALDISCOUNTED: 223,64 (this is the net amount before VAT)
      // - TOTALVAT: 22,36
      // - GROSSTOTAL: 223,64 (this is the same as TOTALDISCOUNTED in the example)
      // - NETTOTAL: 246 (this is the total including VAT)

      let totalVat = 0
      let totalGross = 0
      let totalNet = 0
      let totalDiscounted = 0

      if (invoice.TRANSACTIONS?.items?.length) {
        for (const line of invoice.TRANSACTIONS.items) {
          const quantity = line.QUANTITY || 0
          const price = line.PRICE || 0
          const total = line.TOTAL || (quantity * price)
          const vatRate = line.VAT_RATE || 0
          const vatIncluded = line.VAT_INCLUDED || 0
          const discountRate = line.DISCOUNT_RATE || 0

          let lineVat = 0
          let lineNet = 0
          let lineDiscounted = 0
          let lineTotal = total

          // Apply discount if any
          if (discountRate > 0) {
            lineTotal = total * (1 - (discountRate / 100))
          }

          if (vatIncluded === 1) {
            // VAT included in price
            // Based on the example, for a total of 246 with VAT rate 10%:
            // lineDiscounted (VATMATRAH) = 223,64
            // lineVat = 22,36
            lineDiscounted = lineTotal / (1 + (vatRate / 100))
            lineVat = lineTotal - lineDiscounted
            lineNet = lineTotal // This is the total including VAT
          }
          else {
            // VAT not included
            lineDiscounted = lineTotal
            lineVat = lineTotal * (vatRate / 100)
            lineNet = lineTotal + lineVat
          }

          // Round to 2 decimal places to match the example
          lineVat = Math.round(lineVat * 100) / 100
          lineNet = Math.round(lineNet * 100) / 100
          lineDiscounted = Math.round(lineDiscounted * 100) / 100

          totalVat += lineVat
          totalNet += lineNet
          totalGross += lineDiscounted // GROSSTOTAL is the same as TOTALDISCOUNTED in the example
          totalDiscounted += lineDiscounted
        }
      }

      // Round totals to 2 decimal places to match the example
      totalVat = Math.round(totalVat * 100) / 100
      totalNet = Math.round(totalNet * 100) / 100
      totalGross = Math.round(totalGross * 100) / 100
      totalDiscounted = Math.round(totalDiscounted * 100) / 100

      // When use_rest=false, we need to calculate these fields ourselves
      // We don't update invoice.INVOICE directly because these fields don't exist in the SatisFaturaHeader interface
      // Instead, we'll pass the calculated totals to the insertLogoActualInvoice and insertLogoActualStfiche functions

      // 1. Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
      const invoiceResult = await LogoErpInvoice.insertLogoActualInvoice({
        invoice: invoice.INVOICE,
        veritabaniId,
        requestData: invoice.requestData,
        totals: {
          totalVat,
          totalNet,
          totalGross,
          totalDiscounted,
        },
        logoCredentials,
      })

      if (!invoiceResult.logicalref) {
        return { success: false, error: 'Logo INVOICE tablosuna ekleme başarısız oldu' }
      }
      invoiceRef = invoiceResult.logicalref
      ficheNo = invoiceResult.ficheno || ''

      // 2. Insert into actual Logo STFICHE table (LG_{FFF}_{DD}_STFICHE)
      const stficheResult = await LogoErpStfiche.insertLogoActualStfiche({
        invoice: invoice.INVOICE,
        invoiceRef: invoiceResult.logicalref!,
        ficheNo: invoiceResult.ficheno!,
        veritabaniId,
        requestData: invoice.requestData,
        totals: {
          totalVat,
          totalNet,
          totalGross,
          totalDiscounted,
        },
        logoCredentials,
      })

      if (!stficheResult.logicalref) {
        return { success: false, error: 'Logo STFICHE tablosuna ekleme başarısız oldu' }
      }
      stficheRef = stficheResult.logicalref
      stficheNo = stficheResult.ficheno

      // 3. Insert into actual Logo STLINE table (LG_{FFF}_{DD}_STLINE) for each transaction line
      if (invoice.TRANSACTIONS?.items?.length) {
        const stlinesSuccess = await LogoErpStline.insertLogoActualStlines({
          invoiceRef: invoiceResult.logicalref!,
          stficheRef: stficheResult.logicalref!,
          lines: invoice.TRANSACTIONS.items,
          veritabaniId,
          invoiceDate: invoice.INVOICE.DATE,
        })

        if (!stlinesSuccess) {
          return { success: false, error: 'Logo STLINE tablosuna ekleme başarısız oldu' }
        }
      }

      // When use_rest=false, we don't need to insert into LogoSatisFaturalari, LogoSatisFaturalariIrsaliyesi, LogoSatisFaturalariSatirlari tables
      // because these are for logging logo-rest requests.
      // We need to insert into SatisFaturalari and SatisFaturalariSatirlari tables to track the invoice in our application
      // and log the SQL operations in logo_sql_data column for audit trail.

      // First, insert into SatisFaturalari table
      let satisFaturaId: number | undefined
      try {
        // Extract request data from invoice.requestData
        const requestData = invoice.requestData
        if (!requestData) {
          throw new Error('Request data is missing')
        }

        // Insert into SatisFaturalari table
        const satisFaturaResult = await DatabaseService.insertSatisFatura({
          requestData,
          logoFaturaNo: ficheNo,
          logoIrsaliyeNo: stficheNo,
          logoFaturaLogicalRef: invoiceRef,
          logoIrsaliyeLogicalRef: stficheRef,
          veritabaniId,
        })

        if (!satisFaturaResult.success) {
          consola.error('SatisFaturalari tablosuna ekleme başarısız oldu:', satisFaturaResult.error)
        }
        else {
          satisFaturaId = satisFaturaResult.id

          // Insert into SatisFaturalariSatirlari table
          if (invoice.TRANSACTIONS?.items?.length && satisFaturaId) {
            // Convert TRANSACTIONS items to SatisFaturaSatirRequest format
            const faturaSatirlari = invoice.TRANSACTIONS.items.map((item) => {
              return {
                satir_turu: item.TYPE,
                malzeme_kodu: item.MASTER_CODE,
                ambar_kodu: item.SOURCEINDEX,
                fabrika_kodu: item.FACTORY,
                hareket_ozel_kodu: item.AUXIL_CODE,
                miktar: item.QUANTITY,
                indirim_tutari: 0, // Not available in item
                birim_fiyat: item.PRICE,
                para_birimi: 'TL', // Default
                dovizli_birim_fiyat: item.EDT_PRICE,
                doviz_kuru: item.TC_XRATE,
                aciklama: item.DESCRIPTION,
                indirim_orani: item.DISCOUNT_RATE,
                birim_kodu: item.UNIT_CODE,
                kdv_orani: item.VAT_RATE,
                satis_elemani: item.SALEMANCODE,
                proje_kodu: item.PROJECT_CODE,
              }
            })

            const satirlarResult = await DatabaseService.insertSatisFaturaSatirlari({
              faturaSatirlari,
              satisFaturaId,
            })

            if (!satirlarResult.success) {
              consola.error('SatisFaturalariSatirlari tablosuna ekleme başarısız oldu:', satirlarResult.error)
            }
          }
        }
      }
      catch (error) {
        consola.warn('SatisFaturalari tablosuna ekleme başarısız oldu:', error)
      }

      // Log the SQL operations in the main business tables for audit trail
      if (satisFaturaId) {
        try {
          // Get logo config for table names
          const logoConfig = await getLogoConfigById(veritabaniId)

          // Create SQL operation log for audit trail
          const sqlOperationLog = {
            invoice_operation: {
              table: `LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE`,
              logicalref: invoiceRef,
              ficheno: ficheNo,
              operation: 'INSERT',
              timestamp: new Date().toISOString(),
            },
            stfiche_operation: {
              table: `LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE`,
              logicalref: stficheRef,
              ficheno: stficheNo,
              operation: 'INSERT',
              timestamp: new Date().toISOString(),
            },
            stline_operations: invoice.TRANSACTIONS?.items?.map((item, index) => ({
              table: `LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STLINE`,
              line_number: index + 1,
              master_code: item.MASTER_CODE,
              quantity: item.QUANTITY,
              price: item.PRICE,
              operation: 'INSERT',
              timestamp: new Date().toISOString(),
            })) || [],
          }

          // Update SatisFaturalari with logo_sql_data
          const appConnection = await DbService.getConnection('db')
          await appConnection.request()
            .input('satis_fatura_id', sql.Int, satisFaturaId)
            .input('logo_sql_data', sql.VarChar(sql.MAX), JSON.stringify(sqlOperationLog))
            .query(`
              UPDATE SatisFaturalari
              SET logo_sql_data = @logo_sql_data,
                  updatedAt = GETDATE()
              WHERE id = @satis_fatura_id
            `)

          consola.success(`Logo SQL işlemleri SatisFaturalari tablosunda loglandı. Fatura ID: ${satisFaturaId}`)
        }
        catch (error: any) {
          consola.warn('Logo SQL işlemleri loglanamadı:', error.message || error)
        }
      }

      return {
        success: true,
        logoRef: invoiceRef,
        ficheNo,
        irsaliyeNo: stficheNo,
      }
    }
    catch (error: any) {
      consola.error('Doğrudan SQL fatura işlemi sırasında hata oluştu:', error)
      return { success: false, error: error.message, logoRef: invoiceRef }
    }
  },
}

export default LogoSqlDirectProcessor
