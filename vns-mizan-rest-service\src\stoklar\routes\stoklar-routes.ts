import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getStokBilgileri } from '../services/stoklar-service.ts'

const router = Router()

/**
 * Get stok bilgileri validation schema
 */
const getStokBilgileriSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /stoklar:
 *   get:
 *     tags: [Stoklar]
 *     summary: Logo veritabanından stok bilgilerini listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: number
 *                   kodu:
 *                     type: string
 *                   adi:
 *                     type: string
 *                   birimler:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         birim:
 *                           type: string
 *                         katsayi:
 *                           type: string
 *                         birimSira:
 *                           type: number
 *                         barkod:
 *                           type: string
 *                         barkodSira:
 *                           type: number
 *                   fiyatlar:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         listeNo:
 *                           type: string
 *                         listeAdi:
 *                           type: string
 *                         birim:
 *                           type: number
 *                         depoNo:
 *                           type: number
 *                         doviz:
 *                           type: number
 *                         dovizAdi:
 *                           type: string
 *                         kur:
 *                           type: number
 *                         satisFiyati:
 *                           type: number
 *                         kdvDurumu:
 *                           type: boolean
 *       400:
 *         description: Geçersiz istek
 *       500:
 *         description: Sunucu hatası
 */
const getStokBilgileriHandler: RequestHandler = async (req, res) => {
  try {
    const result = getStokBilgileriSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const stokBilgileri = await getStokBilgileri({ veritabaniId: veritabani_id })
    res.json(stokBilgileri)
  }
  catch (error) {
    consola.error('Stok bilgileri alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Stok bilgileri alınırken bir hata oluştu',
    })
  }
}

router.get('/', getStokBilgileriHandler)

export default router
