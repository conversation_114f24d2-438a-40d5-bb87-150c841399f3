import type { z } from 'zod'
import type {
  SatinalmaFaturaInput,
} from '../models/purchase-invoice.ts'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'
import { consola } from 'consola'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { convertTimeToLogoInt } from '../../shared/utils/date-utils.ts'
import { transformToRestFormat, transformToSqlFormat } from '../transformers/rest-transformer.ts'
import SatinalmaFaturaRestValidator from '../validators/rest-validator.ts'
import SatinalmaFaturaSqlValidator from '../validators/sql-validator.ts'
import SatinalmaFaturalariLogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

/**
 * Simplified Purchase Invoice Service using the new transformer pattern
 */

/**
 * Fatura yanıt tipi
 */
interface InvoiceResponse {
  status: string
  data?: any
  error?: string
  logoRef?: number
  ficheNo?: string
}

/**
 * REST API yanıt tipi
 */
interface RestApiResponse {
  INTERNAL_REFERENCE: number
}

const SatinalmaFaturalariService = {

  /**
   * Ana fatura işleme fonksiyonu
   */
  async handleCreateInvoice({
    invoiceData,
    veritabaniId,
    requestPayload,
  }: {
    invoiceData: SatinalmaFaturaInput
    veritabaniId: string
    requestPayload: z.infer<typeof satinalmaFaturalariSchema>
  }): Promise<InvoiceResponse> {
    // Girdi verilerini doğrula
    if (!invoiceData?.INVOICE || !veritabaniId || !requestPayload) {
      return { status: 'error', error: 'Geçersiz girdi verisi.' }
    }

    try {
      // Fatura satırlarını işle
      await SatinalmaFaturalariService.processInvoiceLineItems(invoiceData, requestPayload, veritabaniId)

      // REST API kullanılıp kullanılmayacağını belirle
      const useRest = await LogoSqlService.getUseRestFlag(veritabaniId)

      if (useRest) {
        return await SatinalmaFaturalariService.processWithRest({
          _invoiceData: invoiceData,
          veritabaniId,
          requestPayload,
        })
      }
      else {
        return await SatinalmaFaturalariService.processWithSql({
          _invoiceData: invoiceData,
          veritabaniId,
          requestPayload,
        })
      }
    }
    catch (error) {
      consola.error('Fatura oluşturulurken beklenmeyen bir hata oluştu:', error)
      return {
        status: 'error',
        error: `İşlem hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
      }
    }
  },

  /**
   * REST API ile fatura işleme
   */
  async processWithRest({
    _invoiceData,
    veritabaniId,
    requestPayload,
  }: {
    _invoiceData: SatinalmaFaturaInput
    veritabaniId: string
    requestPayload: z.infer<typeof satinalmaFaturalariSchema>
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for REST API
      const validationResult = await SatinalmaFaturaRestValidator.validateSatinalmaFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'REST doğrulama başarısız oldu',
        }
      }

      // Transform data for REST API
      const restData = await transformToRestFormat(requestPayload)

      // Process line items
      await SatinalmaFaturalariService.processInvoiceLineItems(restData, requestPayload, veritabaniId)

      // Send to Logo REST API
      const restResult = await SatinalmaFaturalariService.sendInvoiceToRestApi(
        restData,
        veritabaniId,
        requestPayload.logo,
      )

      // Save to local database
      const saveResult = await SatinalmaFaturalariService.saveInvoiceToLocalDb(
        requestPayload,
        restData,
        veritabaniId,
        restResult.logoRestError,
        restResult.ficheNo,
        restResult.logoRestApiResponse,
      )

      if (!saveResult.success) {
        return {
          status: 'error',
          error: saveResult.error,
        }
      }

      return {
        status: 'success',
        data: {
          message: restResult.logoRestError
            ? `Logo REST API'ye gönderilirken hata oluştu, ancak fatura yerel veritabanına başarıyla kaydedildi.`
            : `Fatura Logo REST API'ye başarıyla gönderildi ve yerel veritabanına kaydedildi.`,
          id: saveResult.satinalmaFaturaDbId,
          useRest: true,
          ficheNo: restResult.ficheNo,
        },
        error: restResult.logoRestError,
        logoRef: restResult.logoRestApiResponse?.INTERNAL_REFERENCE,
        ficheNo: restResult.ficheNo,
      }
    }
    catch (error: any) {
      consola.error('REST API fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'REST API işlemi başarısız oldu',
      }
    }
  },

  /**
   * Direct SQL ile fatura işleme
   */
  async processWithSql({
    _invoiceData,
    veritabaniId,
    requestPayload,
  }: {
    _invoiceData: SatinalmaFaturaInput
    veritabaniId: string
    requestPayload: z.infer<typeof satinalmaFaturalariSchema>
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for SQL integration
      const validationResult = await SatinalmaFaturaSqlValidator.validateSatinalmaFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'SQL doğrulama başarısız oldu',
        }
      }

      // Transform data for SQL integration
      const sqlData = await transformToSqlFormat(requestPayload)

      // Process line items
      await SatinalmaFaturalariService.processInvoiceLineItems(sqlData, requestPayload, veritabaniId)

      // Process with direct SQL
      const directSqlResult = await LogoSqlService.processDirectSql({
        invoice: sqlData,
        veritabaniId,
        logoCredentials: requestPayload.logo,
      })

      if (!directSqlResult.success) {
        return {
          status: 'error',
          error: directSqlResult.error || 'Doğrudan SQL entegrasyonu işlenemedi.',
          logoRef: directSqlResult.logoRef,
        }
      }

      // Fatura numarasını al
      const ficheNo = directSqlResult.ficheNo

      // Yerel veritabanına kaydet (logoRef ile birlikte)
      const saveResult = await SatinalmaFaturalariService.saveInvoiceToLocalDb(
        requestPayload,
        sqlData,
        veritabaniId,
        undefined, // logoRestError
        ficheNo,
        undefined, // logoRestApiResponse - skip REST API logging when use_rest=false
      )

      return {
        status: 'success',
        data: {
          message: 'Satınalma faturası doğrudan SQL ile başarıyla işlendi ve yerel veritabanına kaydedildi.',
          id: saveResult.satinalmaFaturaDbId,
          useRest: false,
          ficheNo,
        },
        logoRef: directSqlResult.logoRef,
        ficheNo,
      }
    }
    catch (error: any) {
      consola.error('SQL fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'SQL işlemi başarısız oldu',
      }
    }
  },

  /**
   * Fatura satırlarını işler ve maliyet grubu ve para birimi bilgilerini ayarlar
   */
  async processInvoiceLineItems(
    invoiceData: SatinalmaFaturaInput,
    requestPayload: z.infer<typeof satinalmaFaturalariSchema>,
    veritabaniId: string,
  ): Promise<void> {
    // Ana fatura için maliyet grubu ayarla
    if (invoiceData.INVOICE.SOURCE_WH) {
      const costGroup = await LogoSqlService.getSourceCostGrp({
        nr: invoiceData.INVOICE.SOURCE_WH,
        veritabaniId,
      })

      if (costGroup !== undefined) {
        invoiceData.INVOICE.SOURCE_COST_GRP = costGroup
      }
      else {
        consola.warn(`Ambar ${invoiceData.INVOICE.SOURCE_WH} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
        invoiceData.INVOICE.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP ?? 0
      }
    }

    // Satırlar için maliyet grubu ayarla
    if (invoiceData.TRANSACTIONS?.items) {
      for (const item of invoiceData.TRANSACTIONS.items) {
        if (item.SOURCEINDEX) {
          const costGroup = await LogoSqlService.getSourceCostGrp({
            nr: item.SOURCEINDEX,
            veritabaniId,
          })

          if (costGroup !== undefined) {
            item.SOURCE_COST_GRP = costGroup
          }
          else {
            consola.warn(`Ambar ${item.SOURCEINDEX} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
            item.SOURCE_COST_GRP = item.SOURCE_COST_GRP ?? 0
          }
        }
      }
    }

    // RC_XRATE (reporting currency exchange rate) hesapla
    try {
      // Firma raporlama para birimini al
      const firmRepCurr = await LogoLookupService.getFirmRepCurr(veritabaniId)
      if (firmRepCurr !== undefined) {
        // Fatura tarihine göre döviz kurunu al
        const exchangeRate = await LogoSqlService.getExchangeRateByType({
          date: requestPayload.tarihi,
          crtype: firmRepCurr,
          veritabaniId,
        })
        if (exchangeRate !== undefined) {
          invoiceData.INVOICE.RC_XRATE = exchangeRate
        }
        else {
          consola.warn(`Tarih: ${requestPayload.tarihi}, döviz: ${firmRepCurr} için döviz kuru bulunamadı, varsayılan olarak 0 kullanılıyor`)
          invoiceData.INVOICE.RC_XRATE = 0
        }
      }
    }
    catch (error) {
      consola.error('Raporlama para birimi kuru alınırken hata oluştu:', error)
      invoiceData.INVOICE.RC_XRATE = invoiceData.INVOICE.TC_XRATE // Fallback to TC_XRATE
    }
  },

  /**
   * Logo REST API ile fatura gönderimini gerçekleştirir
   */
  async sendInvoiceToRestApi(
    invoiceData: SatinalmaFaturaInput,
    veritabaniId: string,
    logoCredentials?: { kullanici_adi: string, sifre: string },
  ): Promise<{
      logoRestApiResponse?: RestApiResponse
      logoRestError?: string
      ficheNo?: string
    }> {
    let accessToken: string | null = null
    let logoRestApiResponse: RestApiResponse | undefined
    let logoRestError: string | undefined
    let ficheNo: string | undefined

    try {
      accessToken = await SatinalmaFaturalariLogoRestService.getToken({
        veritabaniId,
        logoCredentials,
      })
      logoRestApiResponse = await SatinalmaFaturalariLogoRestService.postSatinalmaFatura({
        accessToken,
        invoiceData,
        veritabaniId,
      })

      // LOGICALREF kullanarak FICHENO değerini al
      if (logoRestApiResponse?.INTERNAL_REFERENCE) {
        const invoiceInfo = await LogoSqlService.getInvoiceFichenoByLogicalref({
          logicalref: logoRestApiResponse.INTERNAL_REFERENCE,
          veritabaniId,
        })
        ficheNo = invoiceInfo?.ficheno
      }
    }
    catch (error) {
      logoRestError = error instanceof Error ? error.message : 'Bilinmeyen hata'
      consola.error('Logo REST API ile fatura gönderilirken hata oluştu:', error)
    }
    finally {
      // Token'ı iptal et
      if (accessToken) {
        try {
          await SatinalmaFaturalariLogoRestService.revokeToken({
            accessToken,
            veritabaniId,
          })
        }
        catch (error) {
          consola.error('Token iptal edilirken hata oluştu:', error)
        }
      }
    }

    return { logoRestApiResponse, logoRestError, ficheNo }
  },

  /**
   * Fatura verilerini yerel veritabanına kaydeder
   */
  async saveInvoiceToLocalDb(
    requestPayload: z.infer<typeof satinalmaFaturalariSchema>,
    invoiceData: SatinalmaFaturaInput,
    veritabaniId: string,
    logoRestError?: string,
    ficheNo?: string,
    logoRestApiResponse?: RestApiResponse,
  ): Promise<{
      success: boolean
      error?: string
      satinalmaFaturaDbId?: number
    }> {
    try {
      // Prepare JSON data for logging
      const invoiceJsonData = JSON.stringify(invoiceData.INVOICE)
      const stficheJsonData = logoRestApiResponse
        ? JSON.stringify({
            LOGICALREF: logoRestApiResponse.INTERNAL_REFERENCE,
            FICHENO: ficheNo,
            // Add other stfiche fields as needed
          })
        : undefined

      // Ana fatura bilgilerini kaydet
      const satinalmaFaturaResult = await LogoSqlService.insertSatinalmaFatura({
        requestData: requestPayload,
        errorMessage: logoRestError,
        logoFaturaNo: ficheNo,
        logoFaturaLogicalRef: logoRestApiResponse?.INTERNAL_REFERENCE,
        invoiceData: invoiceJsonData,
        stficheData: stficheJsonData,
        veritabaniId,
      })

      if (!satinalmaFaturaResult.success || !satinalmaFaturaResult.id) {
        return {
          success: false,
          error: satinalmaFaturaResult.error || 'Satınalma faturası kaydedilemedi',
        }
      }

      const satinalmaFaturaId = satinalmaFaturaResult.id

      // Fatura satırlarını kaydet
      if (requestPayload.fatura_satirlari && requestPayload.fatura_satirlari.length > 0) {
        // Prepare stline data for each line
        const stlineDataList = invoiceData.TRANSACTIONS?.items?.map(item => JSON.stringify(item)) || []

        const satirlarResult = await LogoSqlService.insertSatinalmaFaturaSatirlari({
          satinalmaFaturaId,
          satirlar: requestPayload.fatura_satirlari,
          stlineDataList,
        })

        if (!satirlarResult.success) {
          return {
            success: false,
            error: satirlarResult.error || 'Satınalma faturası satırları kaydedilemedi',
          }
        }
      }

      return {
        success: true,
        satinalmaFaturaDbId: satinalmaFaturaId,
      }
    }
    catch (error) {
      consola.error('Fatura verileri yerel veritabanına kaydedilirken hata oluştu:', error)
      return {
        success: false,
        error: `Veritabanı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
      }
    }
  },

  // Legacy function exports for backward compatibility
  convertRequestToLogoFormat: transformToRestFormat,
  convertTimeToLogoInt,
}

export default SatinalmaFaturalariService
