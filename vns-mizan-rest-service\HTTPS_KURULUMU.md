# HTTPS Kurulumu

Bu belge, VNS Mizan Entegrasyon API'sini HTTPS ile çalıştırmak için gerekli adımları açıklar.

## Geliştirme Ortamı İçin

Geliştirme ortamında kendinden imzalı sertifikalar kullanabilirsiniz:

1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>şturun:
   ```
   pnpm generate:certs
   ```
   veya
   ```
   dist/generate-certificates.exe
   ```

2. Oluşturulan sertifikalar `certs` klasöründe saklanır:
   - `certs/key.pem`: Özel anahtar
   - `certs/cert.pem`: Sertifika

3. `config.json` dosyasını güncelleyin:
   ```json
   {
     "project_settings": {
       "https": {
         "enabled": true,
         "key_path": "certs/key.pem",
         "cert_path": "certs/cert.pem"
       }
     }
   }
   ```

4. <PERSON><PERSON><PERSON><PERSON> ba<PERSON>n:
   ```
   pnpm dev
   ```

## <PERSON>retim Ortamı İçin

Üretim ortamında gerçek bir sertifika yetkilisi (CA) tarafından imzalanmış sertifikalar kullanmalısınız:

1. Bir sertifika yetkilisinden (Let's Encrypt, DigiCert, vb.) sertifika alın.

2. Sertifika ve özel anahtar dosyalarını güvenli bir konuma yerleştirin.

3. `config.json` dosyasını güncelleyin:
   ```json
   {
     "project_settings": {
       "https": {
         "enabled": true,
         "key_path": "/path/to/your/private-key.pem",
         "cert_path": "/path/to/your/certificate.pem"
       }
     }
   }
   ```

4. Sunucuyu başlatın.

## Güvenlik Notları

- Kendinden imzalı sertifikalar yalnızca geliştirme ortamı için uygundur.
- Üretim ortamında her zaman güvenilir bir sertifika yetkilisi tarafından imzalanmış sertifikalar kullanın.
- Özel anahtarınızı güvende tutun ve asla kaynak kontrol sistemine (git) dahil etmeyin.
- `certs` klasörü `.gitignore` dosyasına eklenmiştir.

## Sorun Giderme

### SSL Protokol Hatası

Eğer `ERR_SSL_PROTOCOL_ERROR` hatası alıyorsanız:

1. Sunucunun HTTPS ile çalıştığından emin olun.
2. Tarayıcınızın sertifikayı kabul ettiğinden emin olun (kendinden imzalı sertifikalar için güvenlik uyarısını kabul etmeniz gerekebilir).
3. Doğru port üzerinden eriştiğinizden emin olun.

### Sertifika Oluşturma Hatası

Eğer sertifika oluşturma sırasında hata alıyorsanız:

1. OpenSSL'in yüklü olduğundan emin olun.
2. Komut satırından `openssl version` komutunu çalıştırarak OpenSSL'in düzgün çalıştığını doğrulayın.
