/**
 * Interface for cari hesaplar request
 */
export interface CariHesapRequest {
  kodu: string
  vkVeyaTckNo: string
  unvan: string
  ad?: string
  soyad?: string
  adres: string
  il: string
  ilce: string
  ulke?: string
  ulkeKodu?: string
  email: string
  ozelKod?: string
  postaKodu?: string
}

/**
 * Interface for cari hesaplar response data from Logo
 */
export interface CariHesapResponseData {
  code: string
  vkVeyaTckNo: string
  logicalref: number
  isExisting: boolean
  veritabani_id: string
}

/**
 * Interface for Logo cari hesaplar data
 */
export interface LogoCariHesap {
  id?: number
  cariHesapId: number
  code: string
  title: string
  name?: string
  surname?: string
  address1?: string
  address2?: string
  town: string
  city: string
  country: string
  countryCode: string
  postalCode?: string
  auxilCode?: string
  email: string
  taxId?: string
  tcId?: string
  accountType: number
  acceptEInv?: number
  postLabel?: string
  senderLabel?: string
  profileId?: string
  insteadOfDispatch?: number
  createdAt?: Date
}

/**
 * Interface for Logo REST API cari hesaplar request
 */
export interface LogoCariHesapRequest {
  ACCOUNT_TYPE: number
  CODE: string
  TITLE: string
  NAME?: string
  SURNAME?: string
  // Kept for backward compatibility, but not used anymore:
  ADDRESS1?: string
  ADDRESS2?: string
  TOWN: string
  CITY: string
  COUNTRY: string
  COUNTRY_CODE: string
  POSTAL_CODE?: string
  AUXIL_CODE?: string
  E_MAIL: string
  TAX_ID?: string
  TCKNO?: string
  PERSCOMPANY?: number
  PURCHBRWS: number
  SALESBRWS: number
  IMPBRWS: number
  EXPBRWS: number
  FINBRWS: number
  ACCEPT_EINV?: number
  POST_LABEL?: string
  SENDER_LABEL?: string
  PROFILE_ID?: string
  INSTEAD_OF_DISPATCH?: number
  GUID?: string | null
}

/**
 * Type for parameters used in Logo CLCARD insert
 */
export interface ClCardParams {
  CARDTYPE: number
  DEFINITION_: string
  SPECODE: string
  ADDR1: string
  ADDR2: string
  CITY: string
  COUNTRY: string
  POSTCODE: string
  EMAILADDR: string
  CAPIBLOCK_CREATEDMIN: number
  PURCHBRWS: number
  SALESBRWS: number
  IMPBRWS: number
  EXPBRWS: number
  FINBRWS: number
  ISPERSCOMP: number
  CODE: string
  TAXNR: string
  TCKNO: string
  NAME: string
  SURNAME: string
  INSTEADOFDESP: number
  TOWN: string
  COUNTRYCODE: string
  GUID: string
  ACTIVE: number
  CYPHCODE: string
  TELNRS1: string
  TELNRS2: string
  FAXNR: string
  TAXOFFICE: string
  INCHARGE: string
  WEBADDR: string
  WARNEMAILADDR: string
  WARNFAXNR: string
  VATNR: string
  BANKBRANCHS1: string
  BANKBRANCHS2: string
  BANKBRANCHS3: string
  BANKBRANCHS4: string
  BANKBRANCHS5: string
  BANKBRANCHS6: string
  BANKBRANCHS7: string
  BANKACCOUNTS1: string
  BANKACCOUNTS2: string
  BANKACCOUNTS3: string
  BANKACCOUNTS4: string
  BANKACCOUNTS5: string
  BANKACCOUNTS6: string
  BANKACCOUNTS7: string
  DELIVERYMETHOD: string
  DELIVERYFIRM: string
  EDINO: string
  TRADINGGRP: string
  PPGROUPCODE: string
  TAXOFFCODE: string
  TOWNCODE: string
  DISTRICTCODE: string
  DISTRICT: string
  CITYCODE: string
  ORDSENDEMAILADDR: string
  ORDSENDFAXNR: string
  DSPSENDEMAILADDR: string
  DSPSENDFAXNR: string
  INVSENDEMAILADDR: string
  INVSENDFAXNR: string
  SUBSCRIBEREXT: string
  AUTOPAIDBANK: string
  STORECREDITCARDNO: string
  LOGOID: string
  EXPREGNO: string
  EXPDOCNO: string
  LTRSENDEMAILADDR: string
  LTRSENDFAXNR: string
  CELLPHONE: string
  STATECODE: string
  STATENAME: string
  TELCODES1: string
  TELCODES2: string
  FAXCODE: string
  ORGLOGOID: string
  SPECODE2: string
  SPECODE3: string
  SPECODE4: string
  SPECODE5: string
  OFFSENDEMAILADDR: string
  OFFSENDFAXNR: string
  BANKNAMES1: string
  BANKNAMES2: string
  BANKNAMES3: string
  BANKNAMES4: string
  BANKNAMES5: string
  BANKNAMES6: string
  BANKNAMES7: string
  MAPID: string
  LONGITUDE: string
  LATITUTE: string
  CITYID: string
  TOWNID: string
  BANKIBANS1: string
  BANKIBANS2: string
  BANKIBANS3: string
  BANKIBANS4: string
  BANKIBANS5: string
  BANKIBANS6: string
  BANKIBANS7: string
  EXTSENDEMAILADDR: string
  EXTSENDFAXNR: string
  BANKBICS1: string
  BANKBICS2: string
  BANKBICS3: string
  BANKBICS4: string
  BANKBICS5: string
  BANKBICS6: string
  BANKBICS7: string
  INCHARGE2: string
  INCHARGE3: string
  EMAILADDR2: string
  EMAILADDR3: string
  EINVOICEID: string
  BANKBCURRENCY1: string
  BANKBCURRENCY2: string
  BANKBCURRENCY3: string
  BANKBCURRENCY4: string
  BANKBCURRENCY5: string
  BANKBCURRENCY6: string
  BANKBCURRENCY7: string
  BANKCORRPACC1: string
  BANKCORRPACC2: string
  BANKCORRPACC3: string
  BANKCORRPACC4: string
  BANKCORRPACC5: string
  BANKCORRPACC6: string
  BANKCORRPACC7: string
  BANKVOEN1: string
  BANKVOEN2: string
  BANKVOEN3: string
  BANKVOEN4: string
  BANKVOEN5: string
  BANKVOEN6: string
  BANKVOEN7: string
  DEFINITION2: string
  TELEXTNUMS1: string
  TELEXTNUMS2: string
  FAXEXTNUM: string
  FACEBOOKURL: string
  TWITTERURL: string
  APPLEID: string
  SKYPEID: string
  GLOBALID: string
  ADRESSNO: string
  POSTLABELCODE: string
  SENDERLABELCODE: string
  FBSSENDEMAILADDR: string
  FBSSENDFAXNR: string
  FBASENDEMAILADDR: string
  FBASENDFAXNR: string
  EARCEMAILADDR1: string
  EARCEMAILADDR2: string
  EARCEMAILADDR3: string
  POSTLABELCODEDESP: string
  SENDERLABELCODEDESP: string
  EXIMSENDEMAILADDR: string
  EXIMSENDFAXNR: string
  INCHTELCODES1: string
  INCHTELCODES2: string
  INCHTELCODES3: string
  INCHTELNRS1: string
  INCHTELNRS2: string
  INCHTELNRS3: string
  INCHTELEXTNUMS1: string
  INCHTELEXTNUMS2: string
  INCHTELEXTNUMS3: string
  MERSISNO: string
  COMMRECORDNO: string
  WHATSAPPID: string
  LINKEDINURL: string
  INSTAGRAMURL: string
  DISCRATE: number
  EXTENREF: number
  PAYMENTREF: number
  WARNMETHOD: number
  CLANGUAGE: number
  BLOCKED: number
  CCURRENCY: number
  TEXTINC: number
  SITEID: number
  RECSTATUS: number
  ORGLOGICREF: number
  CAPIBLOCK_MODIFIEDBY: number
  CAPIBLOCK_MODIFIEDHOUR: number
  CAPIBLOCK_MODIFIEDMIN: number
  CAPIBLOCK_MODIFIEDSEC: number
  PAYMENTPROC: number
  CRATEDIFFPROC: number
  WFSTATUS: number
  PPGROUPREF: number
  ORDSENDMETHOD: number
  DSPSENDMETHOD: number
  INVSENDMETHOD: number
  SUBSCRIBERSTAT: number
  PAYMENTTYPE: number
  LASTSENDREMLEV: number
  EXTACCESSFLAGS: number
  ORDSENDFORMAT: number
  DSPSENDFORMAT: number
  INVSENDFORMAT: number
  REMSENDFORMAT: number
  CLORDFREQ: number
  ORDDAY: number
  LIDCONFIRMED: number
  EXPBUSTYPREF: number
  INVPRINTCNT: number
  PIECEORDINFLICT: number
  COLLECTINVOICING: number
  EBUSDATASENDTYPE: number
  INISTATUSFLAGS: number
  SLSORDERSTATUS: number
  SLSORDERPRICE: number
  LTRSENDMETHOD: number
  LTRSENDFORMAT: number
  IMAGEINC: number
  SAMEITEMCODEUSE: number
  WFLOWCRDREF: number
  PARENTCLREF: number
  LOWLEVELCODES2: number
  LOWLEVELCODES3: number
  LOWLEVELCODES4: number
  LOWLEVELCODES5: number
  LOWLEVELCODES6: number
  LOWLEVELCODES7: number
  LOWLEVELCODES8: number
  LOWLEVELCODES9: number
  LOWLEVELCODES10: number
  ADDTOREFLIST: number
  TEXTREFTR: number
  TEXTREFEN: number
  ARPQUOTEINC: number
  CLCRM: number
  GRPFIRMNR: number
  CONSCODEREF: number
  OFFSENDMETHOD: number
  OFFSENDFORMAT: number
  EBANKNO: number
  LOANGRPCTRL: number
  LDXFIRMNR: number
  EXTSENDMETHOD: number
  EXTSENDFORMAT: number
  CASHREF: number
  USEDINPERIODS: number
  RSKLIMCR: number
  RSKDUEDATECR: number
  RSKAGINGCR: number
  RSKAGINGDAY: number
  ACCEPTEINV: number
  PROFILEID: number
  PURCORDERSTATUS: number
  PURCORDERPRICE: number
  ISFOREIGN: number
  SHIPBEGTIME1: number
  SHIPBEGTIME2: number
  SHIPBEGTIME3: number
  SHIPENDTIME1: number
  SHIPENDTIME2: number
  SHIPENDTIME3: number
  DBSLIMIT1: number
  DBSLIMIT2: number
  DBSLIMIT3: number
  DBSLIMIT4: number
  DBSLIMIT5: number
  DBSLIMIT6: number
  DBSLIMIT7: number
  DBSTOTAL1: number
  DBSTOTAL2: number
  DBSTOTAL3: number
  DBSTOTAL4: number
  DBSTOTAL5: number
  DBSTOTAL6: number
  DBSTOTAL7: number
  DBSBANKNO1: number
  DBSBANKNO2: number
  DBSBANKNO3: number
  DBSBANKNO4: number
  DBSBANKNO5: number
  DBSBANKNO6: number
  DBSBANKNO7: number
  DBSRISKCNTRL1: number
  DBSRISKCNTRL2: number
  DBSRISKCNTRL3: number
  DBSRISKCNTRL4: number
  DBSRISKCNTRL5: number
  DBSRISKCNTRL6: number
  DBSRISKCNTRL7: number
  DBSBANKCURRENCY1: number
  DBSBANKCURRENCY2: number
  DBSBANKCURRENCY3: number
  DBSBANKCURRENCY4: number
  DBSBANKCURRENCY5: number
  DBSBANKCURRENCY6: number
  DBSBANKCURRENCY7: number
  EINVOICETYPE: number
  DUEDATECOUNT: number
  DUEDATELIMIT: number
  DUEDATETRACK: number
  DUEDATECONTROL1: number
  DUEDATECONTROL2: number
  DUEDATECONTROL3: number
  DUEDATECONTROL4: number
  DUEDATECONTROL5: number
  DUEDATECONTROL6: number
  DUEDATECONTROL7: number
  DUEDATECONTROL8: number
  DUEDATECONTROL9: number
  DUEDATECONTROL10: number
  DUEDATECONTROL11: number
  DUEDATECONTROL12: number
  DUEDATECONTROL13: number
  DUEDATECONTROL14: number
  DUEDATECONTROL15: number
  CLOSEDATECOUNT: number
  CLOSEDATETRACK: number
  DEGACTIVE: number
  DEGCURR: number
  LABELINFO: number
  DEFBNACCREF: number
  PROJECTREF: number
  DISCTYPE: number
  SENDMOD: number
  ISPERCURR: number
  CURRATETYPE: number
  EINVOICETYP: number
  FBSSENDMETHOD: number
  FBSSENDFORMAT: number
  FBASENDMETHOD: number
  FBASENDFORMAT: number
  SECTORMAINREF: number
  SECTORSUBREF: number
  PERSONELCOSTS: number
  FACTORYDIVNR: number
  FACTORYNR: number
  ININVENNR: number
  OUTINVENNR: number
  QTYDEPDURATION: number
  QTYINDEPDURATION: number
  OVERLAPTYPE: number
  OVERLAPAMNT: number
  OVERLAPPERC: number
  BROKERCOMP: number
  CREATEWHFICHE: number
  EINVCUSTOM: number
  SUBCONT: number
  ORDPRIORITY: number
  ACCEPTEDESP: number
  PROFILEIDDESP: number
  LABELINFODESP: number
  ACCEPTEINVPUBLIC: number
  PUBLICBNACCREF: number
  PAYMENTPROCBRANCH: number
  KVKKPERMSTATUS: number
  KVKKANONYSTATUS: number
  EXIMSENDMETHOD: number
  EXIMSENDFORMAT: number
  CLCCANDEDUCT: number
  DRIVERREF: number
  NOTIFYCRDREF: number
  EXCNTRYTYP: number
  EXCNTRYREF: number
  IMCNTRYTYP: number
  IMCNTRYREF: number
  EXIMPAYTYPREF: number
  EXIMBRBANKREF: number
  EXIMCUSTOMREF: number
  EXIMREGTYPREF: number
  EXIMNTFYCLREF: number
  EXIMCNSLTCLREF: number
  EXIMFRGHTCLREF: number
  DISPPRINTCNT: number
  ORDPRINTCNT: number
  CLPTYPEFORPPAYDT: number
  CLSTYPEFORPPAYDT: number
}

export interface InsertCariHesaplarParams {
  kodu: string
  vkVeyaTckNo: string
  unvan: string
  ad?: string
  soyad?: string
  adres: string
  il: string
  ilce: string
  ulke?: string
  ulkeKodu?: string
  email: string
  postaKodu?: string
  ozelKod?: string
  logoRef?: number
}

export interface UpdateCariHesaplarLogoRefParams {
  id: number
  logoRef: number
}

export interface UpdateCariHesaplarErrorParams {
  id: number
  error: string
}
