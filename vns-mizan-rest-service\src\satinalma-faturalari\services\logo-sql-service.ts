import DatabaseService from './database-service.ts'
import LogoSqlDirectProcessor from './logo-sql-direct-processor.ts'
import LogoSqlLookup from './logo-sql-lookup.ts'
/**
 * Simplified Logo SQL Service that coordinates other services for purchase invoices
 * This replaces the massive original logo-sql-service.ts file
 */
import LogoSqlValidation from './logo-sql-validation.ts'

const LogoSqlService = {
  // Re-export validation functions
  getUseRestFlag: LogoSqlValidation.getUseRestFlag,
  validateSatinalmaFaturaRequest: LogoSqlValidation.validateSatinalmaFaturaRequest,

  // Re-export lookup functions
  getSourceCostGrp: LogoSqlLookup.getSourceCostGrp,
  getInvoiceFichenoByLogicalref: LogoSqlLookup.getInvoiceFichenoByLogicalref,
  getExchangeRateByType: LogoSqlLookup.getExchangeRateByType,

  // Re-export direct processor
  processDirectSql: LogoSqlDirectProcessor.processDirectSql,

  // Re-export database service functions
  insertSatinalmaFatura: DatabaseService.insertSatinalmaFatura,
  insertSatinalmaFaturaSatirlari: DatabaseService.insertSatinalmaFaturaSatirlari,
}

export default LogoSqlService
