import type { CariHesapRequest, LogoCariHesapRequest } from '../models/types.ts'

/**
 * Transforms Mizan customer data to REST API format for Logo REST API integration
 * Used when use_rest=true
 */
export async function transformToRestFormat(cariHesapData: CariHesapRequest): Promise<LogoCariHesapRequest> {
  // Determine if this is a person or company
  const isPerson = cariHesapData.vkVeyaTckNo && cariHesapData.vkVeyaTckNo.length === 11

  return {
    CODE: cariHesapData.kodu,
    TITLE: cariHesapData.unvan || `${cariHesapData.ad || ''} ${cariHesapData.soyad || ''}`.trim(),
    NAME: cariHesapData.ad || '',
    SURNAME: cariHesapData.soyad || '',
    ADDRESS1: cariHesapData.adres || '',
    ADDRESS2: '', // Not provided in request
    TOWN: cariHesapData.ilce || '',
    CITY: cariHesapData.il || '',
    COUNTRY: cariHesapData.ulke || 'TÜRKİYE',
    COUNTRY_CODE: cariHesapData.ulkeKodu || 'TR',
    POSTAL_CODE: cariHesapData.postaKodu || '',
    AUXIL_CODE: cariHesapData.ozelKod || '',
    E_MAIL: cariHesapData.email || '',
    TAX_ID: isPerson ? '' : cariHesapData.vkVeyaTckNo || '',
    TCKNO: isPerson ? cariHesapData.vkVeyaTckNo || '' : '',
    ACCOUNT_TYPE: 3, // Default customer type
    PERSCOMPANY: isPerson ? 1 : 0, // 1 for person, 0 for company
    ACCEPT_EINV: 1, // Default to accept e-invoice
    POST_LABEL: '', // Not provided in request
    SENDER_LABEL: '', // Not provided in request
    PROFILE_ID: '2', // Default profile
    INSTEAD_OF_DISPATCH: 1, // Default value

    // REST API specific fields - only include properties that exist in LogoCariHesapRequest
    PURCHBRWS: 1, // Allow purchase browsing
    SALESBRWS: 1, // Allow sales browsing
    IMPBRWS: 1, // Allow import browsing
    EXPBRWS: 1, // Allow export browsing
    FINBRWS: 1, // Allow finance browsing
  }
}

/**
 * Transforms Mizan customer data to SQL format for direct database integration
 * Used when use_rest=false
 */
export async function transformToSqlFormat(cariHesapData: CariHesapRequest): Promise<LogoCariHesapRequest> {
  // For SQL integration, we use the same transformation as REST
  // but may have different field mappings in the future
  return await transformToRestFormat(cariHesapData)
}
