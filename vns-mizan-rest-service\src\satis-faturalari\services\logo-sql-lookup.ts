import { consola } from 'consola'
import DbService from '../../shared/services/db-service.ts'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo SQL lookup operations
 * Contains functions that query Logo ERP for reference data
 */
const LogoSqlLookup = {

  // Re-export shared exchange rate functions
  getExchangeRate: LogoSqlUtils.getExchangeRate,
  getExchangeRateByType: LogoSqlUtils.getExchangeRateByType,

  getUnitCodeForItem: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const query = `
          SELECT unitsetl.CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS as items
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
              ON items.UNITSETREF = unitsetf.LOGICALREF
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
              ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
              AND unitsetl.MAINUNIT = 1
          WHERE items.CODE = @code
      `

      const { recordset } = await logoConnection.request().input('code', code).query(query)

      return recordset?.[0]?.CODE || 'ADET'
    }
    catch (error) {
      consola.error('Malzeme birim kodu alınırken hata oluştu:', error)
      return 'ADET'
    }
  },

  getUnitCodeForSrvcard: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const query = `
          SELECT unitsetl.CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
              ON srvcard.UNITSETREF = unitsetf.LOGICALREF
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
              ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
              AND unitsetl.MAINUNIT = 1
          WHERE srvcard.CODE = @code
      `

      const { recordset } = await logoConnection.request().input('code', code).query(query)

      return recordset?.[0]?.CODE || 'ADET'
    }
    catch (error) {
      consola.error('Hizmet birim seti kodu alınırken hata oluştu:', error)
      return 'ADET'
    }
  },

  getItemCode: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS
            WHERE CODE = @code
        `)

      if (!recordset || recordset.length === 0) {
        throw new Error(`"${code}" kodlu malzeme Logo'da bulunamadı. Lütfen malzeme kartını kontrol edin.`)
      }

      return recordset[0].CODE
    }
    catch (error) {
      consola.error('Malzeme kodu alınırken hata oluştu:', error)
      throw error
    }
  },

  getCustomerVknOrTckn: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string | undefined> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('arpCode', code)
        .query<{ vknoveyatckno: string }>(`
            SELECT IIF(clcard.ISPERSCOMP=1, clcard.TCKNO, clcard.TAXNR) as vknoveyatckno
            FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD as clcard
            WHERE clcard.CODE = @arpCode
        `)

      if (!recordset || recordset.length === 0) {
        throw new Error('Cari hesap sistemde kayıtlı değil')
      }

      return recordset[0]?.vknoveyatckno
    }
    catch (error) {
      consola.error('Cari hesap VKN/TCKN bilgisi alınırken hata oluştu:', error)
      throw error
    }
  },

  getInvoiceFichenoByLogicalref: async ({ logicalref, veritabaniId }: { logicalref: number, veritabaniId: string }): Promise<{ ficheno: string, logicalref: number } | undefined> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('logicalref', logicalref)
        .query(`
          SELECT FICHENO, LOGICALREF
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE LOGICALREF = @logicalref
        `)

      if (recordset && recordset.length > 0) {
        return {
          ficheno: recordset[0].FICHENO,
          logicalref: recordset[0].LOGICALREF,
        }
      }

      return undefined
    }
    catch (error) {
      consola.error(`Fatura numarası Logo'dan alınırken hata oluştu:`, error)
      return undefined
    }
  },

  // Re-export shared source cost group function
  getSourceCostGrp: LogoSqlUtils.getSourceCostGrp,
}

export default LogoSqlLookup
