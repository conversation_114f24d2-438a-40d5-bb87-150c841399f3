import type { CariHesapRequest, CariHesapResponseData, LogoCariHesapRequest } from '../models/types.ts'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import { transformToRestFormat, transformToSqlFormat } from '../transformers/rest-transformer.ts'
import LogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

/**
 * Simplified Customer Service using the new transformer pattern
 */
const CariHesaplarService = {

  /**
   * Main customer processing function
   */
  async sendCariHesap({
    cariHesapData,
    veritabaniId,
  }: {
    cariHesapData: CariHesapRequest
    veritabaniId: string
  }): Promise<CariHesapResponseData> {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)

      // 1. Check if customer already exists
      const existingCard = await CariHesaplarService.checkExistingCariHesap({
        code: cariHesapData.kodu,
        veritabaniId,
      })
      if (existingCard) {
        return existingCard
      }

      // 2. Process based on REST flag
      return logoConfig.erp.rest_settings.use_rest
        ? await CariHesaplarService.processWithRest({ cariHesapData, veritabaniId })
        : await CariHesaplarService.processWithSql({ cariHesapData, veritabaniId, logoConfig })
    }
    catch (error) {
      consola.error('Cari kart gönderilirken hata oluştu:', error)
      throw error
    }
  },

  /**
   * Check if customer already exists
   */
  async checkExistingCariHesap({
    code,
    veritabaniId,
  }: {
    code: string
    veritabaniId: string
  }): Promise<CariHesapResponseData | null> {
    const existingCard = await LogoSqlService.getcariHesapDetails({
      code,
      veritabaniId,
    })

    if (existingCard) {
      return {
        code: existingCard.code,
        vkVeyaTckNo: existingCard.vkVeyaTckNo,
        logicalref: existingCard.logicalref,
        isExisting: true,
        veritabani_id: veritabaniId,
      }
    }

    return null
  },

  /**
   * Process with REST API integration
   */
  async processWithRest({
    cariHesapData,
    veritabaniId,
  }: {
    cariHesapData: CariHesapRequest
    veritabaniId: string
  }): Promise<CariHesapResponseData> {
    // Create cariHesap record first - without LOGO references
    const cardId = await CariHesaplarService.insertCariHesaplar({
      cariHesapData,
      veritabaniId,
      logicalref: 0,
    })
    let accessToken = null
    let logicalref = null
    let logoRestLogData = ''
    let readyCustomerToPost: LogoCariHesapRequest | null = null

    try {
      // Transform to Logo REST format
      readyCustomerToPost = await transformToRestFormat(cariHesapData)

      // Get a token from Logo REST API
      accessToken = await LogoRestService.getToken({ veritabaniId })

      // Send the cari hesaplar to Logo REST API
      const response = await LogoRestService.postCariHesap({
        accessToken,
        cariHesap: readyCustomerToPost,
        veritabaniId,
      })
      logicalref = response.INTERNAL_REFERENCE

      // Create REST log data for this operation (inline implementation)
      logoRestLogData = JSON.stringify({
        endpoint: '/Arps',
        method: 'POST',
        timestamp: new Date().toISOString(),
        payload: readyCustomerToPost,
        response_status: 200,
        response_data: response,
      }, null, 2)

      // Update the card record with logo reference
      await LogoSqlService.updateCariHesaplarLogoRef({
        veritabaniId,
        params: { id: cardId, logoRef: logicalref },
      })

      // Update with REST log data
      await CariHesaplarService.updateCariHesapWithLogData({
        cardId,
        veritabaniId,
        logoRestLogData,
      })

      // Save Logo Cari hesaplar data
      await CariHesaplarService.saveLogoCariHesapData({
        cardId,
        readyCustomerToPost,
        veritabaniId,
      })

      return {
        code: cariHesapData.kodu,
        vkVeyaTckNo: cariHesapData.vkVeyaTckNo,
        logicalref,
        isExisting: false,
        veritabani_id: veritabaniId,
      }
    }
    catch (error) {
      // Hata durumunda da loglama verisini kaydet (inline implementation)
      const errorLogData = JSON.stringify({
        endpoint: '/Arps',
        method: 'POST',
        timestamp: new Date().toISOString(),
        payload: readyCustomerToPost || null,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata',
      }, null, 2)

      await CariHesaplarService.updateCariHesapWithLogData({
        cardId,
        veritabaniId,
        logoRestLogData: errorLogData,
      })

      await CariHesaplarService.handleRestError({ cardId, error, veritabaniId })
      throw error
    }
    finally {
      if (accessToken) {
        try {
          await LogoRestService.revokeToken({ accessToken, veritabaniId })
        }
        catch (revokeError) {
          consola.error('Token revoke error:', revokeError)
        }
      }
    }
  },

  /**
   * Process with direct SQL integration
   */
  async processWithSql({
    cariHesapData,
    veritabaniId,
    logoConfig,
  }: {
    cariHesapData: CariHesapRequest
    veritabaniId: string
    logoConfig: any
  }): Promise<CariHesapResponseData> {
    // Transform to Logo SQL format
    const readyCustomerToPost = await transformToSqlFormat(cariHesapData)

    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const dbConnection = await DbService.getConnection('db')
    const guid = await LogoSqlService.generateUniqueGuid(logoConnection, dbConnection, logoConfig.erp.firma_numarasi)

    // Set default values and prepare params for CLCARD insert
    const clcardParams = CariHesaplarService.prepareClCardParams({
      cariHesapData,
      readyCustomerToPost,
      guid,
    })

    // Store record in CariHesaplarSql for tracking
    await LogoSqlService.insertCariHesaplarSql({
      veritabaniId,
      params: {
        ...clcardParams,
        veritabani_id: veritabaniId,
      },
    })

    // Insert to CLCARD table
    const logicalref = await LogoSqlService.insertClCard({ veritabaniId, params: clcardParams })

    // Create SQL log data for this operation (inline implementation)
    let logoSqlLogData: string
    try {
      const firmaNo = logoConfig.erp.firma_numarasi
      const timestamp = new Date().toISOString()

      const logData = {
        [`LG_${firmaNo}_CLCARD`]: {
          ...clcardParams,
          operation: 'INSERT',
          timestamp,
        },
      }

      logoSqlLogData = JSON.stringify(logData, null, 2)
    }
    catch (error) {
      const errorLogData = {
        error: `Logo SQL log data oluşturulurken hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        timestamp: new Date().toISOString(),
        original_data: clcardParams,
      }
      logoSqlLogData = JSON.stringify(errorLogData, null, 2)
    }

    // Insert into CariHesaplar with correct logoRef and SQL log data
    await CariHesaplarService.insertCariHesaplar({
      cariHesapData,
      veritabaniId,
      logicalref,
      logoSqlLogData,
    })

    return {
      code: cariHesapData.kodu,
      vkVeyaTckNo: cariHesapData.vkVeyaTckNo,
      logicalref,
      isExisting: false,
      veritabani_id: veritabaniId,
    }
  },

  /**
   * Insert customer into application database
   */
  async insertCariHesaplar({
    cariHesapData,
    veritabaniId,
    logicalref,
    logoRestLogData,
    logoSqlLogData,
  }: {
    cariHesapData: CariHesapRequest
    veritabaniId: string
    logicalref: number
    logoRestLogData?: string
    logoSqlLogData?: string
  }): Promise<number> {
    // Insert into our application database with logging data
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()
    request.input('kodu', cariHesapData.kodu)
    request.input('vkVeyaTckNo', cariHesapData.vkVeyaTckNo)
    request.input('unvan', cariHesapData.unvan)
    request.input('ad', cariHesapData.ad)
    request.input('soyad', cariHesapData.soyad)
    request.input('adres', cariHesapData.adres)
    request.input('il', cariHesapData.il)
    request.input('ilce', cariHesapData.ilce)
    request.input('ulke', cariHesapData.ulke || 'TÜRKİYE')
    request.input('ulkeKodu', cariHesapData.ulkeKodu || 'TR')
    request.input('email', cariHesapData.email)
    request.input('postaKodu', cariHesapData.postaKodu)
    request.input('ozelKod', cariHesapData.ozelKod)
    request.input('logoRef', logicalref ?? 0)
    request.input('veritabani_id', veritabaniId)
    request.input('logo_rest_data', logoRestLogData || null)
    request.input('logo_sql_data', logoSqlLogData || null)

    const result = await request.query(`
      INSERT INTO CariHesaplar (
        kodu, vkVeyaTckNo, unvan, ad, soyad, adres,
        il, ilce, ulke, ulkeKodu, email,
        postaKodu, ozelKod, logoRef, veritabani_id,
        logo_rest_data, logo_sql_data, createdAt, updatedAt
      )
      OUTPUT INSERTED.id
      VALUES (
        @kodu, @vkVeyaTckNo, @unvan, @ad, @soyad, @adres,
        @il, @ilce, @ulke, @ulkeKodu, @email,
        @postaKodu, @ozelKod, @logoRef, @veritabani_id,
        @logo_rest_data, @logo_sql_data, GETDATE(), GETDATE()
      )
    `)
    return result.recordset[0].id
  },

  /**
   * Save Logo customer data
   */
  async saveLogoCariHesapData({
    cardId,
    readyCustomerToPost,
    veritabaniId,
  }: {
    cardId: number
    readyCustomerToPost: LogoCariHesapRequest
    veritabaniId: string
  }): Promise<void> {
    const logoCariHesap = {
      cariHesapId: cardId,
      code: readyCustomerToPost.CODE,
      title: readyCustomerToPost.TITLE,
      name: readyCustomerToPost.NAME,
      surname: readyCustomerToPost.SURNAME,
      address1: readyCustomerToPost.ADDRESS1,
      address2: readyCustomerToPost.ADDRESS2,
      town: readyCustomerToPost.TOWN,
      city: readyCustomerToPost.CITY,
      country: readyCustomerToPost.COUNTRY,
      countryCode: readyCustomerToPost.COUNTRY_CODE,
      postalCode: readyCustomerToPost.POSTAL_CODE,
      auxilCode: readyCustomerToPost.AUXIL_CODE,
      email: readyCustomerToPost.E_MAIL,
      taxId: readyCustomerToPost.TAX_ID,
      tcId: readyCustomerToPost.TCKNO,
      accountType: readyCustomerToPost.ACCOUNT_TYPE,
      acceptEInv: readyCustomerToPost.ACCEPT_EINV,
      postLabel: readyCustomerToPost.POST_LABEL,
      senderLabel: readyCustomerToPost.SENDER_LABEL,
      profileId: readyCustomerToPost.PROFILE_ID,
      insteadOfDispatch: readyCustomerToPost.INSTEAD_OF_DISPATCH,
    }

    await LogoSqlService.saveLogoCariHesap({ logoCariHesap, veritabaniId })
  },

  /**
   * Update customer with log data
   */
  async updateCariHesapWithLogData({
    cardId,
    veritabaniId,
    logoRestLogData,
  }: {
    cardId: number
    veritabaniId: string
    logoRestLogData: string
  }): Promise<void> {
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()
    request.input('id', cardId)
    request.input('logo_rest_data', logoRestLogData)
    request.input('veritabani_id', veritabaniId)

    await request.query(`
      UPDATE CariHesaplar
      SET logo_rest_data = @logo_rest_data, updatedAt = GETDATE()
      WHERE id = @id AND veritabani_id = @veritabani_id
    `)
  },

  /**
   * Handle REST API errors
   */
  async handleRestError({
    cardId,
    error,
    veritabaniId,
  }: {
    cardId: number
    error: unknown
    veritabaniId: string
  }): Promise<void> {
    const errorMessage = error instanceof Error ? `${error.message} ${error.cause}` : 'Bilinmeyen hata'

    if (error instanceof Error) {
      error.message = `${error.message} ${error.cause}`
    }

    await LogoSqlService.updateCariHesaplarError({
      veritabaniId,
      params: { id: cardId, error: errorMessage },
    })
  },

  /**
   * Prepare CLCARD parameters for SQL insertion
   */
  prepareClCardParams({
    cariHesapData,
    readyCustomerToPost,
    guid,
  }: {
    cariHesapData: CariHesapRequest
    readyCustomerToPost: LogoCariHesapRequest
    guid: string
  }) {
    const clcardInsertData = {
      COUNTRY: cariHesapData.ulke || 'TÜRKİYE',
      COUNTRY_CODE: cariHesapData.ulkeKodu || 'TR',
      POSTAL_CODE: cariHesapData.postaKodu || '',
      AUXIL_CODE: cariHesapData.ozelKod || '',
      E_MAIL: cariHesapData.email || '',
      PURCHBRWS: 1,
      SALESBRWS: 1,
      IMPBRWS: 1,
      EXPBRWS: 1,
      FINBRWS: 1,
      ISPERSCOMP: readyCustomerToPost.PERSCOMPANY || 0,
      INSTEADOFDESP: readyCustomerToPost.INSTEAD_OF_DISPATCH || 1,
    }

    return {
      CARDTYPE: readyCustomerToPost.ACCOUNT_TYPE || 3,
      DEFINITION_: readyCustomerToPost.TITLE,
      SPECODE: readyCustomerToPost.AUXIL_CODE || '',
      ADDR1: readyCustomerToPost.ADDRESS1 || '',
      ADDR2: readyCustomerToPost.ADDRESS2 || '',
      CITY: readyCustomerToPost.CITY || '',
      COUNTRY: clcardInsertData.COUNTRY,
      POSTCODE: clcardInsertData.POSTAL_CODE,
      EMAILADDR: clcardInsertData.E_MAIL,
      CAPIBLOCK_CREATEDMIN: 0,
      PURCHBRWS: clcardInsertData.PURCHBRWS,
      SALESBRWS: clcardInsertData.SALESBRWS,
      IMPBRWS: clcardInsertData.IMPBRWS,
      EXPBRWS: clcardInsertData.EXPBRWS,
      FINBRWS: clcardInsertData.FINBRWS,
      ISPERSCOMP: clcardInsertData.ISPERSCOMP,
      CODE: readyCustomerToPost.CODE,
      TAXNR: readyCustomerToPost.TAX_ID || '',
      TCKNO: readyCustomerToPost.TCKNO || '',
      NAME: readyCustomerToPost.NAME || '',
      SURNAME: readyCustomerToPost.SURNAME || '',
      INSTEADOFDESP: clcardInsertData.INSTEADOFDESP,
      TOWN: readyCustomerToPost.TOWN || '',
      COUNTRYCODE: clcardInsertData.COUNTRY_CODE,
      GUID: guid,
      ACTIVE: 0,
      // All required fields with default values to satisfy ClCardParams interface
      CYPHCODE: '',
      TELNRS1: '',
      TELNRS2: '',
      FAXNR: '',
      TAXOFFICE: '',
      INCHARGE: '',
      WEBADDR: '',
      WARNEMAILADDR: '',
      WARNFAXNR: '',
      VATNR: '',
      // Bank fields
      BANKBRANCHS1: '',
      BANKBRANCHS2: '',
      BANKBRANCHS3: '',
      BANKBRANCHS4: '',
      BANKBRANCHS5: '',
      BANKBRANCHS6: '',
      BANKBRANCHS7: '',
      BANKACCOUNTS1: '',
      BANKACCOUNTS2: '',
      BANKACCOUNTS3: '',
      BANKACCOUNTS4: '',
      BANKACCOUNTS5: '',
      BANKACCOUNTS6: '',
      BANKACCOUNTS7: '',
      BANKNAMES1: '',
      BANKNAMES2: '',
      BANKNAMES3: '',
      BANKNAMES4: '',
      BANKNAMES5: '',
      BANKNAMES6: '',
      BANKNAMES7: '',
      BANKIBANS1: '',
      BANKIBANS2: '',
      BANKIBANS3: '',
      BANKIBANS4: '',
      BANKIBANS5: '',
      BANKIBANS6: '',
      BANKIBANS7: '',
      BANKBICS1: '',
      BANKBICS2: '',
      BANKBICS3: '',
      BANKBICS4: '',
      BANKBICS5: '',
      BANKBICS6: '',
      BANKBICS7: '',
      BANKBCURRENCY1: '',
      BANKBCURRENCY2: '',
      BANKBCURRENCY3: '',
      BANKBCURRENCY4: '',
      BANKBCURRENCY5: '',
      BANKBCURRENCY6: '',
      BANKBCURRENCY7: '',
      BANKCORRPACC1: '',
      BANKCORRPACC2: '',
      BANKCORRPACC3: '',
      BANKCORRPACC4: '',
      BANKCORRPACC5: '',
      BANKCORRPACC6: '',
      BANKCORRPACC7: '',
      BANKVOEN1: '',
      BANKVOEN2: '',
      BANKVOEN3: '',
      BANKVOEN4: '',
      BANKVOEN5: '',
      BANKVOEN6: '',
      BANKVOEN7: '',
      // Other required string fields
      DELIVERYMETHOD: '',
      DELIVERYFIRM: '',
      EDINO: '',
      TRADINGGRP: '',
      PPGROUPCODE: '',
      TAXOFFCODE: '',
      TOWNCODE: '',
      DISTRICTCODE: '',
      DISTRICT: '',
      CITYCODE: '',
      ORDSENDEMAILADDR: '',
      ORDSENDFAXNR: '',
      DSPSENDEMAILADDR: '',
      DSPSENDFAXNR: '',
      INVSENDEMAILADDR: '',
      INVSENDFAXNR: '',
      SUBSCRIBEREXT: '',
      AUTOPAIDBANK: '',
      STORECREDITCARDNO: '',
      LOGOID: '',
      EXPREGNO: '',
      EXPDOCNO: '',
      LTRSENDEMAILADDR: '',
      LTRSENDFAXNR: '',
      CELLPHONE: '',
      STATECODE: '',
      STATENAME: '',
      TELCODES1: '',
      TELCODES2: '',
      FAXCODE: '',
      ORGLOGOID: '',
      SPECODE2: '',
      SPECODE3: '',
      SPECODE4: '',
      SPECODE5: '',
      OFFSENDEMAILADDR: '',
      OFFSENDFAXNR: '',
      MAPID: '',
      LONGITUDE: '',
      LATITUTE: '',
      CITYID: '',
      TOWNID: '',
      EXTSENDEMAILADDR: '',
      EXTSENDFAXNR: '',
      INCHARGE2: '',
      INCHARGE3: '',
      EMAILADDR2: '',
      EMAILADDR3: '',
      EINVOICEID: '',
      DEFINITION2: '',
      TELEXTNUMS1: '',
      TELEXTNUMS2: '',
      FAXEXTNUM: '',
      FACEBOOKURL: '',
      TWITTERURL: '',
      APPLEID: '',
      SKYPEID: '',
      GLOBALID: '',
      ADRESSNO: '',
      POSTLABELCODE: '',
      SENDERLABELCODE: '',
      FBSSENDEMAILADDR: '',
      FBSSENDFAXNR: '',
      FBASENDEMAILADDR: '',
      FBASENDFAXNR: '',
      EARCEMAILADDR1: '',
      EARCEMAILADDR2: '',
      EARCEMAILADDR3: '',
      POSTLABELCODEDESP: '',
      SENDERLABELCODEDESP: '',
      EXIMSENDEMAILADDR: '',
      EXIMSENDFAXNR: '',
      INCHTELCODES1: '',
      INCHTELCODES2: '',
      INCHTELCODES3: '',
      INCHTELNRS1: '',
      INCHTELNRS2: '',
      INCHTELNRS3: '',
      INCHTELEXTNUMS1: '',
      INCHTELEXTNUMS2: '',
      INCHTELEXTNUMS3: '',
      MERSISNO: '',
      COMMRECORDNO: '',
      WHATSAPPID: '',
      LINKEDINURL: '',
      INSTAGRAMURL: '',
      // All required numeric fields with default values
      DISCRATE: 0,
      EXTENREF: 0,
      PAYMENTREF: 0,
      WARNMETHOD: 0,
      CLANGUAGE: 0,
      BLOCKED: 0,
      CCURRENCY: 0,
      TEXTINC: 0,
      SITEID: 0,
      RECSTATUS: 0,
      ORGLOGICREF: 0,
      CAPIBLOCK_MODIFIEDBY: 0,
      CAPIBLOCK_MODIFIEDHOUR: 0,
      CAPIBLOCK_MODIFIEDMIN: 0,
      CAPIBLOCK_MODIFIEDSEC: 0,
      PAYMENTPROC: 0,
      CRATEDIFFPROC: 0,
      WFSTATUS: 0,
      PPGROUPREF: 0,
      ORDSENDMETHOD: 0,
      DSPSENDMETHOD: 0,
      INVSENDMETHOD: 0,
      SUBSCRIBERSTAT: 0,
      PAYMENTTYPE: 0,
      LASTSENDREMLEV: 0,
      EXTACCESSFLAGS: 0,
      ORDSENDFORMAT: 0,
      DSPSENDFORMAT: 0,
      INVSENDFORMAT: 0,
      REMSENDFORMAT: 0,
      CLORDFREQ: 0,
      ORDDAY: 0,
      LIDCONFIRMED: 0,
      EXPBUSTYPREF: 0,
      INVPRINTCNT: 0,
      PIECEORDINFLICT: 0,
      COLLECTINVOICING: 0,
      EBUSDATASENDTYPE: 0,
      INISTATUSFLAGS: 0,
      SLSORDERSTATUS: 0,
      SLSORDERPRICE: 0,
      LTRSENDMETHOD: 0,
      LTRSENDFORMAT: 0,
      IMAGEINC: 0,
      SAMEITEMCODEUSE: 0,
      WFLOWCRDREF: 0,
      PARENTCLREF: 0,
      LOWLEVELCODES2: 0,
      LOWLEVELCODES3: 0,
      LOWLEVELCODES4: 0,
      LOWLEVELCODES5: 0,
      LOWLEVELCODES6: 0,
      LOWLEVELCODES7: 0,
      LOWLEVELCODES8: 0,
      LOWLEVELCODES9: 0,
      LOWLEVELCODES10: 0,
      ADDTOREFLIST: 0,
      TEXTREFTR: 0,
      TEXTREFEN: 0,
      ARPQUOTEINC: 0,
      CLCRM: 0,
      GRPFIRMNR: 0,
      CONSCODEREF: 0,
      OFFSENDMETHOD: 0,
      OFFSENDFORMAT: 0,
      EBANKNO: 0,
      LOANGRPCTRL: 0,
      LDXFIRMNR: 0,
      EXTSENDMETHOD: 0,
      EXTSENDFORMAT: 0,
      CASHREF: 0,
      USEDINPERIODS: 0,
      RSKLIMCR: 0,
      RSKDUEDATECR: 0,
      RSKAGINGCR: 0,
      RSKAGINGDAY: 0,
      ACCEPTEINV: 0,
      PROFILEID: 0,
      PURCORDERSTATUS: 0,
      PURCORDERPRICE: 0,
      ISFOREIGN: 0,
      SHIPBEGTIME1: 0,
      SHIPBEGTIME2: 0,
      SHIPBEGTIME3: 0,
      SHIPENDTIME1: 0,
      SHIPENDTIME2: 0,
      SHIPENDTIME3: 0,
      DBSLIMIT1: 0,
      DBSLIMIT2: 0,
      DBSLIMIT3: 0,
      DBSLIMIT4: 0,
      DBSLIMIT5: 0,
      DBSLIMIT6: 0,
      DBSLIMIT7: 0,
      DBSTOTAL1: 0,
      DBSTOTAL2: 0,
      DBSTOTAL3: 0,
      DBSTOTAL4: 0,
      DBSTOTAL5: 0,
      DBSTOTAL6: 0,
      DBSTOTAL7: 0,
      DBSBANKNO1: 0,
      DBSBANKNO2: 0,
      DBSBANKNO3: 0,
      DBSBANKNO4: 0,
      DBSBANKNO5: 0,
      DBSBANKNO6: 0,
      DBSBANKNO7: 0,
      DBSRISKCNTRL1: 0,
      DBSRISKCNTRL2: 0,
      DBSRISKCNTRL3: 0,
      DBSRISKCNTRL4: 0,
      DBSRISKCNTRL5: 0,
      DBSRISKCNTRL6: 0,
      DBSRISKCNTRL7: 0,
      DBSBANKCURRENCY1: 0,
      DBSBANKCURRENCY2: 0,
      DBSBANKCURRENCY3: 0,
      DBSBANKCURRENCY4: 0,
      DBSBANKCURRENCY5: 0,
      DBSBANKCURRENCY6: 0,
      DBSBANKCURRENCY7: 0,
      EINVOICETYPE: 0,
      DUEDATECOUNT: 0,
      DUEDATELIMIT: 0,
      DUEDATETRACK: 0,
      DUEDATECONTROL1: 0,
      DUEDATECONTROL2: 0,
      DUEDATECONTROL3: 0,
      DUEDATECONTROL4: 0,
      DUEDATECONTROL5: 0,
      DUEDATECONTROL6: 0,
      DUEDATECONTROL7: 0,
      DUEDATECONTROL8: 0,
      DUEDATECONTROL9: 0,
      DUEDATECONTROL10: 0,
      DUEDATECONTROL11: 0,
      DUEDATECONTROL12: 0,
      DUEDATECONTROL13: 0,
      DUEDATECONTROL14: 0,
      DUEDATECONTROL15: 0,
      CLOSEDATECOUNT: 0,
      CLOSEDATETRACK: 0,
      DEGACTIVE: 0,
      DEGCURR: 0,
      LABELINFO: 0,
      DEFBNACCREF: 0,
      PROJECTREF: 0,
      DISCTYPE: 0,
      SENDMOD: 0,
      ISPERCURR: 0,
      CURRATETYPE: 0,
      EINVOICETYP: 0,
      FBSSENDMETHOD: 0,
      FBSSENDFORMAT: 0,
      FBASENDMETHOD: 0,
      FBASENDFORMAT: 0,
      SECTORMAINREF: 0,
      SECTORSUBREF: 0,
      PERSONELCOSTS: 0,
      FACTORYDIVNR: 0,
      FACTORYNR: 0,
      ININVENNR: 0,
      OUTINVENNR: 0,
      QTYDEPDURATION: 0,
      QTYINDEPDURATION: 0,
      OVERLAPTYPE: 0,
      OVERLAPAMNT: 0,
      OVERLAPPERC: 0,
      BROKERCOMP: 0,
      CREATEWHFICHE: 0,
      EINVCUSTOM: 0,
      SUBCONT: 0,
      ORDPRIORITY: 0,
      ACCEPTEDESP: 0,
      PROFILEIDDESP: 0,
      LABELINFODESP: 0,
      ACCEPTEINVPUBLIC: 0,
      PUBLICBNACCREF: 0,
      PAYMENTPROCBRANCH: 0,
      KVKKPERMSTATUS: 0,
      KVKKANONYSTATUS: 0,
      EXIMSENDMETHOD: 0,
      EXIMSENDFORMAT: 0,
      CLCCANDEDUCT: 0,
      DRIVERREF: 0,
      NOTIFYCRDREF: 0,
      EXCNTRYTYP: 0,
      EXCNTRYREF: 0,
      IMCNTRYTYP: 0,
      IMCNTRYREF: 0,
      EXIMPAYTYPREF: 0,
      EXIMBRBANKREF: 0,
      EXIMCUSTOMREF: 0,
      EXIMREGTYPREF: 0,
      EXIMNTFYCLREF: 0,
      EXIMCNSLTCLREF: 0,
      EXIMFRGHTCLREF: 0,
      DISPPRINTCNT: 0,
      ORDPRINTCNT: 0,
      CLPTYPEFORPPAYDT: 0,
      CLSTYPEFORPPAYDT: 0,
    }
  },
}

export default CariHesaplarService

// Export the main function for backward compatibility
export const sendCariHesap = CariHesaplarService.sendCariHesap
