/**
 * Types for the SatisFaturalari module
 */

export interface SatisFaturaLineItem {
  TYPE: number
  MASTER_CODE?: string
  DISCEXP_CALC?: number
  SOURCEINDEX?: number
  SOURCE_COST_GRP?: number
  FACTORY?: number
  AUXIL_CODE?: string
  QUANTITY?: number
  TOTAL?: number
  PRICE?: number
  EDT_CURR?: number
  EDT_PRICE?: number
  TC_XRATE?: number
  DESCRIPTION?: string
  DISCOUNT_RATE?: number
  UNIT_CODE?: string
  VAT_INCLUDED?: number
  VAT_RATE?: number
  BILLED?: number
  SALEMANCODE?: string
  PROJECT_CODE?: string
  AFFECT_RISK?: number
}

export interface SatisFaturaDispatch {
  TYPE: number
  NUMBER: string
  DATE: string
  TIME: number
}

export interface SatisFaturaHeader {
  TYPE: number
  NUMBER: string
  DATE: string
  TIME: number
  DOC_NUMBER?: string
  AUXIL_CODE?: string
  ARP_CODE?: string
  SOURCE_WH?: number
  SOURCE_COST_GRP?: number
  FACTORY?: number
  NOTES1?: string
  NOTES2?: string
  NOTES3?: string
  NOTES4?: string
  NOTES5?: string
  NOTES6?: string
  CURR_INVOICE?: number
  TC_XRATE?: number
  RC_XRATE?: number
  DIVISION?: number
  DEPARTMENT?: number
  SALESMAN_CODE?: string
  CURRSEL_TOTALS?: number
  CURRSEL_DETAILS?: number
  PROJECT_CODE?: string
  AFFECT_RISK?: number
  DOC_DATE?: string
  PROFILE_ID?: number
  EINSTEAD_OF_DISPATCH?: number
  DataObjectParameter?: {
    FillAccCodesOnPreSave?: boolean
  }
}

export interface SatisFaturaInput {
  INVOICE: SatisFaturaHeader
  TRANSACTIONS?: {
    items: SatisFaturaLineItem[]
  }
  DISPATCHES?: {
    items: SatisFaturaDispatch[]
  }
  requestData?: any
}

export interface InsertResult {
  id?: number
  error?: string
  success: boolean
}

// Endpoint request types - these represent the format expected in the HTTP request
export interface SatisFaturaRequest {
  veritabani_id: string
  fatura_turu: number
  fatura_no?: string
  fatura_numarasi_formati?: string
  tarihi: string
  saati: string // HH:MM:SS format that will be converted to seconds
  belge_no?: string
  ozel_kod?: string
  cari_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  aciklama?: string
  doviz_kuru?: number
  isyeri_kodu?: number
  bolum_kodu?: number
  satis_elemani?: string
  proje_kodu?: string
  belge_tarihi?: string
  fatura_satirlari?: SatisFaturaSatirRequest[]
  irsaliye_no?: string
  irsaliye_numarasi_formati?: string
  irsaliye_tarihi?: string
  irsaliye_saati?: string // HH:MM:SS format that will be converted to seconds
}

export interface SatisFaturaSatirRequest {
  satir_turu: number
  malzeme_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  hareket_ozel_kodu?: string
  miktar?: number
  indirim_tutari?: number
  birim_fiyat?: number
  para_birimi?: string
  dovizli_birim_fiyat?: number
  doviz_kuru?: number
  aciklama?: string
  indirim_orani?: number
  birim_kodu?: string
  kdv_orani?: number
  satis_elemani?: string
  proje_kodu?: string
}
