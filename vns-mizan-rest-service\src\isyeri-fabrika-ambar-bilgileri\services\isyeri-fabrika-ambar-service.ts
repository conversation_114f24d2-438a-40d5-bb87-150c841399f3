import type { IResult } from 'mssql'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

interface IsyeriFabrikaAmbar {
  isyeri_no: number
  isyeri_adi: string
  fabrika_no: number
  fabrika_adi: string
  ambar_no: number
  ambar_adi: string
  veritabani_id: string
}

/**
 * Get işyeri, fabrika ve ambar bilgilerini Logo veritabanından getirir
 */
export async function getIsyeriFabrikaAmbar({ veritabaniId }: { veritabaniId: string }): Promise<IsyeriFabrikaAmbar[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        capidiv.NR as isyeri_no,
        capidiv.NAME as isyeri_adi,
        capifactory.NR as fabrika_no,
        capifactory.NAME as fabrika_adi,
        capiwhouse.NR as ambar_no,
        capiwhouse.NAME as ambar_adi,
        '${veritabaniId}' as veritabani_id
      FROM 
        ${logoConfig.erp.logodb_master}..L_CAPIWHOUSE as capiwhouse
        LEFT JOIN ${logoConfig.erp.logodb_master}..L_CAPIDIV as capidiv 
          ON capidiv.NR = capiwhouse.DIVISNR 
          AND capidiv.FIRMNR = ${logoConfig.erp.firma_numarasi}
        LEFT JOIN ${logoConfig.erp.logodb_master}..L_CAPIFACTORY as capifactory 
          ON capifactory.DIVISNR = capidiv.NR 
          AND capifactory.NR = capiwhouse.FACTNR 
          AND capifactory.FIRMNR = ${logoConfig.erp.firma_numarasi}
      WHERE 
        capiwhouse.FIRMNR = ${logoConfig.erp.firma_numarasi}
      ORDER BY 
        capiwhouse.NR
    `
    const result: IResult<IsyeriFabrikaAmbar[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('İşyeri, fabrika ve ambar bilgileri sorgulanırken hata:', error)
    throw error
  }
}
