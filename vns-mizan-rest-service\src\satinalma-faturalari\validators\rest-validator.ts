import type { z } from 'zod'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'
import { consola } from 'consola'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'

/**
 * REST API validation service for purchase invoices
 * Focuses on Logo REST API payload structure and requirements
 */
const SatinalmaFaturaRestValidator = {

  validateSatinalmaFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: z.infer<typeof satinalmaFaturalariSchema>
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // REST API validation focuses on business logic validation
      // Number format validation is handled by Logo REST API itself

      // Cari hesap kontrolü
      if (requestData.cari_kodu) {
        const cariExists = await LogoSqlUtils.checkCustomerExists({
          code: requestData.cari_kodu,
          veritabaniId,
        })
        if (!cariExists) {
          return {
            isValid: false,
            error: `"${requestData.cari_kodu}" kodlu cari hesap Logo'da bulunamadı. Lütfen cari hesap kodunu kontrol edin.`,
          }
        }
      }

      // Proje kodu kontrolü
      if (requestData.proje_kodu) {
        const projeExists = await LogoSqlUtils.checkProjeKoduExists({
          proje_kodu: requestData.proje_kodu,
          veritabaniId,
        })
        if (!projeExists) {
          return {
            isValid: false,
            error: `"${requestData.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
          }
        }
      }

      // Ambar kontrolü
      if (requestData.ambar_kodu) {
        const ambarExists = await LogoSqlUtils.checkWarehouseExists({
          ambar: requestData.ambar_kodu,
          veritabaniId,
        })
        if (!ambarExists) {
          return {
            isValid: false,
            error: `"${requestData.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
          }
        }
      }

      // İşyeri kontrolü
      if (requestData.isyeri_kodu) {
        const isyeriExists = await LogoSqlUtils.checkDivisionExists({
          isyeri: requestData.isyeri_kodu,
          veritabaniId,
        })
        if (!isyeriExists) {
          return {
            isValid: false,
            error: `"${requestData.isyeri_kodu}" numaralı işyeri Logo'da bulunamadı. Lütfen işyeri kodunu kontrol edin.`,
          }
        }
      }

      // Bölüm kontrolü
      if (requestData.bolum_kodu) {
        const bolumExists = await LogoSqlUtils.checkDepartmentExists({
          bolum: requestData.bolum_kodu,
          veritabaniId,
        })
        if (!bolumExists) {
          return {
            isValid: false,
            error: `"${requestData.bolum_kodu}" numaralı bölüm Logo'da bulunamadı. Lütfen bölüm kodunu kontrol edin.`,
          }
        }
      }

      // Fabrika kontrolü
      if (requestData.fabrika_kodu) {
        const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
          fabrika: requestData.fabrika_kodu,
          veritabaniId,
        })
        if (!fabrikaExists) {
          return {
            isValid: false,
            error: `"${requestData.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
          }
        }
      }

      // Satır kontrolü
      if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
        for (const satir of requestData.fatura_satirlari) {
          // Malzeme kodu kontrolü - Hizmet kodu olarak kontrol et
          if (satir.malzeme_kodu) {
            const itemExists = await LogoSqlUtils.checkServiceCodeExists({
              code: satir.malzeme_kodu,
              veritabaniId,
            })
            if (!itemExists) {
              return {
                isValid: false,
                error: `"${satir.malzeme_kodu}" kodlu hizmet Logo'da bulunamadı. Lütfen hizmet kodunu kontrol edin.`,
              }
            }
          }

          // Birim kodu kontrolü
          if (satir.birim_kodu) {
            const unitExists = await LogoLookupService.getUnitRefFromCode(satir.birim_kodu, veritabaniId)
            if (!unitExists) {
              return {
                isValid: false,
                error: `"${satir.birim_kodu}" kodlu birim Logo'da bulunamadı. Lütfen birim kodunu kontrol edin.`,
              }
            }
          }

          // Proje kodu kontrolü (satır seviyesi)
          if (satir.proje_kodu) {
            const projeExists = await LogoSqlUtils.checkProjeKoduExists({
              proje_kodu: satir.proje_kodu,
              veritabaniId,
            })
            if (!projeExists) {
              return {
                isValid: false,
                error: `"${satir.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
              }
            }
          }

          // Ambar kontrolü (satır seviyesi)
          if (satir.ambar_kodu) {
            const ambarExists = await LogoSqlUtils.checkWarehouseExists({
              ambar: satir.ambar_kodu,
              veritabaniId,
            })
            if (!ambarExists) {
              return {
                isValid: false,
                error: `"${satir.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
              }
            }
          }

          // Fabrika kontrolü (satır seviyesi)
          if (satir.fabrika_kodu) {
            const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
              fabrika: satir.fabrika_kodu,
              veritabaniId,
            })
            if (!fabrikaExists) {
              return {
                isValid: false,
                error: `"${satir.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
              }
            }
          }
        }
      }

      return { isValid: true }
    }
    catch (error: any) {
      consola.error('REST satınalma faturası doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `REST satınalma faturası doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },
}

export default SatinalmaFaturaRestValidator
