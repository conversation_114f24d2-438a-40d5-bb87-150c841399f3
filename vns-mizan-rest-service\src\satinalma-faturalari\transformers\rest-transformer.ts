import type { z } from 'zod'
import type {
  SatinalmaFaturaHeader,
  SatinalmaFaturaInput,
  SatinalmaFaturaLineItem,
} from '../models/purchase-invoice.ts'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Converts HH:MM:SS time string to Logo integer format.
 * Formula: HH * 16777216 + MM * 65536 + SS * 256
 * @param timeString Time in HH:MM:SS format
 * @returns Logo integer representation of time, or 0 if invalid.
 */
function convertTimeToLogoInt(timeString: string | undefined): number {
  if (!timeString || !timeFormatRegex.test(timeString)) {
    return 0
  }
  const parts = timeString.split(':')
  if (parts.length !== 3 || !parts[0] || !parts[1] || !parts[2]) {
    return 0
  }
  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    return 0
  }

  return hours * 16777216 + minutes * 65536 + seconds * 256
}

/**
 * Transforms Mizan purchase invoice data to REST API format for Logo REST API integration
 * Used when use_rest=true
 */
export async function transformToRestFormat(
  requestData: z.infer<typeof satinalmaFaturalariSchema>,
): Promise<SatinalmaFaturaInput> {
  // Tarih ve saat formatını ayarla
  const invoiceDate = requestData.tarihi
  const invoiceTime = convertTimeToLogoInt(requestData.saati)

  // Belge tarihi
  const docDate = requestData.belge_tarihi || requestData.tarihi

  // Fatura başlık bilgilerini oluştur
  const invoice: SatinalmaFaturaHeader = {
    TYPE: requestData.fatura_turu,
    NUMBER: requestData.fatura_no || '~',
    DATE: invoiceDate,
    TIME: invoiceTime,
    DOC_NUMBER: requestData.belge_no,
    AUXIL_CODE: requestData.ozel_kod,
    ARP_CODE: requestData.cari_kodu,
    SOURCE_WH: requestData.ambar_kodu,
    FACTORY: requestData.fabrika_kodu,
    NOTES1: requestData.aciklama ? requestData.aciklama.substring(0, 300) : undefined,
    NOTES2: requestData.aciklama && requestData.aciklama.length > 300 ? requestData.aciklama.substring(300, 600) : undefined,
    NOTES3: requestData.aciklama && requestData.aciklama.length > 600 ? requestData.aciklama.substring(600, 900) : undefined,
    NOTES4: requestData.aciklama && requestData.aciklama.length > 900 ? requestData.aciklama.substring(900, 1200) : undefined,
    NOTES5: requestData.aciklama && requestData.aciklama.length > 1200 ? requestData.aciklama.substring(1200, 1500) : undefined,
    NOTES6: requestData.aciklama && requestData.aciklama.length > 1500 ? requestData.aciklama.substring(1500, 1800) : undefined,
    CURR_INVOICE: requestData.doviz_kuru ? 1 : undefined, // Döviz kullanılıyorsa 1
    TC_XRATE: requestData.doviz_kuru,
    // RC_XRATE will be set later after getting the reporting currency exchange rate
    DIVISION: requestData.isyeri_kodu,
    DEPARTMENT: requestData.bolum_kodu,
    PAYMENT_CODE: requestData.odeme_kodu,
    CURRSEL_TOTALS: 2, // TL
    CURRSEL_DETAILS: 2, // TL
    PROJECT_CODE: requestData.proje_kodu,
    AFFECT_RISK: 1, // Riski etkilesin
    DOC_DATE: docDate,
  }

  // Fatura satırlarını oluştur
  let transactions: { items: SatinalmaFaturaLineItem[] } | undefined

  if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
    const items: SatinalmaFaturaLineItem[] = requestData.fatura_satirlari.map((satir) => {
      return {
        TYPE: satir.satir_turu,
        MASTER_CODE: satir.malzeme_kodu,
        SOURCEINDEX: satir.ambar_kodu,
        FACTORY: satir.fabrika_kodu,
        AUXIL_CODE: satir.hareket_ozel_kodu,
        QUANTITY: satir.miktar,
        PRICE: satir.birim_fiyat,
        DESCRIPTION: satir.aciklama,
        UNIT_CODE: satir.birim_kodu,
        VAT_RATE: satir.kdv_orani,
        BILLED: 1, // Faturalandı
        EDT_PRICE: satir.dovizli_birim_fiyat,
        PROJECT_CODE: satir.proje_kodu,
        CANDEDUCT: satir.tevkifat_yapilabilir,
        DEDUCTION_PART1: satir.tevkifat_payi1,
        DEDUCTION_PART2: satir.tevkifat_payi2,
        DEDUCT_CODE: satir.tevkifat_kodu,
        DEDUCT_DEF: satir.tevkifat_aciklamasi,
        OHP_CODE1: satir.masraf_merkezi1,
        OHP_CODE3: satir.masraf_merkezi3,
        OHP_CODE4: satir.masraf_merkezi4,

        // REST API specific fields - indirim_tutari doesn't exist in line schema
        DISCOUNT_AMOUNT: 0, // indirim_tutari not in line schema
        NET_PRICE: satir.birim_fiyat || 0, // Use birim_fiyat directly since no discount
      }
    })

    transactions = { items }
  }

  return {
    INVOICE: invoice,
    TRANSACTIONS: transactions,
    requestData,
  }
}

/**
 * Transforms Mizan purchase invoice data to SQL format for direct database integration
 * Used when use_rest=false
 */
export async function transformToSqlFormat(
  requestData: z.infer<typeof satinalmaFaturalariSchema>,
): Promise<SatinalmaFaturaInput> {
  // For SQL integration, we use similar transformation as REST
  // but without REST-specific fields
  const restData = await transformToRestFormat(requestData)

  // Remove REST-specific fields for SQL integration
  if (restData.INVOICE) {
    delete (restData.INVOICE as any).INVOICE_SERIES
    delete (restData.INVOICE as any).INVOICE_NUMBER_FORMAT
  }

  if (restData.TRANSACTIONS?.items) {
    restData.TRANSACTIONS.items.forEach((item) => {
      delete (item as any).DISCOUNT_AMOUNT
      delete (item as any).NET_PRICE
    })
  }

  return restData
}
