import { consola } from 'consola'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'
import LogoSqlValidation from '../services/logo-sql-validation.ts'

/**
 * SQL validation service for sales invoices
 * Focuses on direct database constraints and Logo table requirements
 */
const SatisFaturaSqlValidator = {

  validateSatisFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: any
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // SQL validation includes number format validation and database constraints

      // Validate fatura_no (SQL-specific validation)
      if (requestData.fatura_no === '') {
        // Empty string is not allowed
        return {
          isValid: false,
          error: 'Fatura numarası boş olamaz. Otomatik numara için "~" kullanın veya geçerli bir fatura numarası girin.',
        }
      }

      // If fatura_no is "~", then fatura_numarasi_formati is required
      if (requestData.fatura_no === '~' && !requestData.fatura_numarasi_formati) {
        return {
          isValid: false,
          error: 'Fatura numarası "~" olarak belirtildiğinde fatura_numarasi_formati alanı zorunludur. Örnek: "ABC____" veya "INV[yyyy]____"',
        }
      }

      // If fatura_numarasi_formati is provided, ensure it ends with at least one underscore
      if (requestData.fatura_numarasi_formati && !requestData.fatura_numarasi_formati.endsWith('_')) {
        return {
          isValid: false,
          error: 'Fatura numarası formatı en az bir "_" karakteri ile bitmelidir. Örnek: "ABC____" veya "INV[yyyy]____"',
        }
      }

      // Validate irsaliye_no (SQL-specific validation)
      if (requestData.irsaliye_no === '') {
        // Empty string is not allowed
        return {
          isValid: false,
          error: 'İrsaliye numarası boş olamaz. Otomatik numara için "~" kullanın veya geçerli bir irsaliye numarası girin.',
        }
      }

      // If irsaliye_no is "~", then irsaliye_numarasi_formati is required
      if (requestData.irsaliye_no === '~' && !requestData.irsaliye_numarasi_formati) {
        return {
          isValid: false,
          error: 'İrsaliye numarası "~" olarak belirtildiğinde irsaliye_numarasi_formati alanı zorunludur. Örnek: "IRS____" veya "DISP[yyyy]____"',
        }
      }

      // If irsaliye_numarasi_formati is provided, ensure it ends with at least one underscore
      if (requestData.irsaliye_numarasi_formati && !requestData.irsaliye_numarasi_formati.endsWith('_')) {
        return {
          isValid: false,
          error: 'İrsaliye numarası formatı en az bir "_" karakteri ile bitmelidir. Örnek: "IRS____" veya "DISP[yyyy]____"',
        }
      }

      // Check if the invoice number already exists (if a specific number is provided)
      if (requestData.fatura_no && requestData.fatura_no !== '~') {
        const invoiceExists = await LogoSqlValidation.checkInvoiceNumberExists({
          fatura_no: requestData.fatura_no,
          trcode: requestData.fatura_turu,
          veritabaniId,
        })

        if (invoiceExists) {
          return {
            isValid: false,
            error: `"${requestData.fatura_no}" numaralı fatura zaten mevcut. Lütfen başka bir fatura numarası kullanın.`,
          }
        }
      }

      // Check if the dispatch number already exists (if a specific number is provided)
      if (requestData.irsaliye_no && requestData.irsaliye_no !== '~') {
        const dispatchExists = await LogoSqlValidation.checkDispatchNumberExists({
          irsaliye_no: requestData.irsaliye_no,
          veritabaniId,
        })

        if (dispatchExists) {
          return {
            isValid: false,
            error: `"${requestData.irsaliye_no}" numaralı irsaliye zaten mevcut. Lütfen başka bir irsaliye numarası kullanın.`,
          }
        }
      }

      // Validate cari_kodu
      if (requestData.cari_kodu) {
        const customerExists = await LogoSqlUtils.checkCustomerExists({
          code: requestData.cari_kodu,
          veritabaniId,
        })
        if (!customerExists) {
          return {
            isValid: false,
            error: `"${requestData.cari_kodu}" kodlu cari hesap Logo'da bulunamadı. Lütfen cari hesap kodunu kontrol edin.`,
          }
        }
      }

      // Validate proje_kodu
      if (requestData.proje_kodu) {
        const projeExists = await LogoSqlUtils.checkProjeKoduExists({
          proje_kodu: requestData.proje_kodu,
          veritabaniId,
        })
        if (!projeExists) {
          return {
            isValid: false,
            error: `"${requestData.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
          }
        }
      }

      // Validate ambar_kodu
      if (requestData.ambar_kodu) {
        const ambarExists = await LogoSqlUtils.checkWarehouseExists({
          ambar: requestData.ambar_kodu,
          veritabaniId,
        })
        if (!ambarExists) {
          return {
            isValid: false,
            error: `"${requestData.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
          }
        }
      }

      // Validate isyeri_kodu
      if (requestData.isyeri_kodu) {
        const isyeriExists = await LogoSqlUtils.checkDivisionExists({
          isyeri: requestData.isyeri_kodu,
          veritabaniId,
        })
        if (!isyeriExists) {
          return {
            isValid: false,
            error: `"${requestData.isyeri_kodu}" numaralı işyeri Logo'da bulunamadı. Lütfen işyeri kodunu kontrol edin.`,
          }
        }
      }

      // Validate bolum_kodu
      if (requestData.bolum_kodu) {
        const bolumExists = await LogoSqlUtils.checkDepartmentExists({
          bolum: requestData.bolum_kodu,
          veritabaniId,
        })
        if (!bolumExists) {
          return {
            isValid: false,
            error: `"${requestData.bolum_kodu}" numaralı bölüm Logo'da bulunamadı. Lütfen bölüm kodunu kontrol edin.`,
          }
        }
      }

      // Validate fabrika_kodu
      if (requestData.fabrika_kodu) {
        const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
          fabrika: requestData.fabrika_kodu,
          veritabaniId,
        })
        if (!fabrikaExists) {
          return {
            isValid: false,
            error: `"${requestData.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
          }
        }
      }

      // Validate fatura_satirlari
      if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
        for (const satir of requestData.fatura_satirlari) {
          // Validate malzeme_kodu for items
          if (satir.satir_turu === 0 && satir.malzeme_kodu) {
            try {
              await LogoSqlUtils.getItemCode({
                code: satir.malzeme_kodu,
                veritabaniId,
              })
            }
            catch (error: any) {
              return {
                isValid: false,
                error: error.message,
              }
            }
          }
          // Validate service code
          else if (satir.satir_turu === 2 && satir.malzeme_kodu) {
            const serviceExists = await LogoSqlUtils.checkServiceCodeExists({
              code: satir.malzeme_kodu,
              veritabaniId,
            })
            if (!serviceExists) {
              return {
                isValid: false,
                error: `"${satir.malzeme_kodu}" kodlu hizmet Logo'da bulunamadı. Lütfen hizmet kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item ambar_kodu
          if (satir.ambar_kodu) {
            const ambarExists = await LogoSqlUtils.checkWarehouseExists({
              ambar: satir.ambar_kodu,
              veritabaniId,
            })
            if (!ambarExists) {
              return {
                isValid: false,
                error: `"${satir.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item fabrika_kodu
          if (satir.fabrika_kodu) {
            const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
              fabrika: satir.fabrika_kodu,
              veritabaniId,
            })
            if (!fabrikaExists) {
              return {
                isValid: false,
                error: `"${satir.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item proje_kodu
          if (satir.proje_kodu) {
            const projeExists = await LogoSqlUtils.checkProjeKoduExists({
              proje_kodu: satir.proje_kodu,
              veritabaniId,
            })
            if (!projeExists) {
              return {
                isValid: false,
                error: `"${satir.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
              }
            }
          }
        }
      }

      return { isValid: true }
    }
    catch (error: any) {
      consola.error('SQL fatura doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `SQL fatura doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },
}

export default SatisFaturaSqlValidator
