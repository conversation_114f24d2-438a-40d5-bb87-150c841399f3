import type { IResult } from 'mssql'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

interface Marka {
  id: number
  kodu: string
  adi: string
  update_date: Date
  veritabani_id: string
}

/**
 * Get markalar from Logo database
 */
export async function getMarkalar({ veritabaniId }: { veritabaniId: string }): Promise<Marka[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        mark.LOGICALREF AS id,
        mark.CAPIBLOCK_MODIFIEDDATE AS update_date,
        mark.CODE AS kodu,
        mark.DESCR AS adi,
        '${veritabaniId}' as veritabani_id
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_MARK as mark
      ORDER BY 
        mark.CODE
    `
    const result: IResult<Marka[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Markalar sorgulanırken hata:', error)
    throw error
  }
}
