import { consola } from 'consola'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'

/**
 * Enhanced REST API validation service for sales invoices
 * Combines business logic validation with REST-specific requirements
 * Focuses on data integrity while Logo REST API handles format validation
 */
const SatisFaturaRestValidator = {

  /**
   * Comprehensive business validation for common invoice requirements
   * Used by both REST and SQL validators to ensure consistent business rules
   */
  validateBusinessRules: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: any
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // Common business validation rules that apply to both REST and SQL

      // Validate required fields
      if (!requestData.fatura_turu) {
        return {
          isValid: false,
          error: 'Fatura türü zorunludur.',
        }
      }

      // Validate fatura_turu range
      if (requestData.fatura_turu < 1 || requestData.fatura_turu > 26) {
        return {
          isValid: false,
          error: 'Fatura türü 1-26 arasında olmalıdır.',
        }
      }

      // Validate date format and logic
      if (requestData.tarihi) {
        const faturaDate = new Date(requestData.tarihi)
        const today = new Date()
        const oneYearAgo = new Date()
        oneYearAgo.setFullYear(today.getFullYear() - 1)

        if (faturaDate > today) {
          return {
            isValid: false,
            error: 'Fatura tarihi gelecek bir tarih olamaz.',
          }
        }

        if (faturaDate < oneYearAgo) {
          return {
            isValid: false,
            error: 'Fatura tarihi bir yıldan eski olamaz.',
          }
        }
      }

      // Validate currency and exchange rate consistency
      if (requestData.doviz_kuru && requestData.doviz_kuru !== 0) {
        if (requestData.doviz_kuru <= 0) {
          return {
            isValid: false,
            error: 'Döviz kuru pozitif bir değer olmalıdır.',
          }
        }
      }

      // Validate line items business rules
      if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
        for (let i = 0; i < requestData.fatura_satirlari.length; i++) {
          const satir = requestData.fatura_satirlari[i]
          const satirNo = i + 1

          // Validate quantity
          if (satir.miktar !== undefined && satir.miktar <= 0) {
            return {
              isValid: false,
              error: `Satır ${satirNo}: Miktar pozitif bir değer olmalıdır.`,
            }
          }

          // Validate unit price
          if (satir.birim_fiyat !== undefined && satir.birim_fiyat < 0) {
            return {
              isValid: false,
              error: `Satır ${satirNo}: Birim fiyat negatif olamaz.`,
            }
          }

          // Validate VAT rate
          if (satir.kdv_orani !== undefined && (satir.kdv_orani < 0 || satir.kdv_orani > 100)) {
            return {
              isValid: false,
              error: `Satır ${satirNo}: KDV oranı 0-100 arasında olmalıdır.`,
            }
          }

          // Validate discount rate
          if (satir.indirim_orani !== undefined && (satir.indirim_orani < 0 || satir.indirim_orani > 100)) {
            return {
              isValid: false,
              error: `Satır ${satirNo}: İndirim oranı 0-100 arasında olmalıdır.`,
            }
          }
        }
      }

      return { isValid: true }
    }
    catch (error: any) {
      consola.error('İş kuralları doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `İş kuralları doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },

  validateSatisFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: any
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // First, run common business rules validation
      const businessRulesResult = await SatisFaturaRestValidator.validateBusinessRules({
        requestData,
        veritabaniId,
      })

      if (!businessRulesResult.isValid) {
        return businessRulesResult
      }

      // REST API validation focuses on Logo ERP data existence validation
      // Number format validation is handled by Logo REST API itself

      // Validate cari_kodu
      if (requestData.cari_kodu) {
        const customerExists = await LogoSqlUtils.checkCustomerExists({
          code: requestData.cari_kodu,
          veritabaniId,
        })
        if (!customerExists) {
          return {
            isValid: false,
            error: `"${requestData.cari_kodu}" kodlu cari hesap Logo'da bulunamadı. Lütfen cari hesap kodunu kontrol edin.`,
          }
        }
      }

      // Validate proje_kodu
      if (requestData.proje_kodu) {
        const projeExists = await LogoSqlUtils.checkProjeKoduExists({
          proje_kodu: requestData.proje_kodu,
          veritabaniId,
        })
        if (!projeExists) {
          return {
            isValid: false,
            error: `"${requestData.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
          }
        }
      }

      // Validate ambar_kodu
      if (requestData.ambar_kodu) {
        const ambarExists = await LogoSqlUtils.checkWarehouseExists({
          ambar: requestData.ambar_kodu,
          veritabaniId,
        })
        if (!ambarExists) {
          return {
            isValid: false,
            error: `"${requestData.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
          }
        }
      }

      // Validate isyeri_kodu
      if (requestData.isyeri_kodu) {
        const isyeriExists = await LogoSqlUtils.checkDivisionExists({
          isyeri: requestData.isyeri_kodu,
          veritabaniId,
        })
        if (!isyeriExists) {
          return {
            isValid: false,
            error: `"${requestData.isyeri_kodu}" numaralı işyeri Logo'da bulunamadı. Lütfen işyeri kodunu kontrol edin.`,
          }
        }
      }

      // Validate bolum_kodu
      if (requestData.bolum_kodu) {
        const bolumExists = await LogoSqlUtils.checkDepartmentExists({
          bolum: requestData.bolum_kodu,
          veritabaniId,
        })
        if (!bolumExists) {
          return {
            isValid: false,
            error: `"${requestData.bolum_kodu}" numaralı bölüm Logo'da bulunamadı. Lütfen bölüm kodunu kontrol edin.`,
          }
        }
      }

      // Validate fabrika_kodu
      if (requestData.fabrika_kodu) {
        const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
          fabrika: requestData.fabrika_kodu,
          veritabaniId,
        })
        if (!fabrikaExists) {
          return {
            isValid: false,
            error: `"${requestData.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
          }
        }
      }

      // Validate fatura_satirlari
      if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
        for (const satir of requestData.fatura_satirlari) {
          // Validate malzeme_kodu for items
          if (satir.satir_turu === 0 && satir.malzeme_kodu) {
            try {
              await LogoSqlUtils.getItemCode({
                code: satir.malzeme_kodu,
                veritabaniId,
              })
            }
            catch (error: any) {
              return {
                isValid: false,
                error: error.message,
              }
            }
          }
          // Validate service code
          else if (satir.satir_turu === 2 && satir.malzeme_kodu) {
            const serviceExists = await LogoSqlUtils.checkServiceCodeExists({
              code: satir.malzeme_kodu,
              veritabaniId,
            })
            if (!serviceExists) {
              return {
                isValid: false,
                error: `"${satir.malzeme_kodu}" kodlu hizmet Logo'da bulunamadı. Lütfen hizmet kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item ambar_kodu
          if (satir.ambar_kodu) {
            const ambarExists = await LogoSqlUtils.checkWarehouseExists({
              ambar: satir.ambar_kodu,
              veritabaniId,
            })
            if (!ambarExists) {
              return {
                isValid: false,
                error: `"${satir.ambar_kodu}" numaralı ambar Logo'da bulunamadı. Lütfen ambar kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item fabrika_kodu
          if (satir.fabrika_kodu) {
            const fabrikaExists = await LogoSqlUtils.checkFactoryExists({
              fabrika: satir.fabrika_kodu,
              veritabaniId,
            })
            if (!fabrikaExists) {
              return {
                isValid: false,
                error: `"${satir.fabrika_kodu}" numaralı fabrika Logo'da bulunamadı. Lütfen fabrika kodunu kontrol edin.`,
              }
            }
          }

          // Validate line item proje_kodu
          if (satir.proje_kodu) {
            const projeExists = await LogoSqlUtils.checkProjeKoduExists({
              proje_kodu: satir.proje_kodu,
              veritabaniId,
            })
            if (!projeExists) {
              return {
                isValid: false,
                error: `"${satir.proje_kodu}" kodlu proje Logo'da bulunamadı. Lütfen proje kodunu kontrol edin.`,
              }
            }
          }
        }
      }

      return { isValid: true }
    }
    catch (error: any) {
      consola.error('REST fatura doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `REST fatura doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },
}

export default SatisFaturaRestValidator
