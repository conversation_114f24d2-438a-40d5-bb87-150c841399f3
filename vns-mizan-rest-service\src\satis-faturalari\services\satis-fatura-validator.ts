import { consola } from 'consola'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import SatisFaturaRestValidator from '../validators/rest-validator.ts'
import SatisFaturaSqlValidator from '../validators/sql-validator.ts'

/**
 * Comprehensive validation service for sales invoice requests
 * Coordinates between REST and SQL validators based on integration method
 */
const SatisFaturaValidator = {

  validateSatisFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: any
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // Check if REST API is used
      const useRest = await LogoConfigService.getUseRestFlag(veritabaniId)

      // Use appropriate validator based on integration method
      if (useRest) {
        return await SatisFaturaRestValidator.validateSatisFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
      else {
        return await SatisFaturaSqlValidator.validateSatisFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
    }
    catch (error: any) {
      consola.error('Fatura doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `Fatura doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },
}

export default SatisFaturaValidator
