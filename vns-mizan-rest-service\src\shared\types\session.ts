import type { SessionData } from 'express-session'

/**
 * Error type for session operations
 */
export type SessionError = Error | null

/**
 * Callback type for session operations
 */
export type SessionCallback = (err: SessionError, session?: SessionData | null) => void

/**
 * Session data with cookie information
 */
export interface SessionWithCookie extends SessionData {
  cookie: {
    originalMaxAge: number | null
    maxAge: number
    secure?: boolean
    httpOnly?: boolean
    domain?: string
    path?: string
    expires?: Date
    sameSite?: boolean | 'lax' | 'strict' | 'none'
  }
}
