import type { z } from 'zod'
import type {
  InsertResult,
  SatinalmaFaturaSatirRequest,
} from '../models/purchase-invoice.ts'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'

/**
 * Service for application database operations for purchase invoices
 */
const DatabaseService = {
  /**
   * Inserts purchase invoice into application database
   */
  insertSatinalmaFatura: async ({
    requestData,
    errorMessage,
    logoFaturaNo,
    logoFaturaLogicalRef,
    invoiceData,
    stficheData,
    veritabaniId,
  }: {
    requestData: z.infer<typeof satinalmaFaturalariSchema>
    errorMessage?: string
    logoFaturaNo?: string
    logoFaturaLogicalRef?: number
    invoiceData?: string
    stficheData?: string
    veritabaniId: string
  }): Promise<InsertResult> => {
    try {
      const pool = await DbService.getConnection('db')
      const result = await pool.request()
        .input('fatura_turu', sql.Int, requestData.fatura_turu)
        .input('fatura_no', sql.VarChar(16), requestData.fatura_no || '~')
        .input('tarihi', sql.Date, new Date(requestData.tarihi))
        .input('saati', sql.VarChar(8), requestData.saati)
        .input('belge_no', sql.VarChar(32), requestData.belge_no)
        .input('ozel_kod', sql.VarChar(10), requestData.ozel_kod)
        .input('cari_kodu', sql.VarChar(16), requestData.cari_kodu)
        .input('ambar_kodu', sql.Int, requestData.ambar_kodu)
        .input('fabrika_kodu', sql.Int, requestData.fabrika_kodu)
        .input('aciklama', sql.VarChar(1800), requestData.aciklama)
        .input('doviz_kuru', sql.Float, requestData.doviz_kuru)
        .input('isyeri_kodu', sql.Int, requestData.isyeri_kodu)
        .input('bolum_kodu', sql.Int, requestData.bolum_kodu)
        .input('odeme_kodu', sql.VarChar(16), requestData.odeme_kodu)
        .input('proje_kodu', sql.VarChar(100), requestData.proje_kodu)
        .input('belge_tarihi', sql.Date, requestData.belge_tarihi ? new Date(requestData.belge_tarihi) : null)
        .input('logo_fatura_no', sql.VarChar(16), logoFaturaNo)
        .input('logo_fatura_logicalref', sql.Int, logoFaturaLogicalRef)
        .input('invoice_data', sql.NVarChar(sql.MAX), invoiceData)
        .input('stfiche_data', sql.NVarChar(sql.MAX), stficheData)
        .input('error', sql.NVarChar(sql.MAX), errorMessage)
        .input('veritabani_id', sql.VarChar(37), veritabaniId)
        .query(`
          INSERT INTO SatinalmaFaturalari (
            fatura_turu, fatura_no, tarihi, saati, belge_no, ozel_kod, cari_kodu,
            ambar_kodu, fabrika_kodu, aciklama, doviz_kuru, isyeri_kodu, bolum_kodu,
            odeme_kodu, proje_kodu, belge_tarihi, logo_fatura_no, logo_fatura_logicalref,
            invoice_data, stfiche_data, error, veritabani_id
          )
          OUTPUT INSERTED.id
          VALUES (
            @fatura_turu, @fatura_no, @tarihi, @saati, @belge_no, @ozel_kod, @cari_kodu,
            @ambar_kodu, @fabrika_kodu, @aciklama, @doviz_kuru, @isyeri_kodu, @bolum_kodu,
            @odeme_kodu, @proje_kodu, @belge_tarihi, @logo_fatura_no, @logo_fatura_logicalref,
            @invoice_data, @stfiche_data, @error, @veritabani_id
          )
        `)

      if (result.recordset.length > 0) {
        return { success: true, id: result.recordset[0].id }
      }
      return { success: false, error: 'Satınalma faturası kaydedilemedi' }
    }
    catch (error) {
      consola.error('Satınalma faturası kaydedilirken hata oluştu:', error)
      return { success: false, error: `Veritabanı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}` }
    }
  },

  /**
   * Inserts purchase invoice line items into application database
   */
  insertSatinalmaFaturaSatirlari: async ({
    satinalmaFaturaId,
    satirlar,
    stlineDataList,
  }: {
    satinalmaFaturaId: number
    satirlar: SatinalmaFaturaSatirRequest[]
    stlineDataList?: string[]
  }): Promise<InsertResult> => {
    try {
      const pool = await DbService.getConnection('db')
      const transaction = new sql.Transaction(pool)
      await transaction.begin()

      try {
        for (let i = 0; i < satirlar.length; i++) {
          const satir = satirlar[i]
          if (!satir)
            continue // Skip if undefined

          const stlineData = stlineDataList?.[i]

          await transaction.request()
            .input('satinalma_fatura_id', sql.Int, satinalmaFaturaId)
            .input('satir_turu', sql.Int, satir.satir_turu)
            .input('malzeme_kodu', sql.VarChar(16), satir.malzeme_kodu)
            .input('ambar_kodu', sql.Int, satir.ambar_kodu)
            .input('fabrika_kodu', sql.Int, satir.fabrika_kodu)
            .input('hareket_ozel_kodu', sql.VarChar(17), satir.hareket_ozel_kodu)
            .input('miktar', sql.Float, satir.miktar)
            .input('birim_fiyat', sql.Float, satir.birim_fiyat)
            .input('aciklama', sql.VarChar(250), satir.aciklama)
            .input('birim_kodu', sql.VarChar(10), satir.birim_kodu)
            .input('kdv_orani', sql.Float, satir.kdv_orani)
            .input('proje_kodu', sql.VarChar(100), satir.proje_kodu)
            .input('dovizli_birim_fiyat', sql.Float, satir.dovizli_birim_fiyat)
            .input('tevkifat_yapilabilir', sql.Int, satir.tevkifat_yapilabilir)
            .input('tevkifat_payi1', sql.Int, satir.tevkifat_payi1)
            .input('tevkifat_payi2', sql.Int, satir.tevkifat_payi2)
            .input('tevkifat_kodu', sql.VarChar(16), satir.tevkifat_kodu)
            .input('tevkifat_aciklamasi', sql.VarChar(250), satir.tevkifat_aciklamasi)
            .input('masraf_merkezi1', sql.VarChar(25), satir.masraf_merkezi1)
            .input('masraf_merkezi3', sql.VarChar(25), satir.masraf_merkezi3)
            .input('masraf_merkezi4', sql.VarChar(25), satir.masraf_merkezi4)
            .input('stline_data', sql.NVarChar(sql.MAX), stlineData)
            .query(`
              INSERT INTO SatinalmaFaturalariSatirlari (
                satinalma_fatura_id, satir_turu, malzeme_kodu, ambar_kodu, fabrika_kodu,
                hareket_ozel_kodu, miktar, birim_fiyat, aciklama, birim_kodu, kdv_orani,
                proje_kodu, dovizli_birim_fiyat, tevkifat_yapilabilir, tevkifat_payi1,
                tevkifat_payi2, tevkifat_kodu, tevkifat_aciklamasi, masraf_merkezi1,
                masraf_merkezi3, masraf_merkezi4, stline_data
              )
              VALUES (
                @satinalma_fatura_id, @satir_turu, @malzeme_kodu, @ambar_kodu, @fabrika_kodu,
                @hareket_ozel_kodu, @miktar, @birim_fiyat, @aciklama, @birim_kodu, @kdv_orani,
                @proje_kodu, @dovizli_birim_fiyat, @tevkifat_yapilabilir, @tevkifat_payi1,
                @tevkifat_payi2, @tevkifat_kodu, @tevkifat_aciklamasi, @masraf_merkezi1,
                @masraf_merkezi3, @masraf_merkezi4, @stline_data
              )
            `)
        }

        await transaction.commit()
        return { success: true }
      }
      catch (error) {
        await transaction.rollback()
        throw error
      }
    }
    catch (error) {
      consola.error('Satınalma faturası satırları kaydedilirken hata oluştu:', error)
      return { success: false, error: `Veritabanı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}` }
    }
  },
}

export default DatabaseService
