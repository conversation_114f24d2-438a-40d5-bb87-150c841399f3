import { readFile } from 'node:fs/promises'

/**
 * Reads and parses the configuration file.
 * @param {string} filePath - The path to the configuration file.
 * @returns {Promise<object>} The parsed configuration object.
 * @throws {Error} If there's an error reading or parsing the file.
 */
export async function readConfig(filePath = 'config.json') {
  try {
    const configContent = await readFile(filePath, 'utf-8')
    const config = JSON.parse(configContent) as Config
    return config
  }
  catch (error) {
    if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
      throw new Error(`config.json dosyası bulunamadı: ${filePath}`)
    }
    if (error instanceof Error) {
      throw new TypeError(`config.json dosyası okunurken bir hata oluştu: ${error.message}`)
    }
    throw error
  }
}

export async function getLogoConfigById(veritabaniId: string) {
  const config = await readConfig()
  const logoConnection = config.project_settings.logo.db_connections.find(
    conn => conn.id === veritabaniId,
  )

  if (!logoConnection) {
    throw new Error(`Veritabanı numarası "${veritabaniId}" olan Logo tanımlaması bulunamadı.`)
  }

  return logoConnection
}

export interface DatabaseConfig {
  server: string
  database: string
  user: string
  password: string
  port: number
  encrypt: boolean
  trust_server_certificate: boolean
}

interface SessionConfig {
  secret: string
  cookie: {
    secure: boolean
    max_age: number
  }
}

interface AdminConfig {
  email: string
  password: string
  full_name: string
}

interface MailConfig {
  host: string
  port: number
  secure: boolean
  user: string
  pass: string
  from: string
}

export interface LogoConnectionConfig {
  id: string
  active: boolean
  name: string
  sql: DatabaseConfig
  erp: {
    logodb_master: string
    firma_numarasi: string
    donem_numarasi: string
    kullanici_adi: string
    sifre: string
    elogo: {
      web_service_url: string
    }
    rest_settings: {
      use_rest: boolean
      rest_api_url: string
      client_key: string
    }
  }
}

interface HttpsConfig {
  enabled: boolean
  key_path: string
  cert_path: string
}

export interface Config {
  node_env: 'production' | 'development'
  database_connections: {
    db: DatabaseConfig
  }
  project_settings: {
    port: number
    instance_count: number
    https?: HttpsConfig
    session: SessionConfig
    admin: AdminConfig
    mail: MailConfig
    logo: {
      db_connections: LogoConnectionConfig[]
    }
  }
}
