import type { z } from 'zod'
import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  SatisFaturaDispatch,
  SatisFaturaHeader,
  SatisFaturaLineItem,
  SatisFaturaSatirRequest,
} from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'

/**
 * Service for application database operations
 * Handles local database operations for tracking and logging
 */
const DatabaseService = {

  /**
   * Insert into SatisFaturalari table
   */
  insertSatisFatura: async ({
    requestData,
    errorMessage,
    logoFaturaNo,
    logoIrsaliyeNo,
    logoFaturaLogicalRef,
    logoIrsaliyeLogicalRef,
    veritabaniId,
    logoKullaniciAdi,
  }: {
    requestData: z.infer<typeof satisFaturalariSchema>
    errorMessage?: string
    logoFaturaNo?: string
    logoIrsaliyeNo?: string
    logoFaturaLogicalRef?: number
    logoIrsaliyeLogicalRef?: number
    veritabaniId?: string
    logoKullaniciAdi?: string
  }): Promise<InsertResult> => {
    try {
      const pool = await DbService.getConnection('db')

      const saatiString = requestData.saati
      const irsaliyeSaatiString = requestData.irsaliye_saati

      const result = await pool.request()
        .input('fatura_turu', sql.Int, requestData.fatura_turu)
        .input('fatura_no', sql.VarChar(16), requestData.fatura_no || '~')
        .input('tarihi', sql.Date, new Date(requestData.tarihi))
        .input('saati', sql.VarChar(8), saatiString)
        .input('belge_no', sql.VarChar(32), requestData.belge_no)
        .input('ozel_kod', sql.VarChar(10), requestData.ozel_kod)
        .input('cari_kodu', sql.VarChar(16), requestData.cari_kodu)
        .input('ambar_kodu', sql.Int, requestData.ambar_kodu)
        .input('fabrika_kodu', sql.Int, requestData.fabrika_kodu)
        .input('aciklama', sql.VarChar(1800), requestData.aciklama)
        .input('doviz_kuru', sql.Float, requestData.doviz_kuru)
        .input('isyeri_kodu', sql.Int, requestData.isyeri_kodu)
        .input('bolum_kodu', sql.Int, requestData.bolum_kodu)
        .input('satis_elemani', sql.VarChar(24), requestData.satis_elemani)
        .input('proje_kodu', sql.VarChar(100), requestData.proje_kodu)
        .input('belge_tarihi', sql.Date, requestData.belge_tarihi ? new Date(requestData.belge_tarihi) : null)
        .input('irsaliye_no', sql.VarChar(16), requestData.irsaliye_no)
        .input('irsaliye_tarihi', sql.Date, requestData.irsaliye_tarihi ? new Date(requestData.irsaliye_tarihi) : null)
        .input('irsaliye_saati', sql.VarChar(8), irsaliyeSaatiString)
        .input('logo_fatura_no', sql.VarChar(16), logoFaturaNo)
        .input('logo_irsaliye_no', sql.VarChar(16), logoIrsaliyeNo)
        .input('logo_fatura_logicalref', sql.Int, logoFaturaLogicalRef)
        .input('logo_irsaliye_logicalref', sql.Int, logoIrsaliyeLogicalRef)
        .input('logo_kullanici_adi', sql.VarChar(50), logoKullaniciAdi)
        .input('error', sql.NVarChar(sql.MAX), errorMessage)
        .input('veritabani_id', sql.VarChar(37), veritabaniId || requestData.veritabani_id)
        .query(`
          INSERT INTO SatisFaturalari (
            fatura_turu, fatura_no, tarihi, saati, belge_no, ozel_kod, cari_kodu,
            ambar_kodu, fabrika_kodu, aciklama, doviz_kuru, isyeri_kodu, bolum_kodu, satis_elemani,
            proje_kodu, belge_tarihi, irsaliye_no, irsaliye_tarihi, irsaliye_saati,
            logo_fatura_no, logo_irsaliye_no, logo_fatura_logicalref, logo_irsaliye_logicalref,
            logo_kullanici_adi, error, veritabani_id
          )
          VALUES (
            @fatura_turu, @fatura_no, @tarihi, @saati, @belge_no, @ozel_kod, @cari_kodu,
            @ambar_kodu, @fabrika_kodu, @aciklama, @doviz_kuru, @isyeri_kodu, @bolum_kodu, @satis_elemani,
            @proje_kodu, @belge_tarihi, @irsaliye_no, @irsaliye_tarihi, @irsaliye_saati,
            @logo_fatura_no, @logo_irsaliye_no, @logo_fatura_logicalref, @logo_irsaliye_logicalref,
            @logo_kullanici_adi, @error, @veritabani_id
          );
          SELECT SCOPE_IDENTITY() AS id;
        `)

      consola.success(`Satış faturası kaydı başarıyla oluşturuldu. ID: ${result.recordset[0].id}`)
      return { id: result.recordset[0].id, success: true }
    }
    catch (error: any) {
      consola.error('Satış faturası eklenirken bir hata meydana geldi:', error)
      return { error: error.message, success: false }
    }
  },

  /**
   * Insert into SatisFaturalariSatirlari table
   */
  insertSatisFaturaSatirlari: async ({
    faturaSatirlari,
    satisFaturaId,
  }: {
    faturaSatirlari: SatisFaturaSatirRequest[]
    satisFaturaId: number
  }): Promise<InsertResult> => {
    let transaction

    try {
      const pool = await DbService.getConnection('db')
      transaction = new sql.Transaction(pool)
      await transaction.begin()

      const request = new sql.Request(transaction)

      for (const satir of faturaSatirlari) {
        await request
          .input('satis_fatura_id', sql.Int, satisFaturaId)
          .input('satir_turu', sql.Int, satir.satir_turu)
          .input('malzeme_kodu', sql.VarChar(16), satir.malzeme_kodu)
          .input('ambar_kodu', sql.Int, satir.ambar_kodu)
          .input('fabrika_kodu', sql.Int, satir.fabrika_kodu)
          .input('hareket_ozel_kodu', sql.VarChar(17), satir.hareket_ozel_kodu)
          .input('miktar', sql.Float, satir.miktar)
          .input('indirim_tutari', sql.Float, satir.indirim_tutari)
          .input('birim_fiyat', sql.Float, satir.birim_fiyat)
          .input('para_birimi', sql.VarChar(10), satir.para_birimi)
          .input('dovizli_birim_fiyat', sql.Float, satir.dovizli_birim_fiyat)
          .input('doviz_kuru', sql.Float, satir.doviz_kuru)
          .input('aciklama', sql.VarChar(250), satir.aciklama)
          .input('indirim_orani', sql.Float, satir.indirim_orani)
          .input('birim_kodu', sql.VarChar(10), satir.birim_kodu)
          .input('kdv_orani', sql.Float, satir.kdv_orani)
          .input('satis_elemani', sql.VarChar(24), satir.satis_elemani)
          .input('proje_kodu', sql.VarChar(100), satir.proje_kodu)
          .query(`
            INSERT INTO SatisFaturalariSatirlari (
              satis_fatura_id, satir_turu, malzeme_kodu, ambar_kodu, fabrika_kodu, hareket_ozel_kodu,
              miktar, indirim_tutari, birim_fiyat, para_birimi, dovizli_birim_fiyat,
              doviz_kuru, aciklama, indirim_orani, birim_kodu, kdv_orani,
              satis_elemani, proje_kodu
            )
            VALUES (
              @satis_fatura_id, @satir_turu, @malzeme_kodu, @ambar_kodu, @fabrika_kodu, @hareket_ozel_kodu,
              @miktar, @indirim_tutari, @birim_fiyat, @para_birimi, @dovizli_birim_fiyat,
              @doviz_kuru, @aciklama, @indirim_orani, @birim_kodu, @kdv_orani,
              @satis_elemani, @proje_kodu
            );
          `)
      }

      await transaction.commit()
      consola.success(`Fatura satırları başarıyla kaydedildi. Fatura ID: ${satisFaturaId}, Satır sayısı: ${faturaSatirlari.length}`)
      return { success: true }
    }
    catch (error: any) {
      if (transaction) {
        try {
          await transaction.rollback()
          consola.warn('Fatura satırları işlemi geri alındı')
        }
        catch (rollbackError) {
          consola.error('İşlem geri alınırken hata oluştu:', rollbackError)
        }
      }
      consola.error('Fatura satırları eklenirken hata oluştu:', error)
      return { error: error.message, success: false }
    }
  },

  /**
   * Insert into LogoSatisFaturalari table
   */
  insertLogoSatisFatura: async ({
    invoice,
    veritabaniId,
    errorMessage,
    logoFaturaNo,
    logoFaturaLogicalRef,
    logoKullaniciAdi,
  }: {
    invoice: SatisFaturaHeader
    veritabaniId: string
    errorMessage?: string
    logoFaturaNo?: string
    logoFaturaLogicalRef?: number
    logoKullaniciAdi?: string
  }): Promise<InsertResult> => {
    try {
      const pool = await DbService.getConnection('db')

      const result = await pool.request()
        .input('TYPE', sql.Int, invoice.TYPE)
        .input('NUMBER', sql.VarChar(16), invoice.NUMBER)
        .input('DATE', sql.Date, new Date(invoice.DATE))
        .input('TIME', sql.Int, invoice.TIME)
        .input('DOC_NUMBER', sql.VarChar(32), invoice.DOC_NUMBER)
        .input('AUXIL_CODE', sql.VarChar(10), invoice.AUXIL_CODE)
        .input('ARP_CODE', sql.VarChar(16), invoice.ARP_CODE)
        .input('SOURCE_WH', sql.Int, invoice.SOURCE_WH)
        .input('SOURCE_COST_GRP', sql.Int, invoice.SOURCE_COST_GRP)
        .input('FACTORY', sql.Int, invoice.FACTORY)
        .input('NOTES1', sql.VarChar(300), invoice.NOTES1)
        .input('NOTES2', sql.VarChar(300), invoice.NOTES2)
        .input('NOTES3', sql.VarChar(300), invoice.NOTES3)
        .input('NOTES4', sql.VarChar(300), invoice.NOTES4)
        .input('NOTES5', sql.VarChar(300), invoice.NOTES5)
        .input('NOTES6', sql.VarChar(300), invoice.NOTES6)
        .input('CURR_INVOICE', sql.Int, invoice.CURR_INVOICE)
        .input('TC_XRATE', sql.Float, invoice.TC_XRATE)
        .input('RC_XRATE', sql.Float, invoice.RC_XRATE)
        .input('DIVISION', sql.Int, invoice.DIVISION)
        .input('DEPARTMENT', sql.Int, invoice.DEPARTMENT)
        .input('SALESMAN_CODE', sql.VarChar(24), invoice.SALESMAN_CODE)
        .input('CURRSEL_TOTALS', sql.Int, invoice.CURRSEL_TOTALS)
        .input('CURRSEL_DETAILS', sql.Int, invoice.CURRSEL_DETAILS)
        .input('PROJECT_CODE', sql.VarChar(100), invoice.PROJECT_CODE)
        .input('AFFECT_RISK', sql.Int, invoice.AFFECT_RISK)
        .input('DOC_DATE', sql.Date, invoice.DOC_DATE ? new Date(invoice.DOC_DATE) : null)
        .input('PROFILE_ID', sql.Int, invoice.PROFILE_ID)
        .input('EINSTEAD_OF_DISPATCH', sql.Int, invoice.EINSTEAD_OF_DISPATCH)
        .input('logo_fatura_no', sql.VarChar(16), logoFaturaNo)
        .input('logo_fatura_logicalref', sql.Int, logoFaturaLogicalRef)
        .input('logo_kullanici_adi', sql.VarChar(50), logoKullaniciAdi)
        .input('error', sql.NVarChar(sql.MAX), errorMessage)
        .input('veritabani_id', sql.VarChar(37), veritabaniId)
        .query(`
          INSERT INTO LogoSatisFaturalari (
            TYPE, NUMBER, DATE, TIME, DOC_NUMBER, AUXIL_CODE, ARP_CODE,
            SOURCE_WH, SOURCE_COST_GRP, FACTORY, NOTES1, NOTES2, NOTES3, NOTES4, NOTES5, NOTES6,
            CURR_INVOICE, TC_XRATE, RC_XRATE, DIVISION, DEPARTMENT, SALESMAN_CODE,
            CURRSEL_TOTALS, CURRSEL_DETAILS, PROJECT_CODE, AFFECT_RISK, DOC_DATE,
            PROFILE_ID, EINSTEAD_OF_DISPATCH, logo_fatura_no, logo_fatura_logicalref, logo_kullanici_adi, error, veritabani_id
          )
          VALUES (
            @TYPE, @NUMBER, @DATE, @TIME, @DOC_NUMBER, @AUXIL_CODE, @ARP_CODE,
            @SOURCE_WH, @SOURCE_COST_GRP, @FACTORY, @NOTES1, @NOTES2, @NOTES3, @NOTES4, @NOTES5, @NOTES6,
            @CURR_INVOICE, @TC_XRATE, @RC_XRATE, @DIVISION, @DEPARTMENT, @SALESMAN_CODE,
            @CURRSEL_TOTALS, @CURRSEL_DETAILS, @PROJECT_CODE, @AFFECT_RISK, @DOC_DATE,
            @PROFILE_ID, @EINSTEAD_OF_DISPATCH, @logo_fatura_no, @logo_fatura_logicalref, @logo_kullanici_adi, @error, @veritabani_id
          );
          SELECT SCOPE_IDENTITY() AS id;
        `)

      consola.success(`Logo fatura kaydı başarıyla oluşturuldu. ID: ${result.recordset[0].id}`)
      return { id: result.recordset[0].id, success: true }
    }
    catch (error: any) {
      consola.error('Logo fatura kaydı eklenirken hata oluştu:', error)
      return { error: error.message, success: false }
    }
  },

  /**
   * Insert into LogoSatisFaturalariIrsaliyesi table
   */
  insertLogoSatisFaturaIrsaliyesi: async ({
    irsaliyeler,
    logoSatisFaturaId,
    veritabaniId,
    errorMessage,
    logoIrsaliyeNo,
    logoIrsaliyeLogicalRef,
  }: {
    irsaliyeler: SatisFaturaDispatch[]
    logoSatisFaturaId: number
    veritabaniId: string
    errorMessage?: string
    logoIrsaliyeNo?: string
    logoIrsaliyeLogicalRef?: number
  }): Promise<InsertResult> => {
    let transaction

    try {
      const pool = await DbService.getConnection('db')
      transaction = new sql.Transaction(pool)
      await transaction.begin()

      const request = new sql.Request(transaction)

      for (const irsaliye of irsaliyeler) {
        await request
          .input('satis_fatura_id', sql.Int, logoSatisFaturaId)
          .input('TYPE', sql.Int, irsaliye.TYPE)
          .input('NUMBER', sql.VarChar(16), irsaliye.NUMBER || '~')
          .input('DATE', sql.Date, new Date(irsaliye.DATE))
          .input('TIME', sql.Int, irsaliye.TIME)
          .input('logo_irsaliye_no', sql.VarChar(16), logoIrsaliyeNo)
          .input('logo_irsaliye_logicalref', sql.Int, logoIrsaliyeLogicalRef)
          .input('error', sql.NVarChar(sql.MAX), errorMessage)
          .input('veritabani_id', sql.VarChar(37), veritabaniId)
          .query(`
            INSERT INTO LogoSatisFaturalariIrsaliyesi (
              satis_fatura_id, TYPE, NUMBER, DATE, TIME, logo_irsaliye_no, logo_irsaliye_logicalref, error, veritabani_id
            )
            VALUES (
              @satis_fatura_id, @TYPE, @NUMBER, @DATE, @TIME, @logo_irsaliye_no, @logo_irsaliye_logicalref, @error, @veritabani_id
            );
          `)
      }

      await transaction.commit()
      consola.success(`Logo fatura irsaliyeleri başarıyla kaydedildi. Fatura ID: ${logoSatisFaturaId}, İrsaliye sayısı: ${irsaliyeler.length}`)
      return { success: true }
    }
    catch (error: any) {
      if (transaction) {
        try {
          await transaction.rollback()
          consola.warn('Logo fatura irsaliyeleri işlemi geri alındı')
        }
        catch (rollbackError) {
          consola.error('İşlem geri alınırken hata oluştu:', rollbackError)
        }
      }
      consola.error('Logo fatura irsaliyeleri eklenirken hata oluştu:', error)
      return { error: error.message, success: false }
    }
  },

  /**
   * Insert into LogoSatisFaturalariSatirlari table
   */
  insertLogoSatisFaturaSatirlari: async ({
    satirlar,
    logoSatisFaturaId,
    errorMessage,
  }: {
    satirlar: SatisFaturaLineItem[]
    logoSatisFaturaId: number
    errorMessage?: string
  }): Promise<InsertResult> => {
    let transaction

    try {
      const pool = await DbService.getConnection('db')
      transaction = new sql.Transaction(pool)
      await transaction.begin()

      const request = new sql.Request(transaction)

      for (const satir of satirlar) {
        await request
          .input('satis_fatura_id', sql.Int, logoSatisFaturaId)
          .input('TYPE', sql.Int, satir.TYPE)
          .input('MASTER_CODE', sql.VarChar(16), satir.MASTER_CODE)
          .input('DISCEXP_CALC', sql.Int, satir.DISCEXP_CALC)
          .input('SOURCEINDEX', sql.Int, satir.SOURCEINDEX)
          .input('SOURCE_COST_GRP', sql.Int, satir.SOURCE_COST_GRP)
          .input('FACTORY', sql.Int, satir.FACTORY)
          .input('AUXIL_CODE', sql.VarChar(10), satir.AUXIL_CODE)
          .input('QUANTITY', sql.Float, satir.QUANTITY)
          .input('TOTAL', sql.Float, satir.TOTAL)
          .input('PRICE', sql.Float, satir.PRICE)
          .input('EDT_CURR', sql.Int, satir.EDT_CURR)
          .input('EDT_PRICE', sql.Float, satir.EDT_PRICE)
          .input('TC_XRATE', sql.Float, satir.TC_XRATE)
          .input('DESCRIPTION', sql.VarChar(250), satir.DESCRIPTION)
          .input('DISCOUNT_RATE', sql.Float, satir.DISCOUNT_RATE)
          .input('UNIT_CODE', sql.VarChar(10), satir.UNIT_CODE)
          .input('VAT_INCLUDED', sql.Int, satir.VAT_INCLUDED)
          .input('VAT_RATE', sql.Float, satir.VAT_RATE)
          .input('BILLED', sql.Int, satir.BILLED)
          .input('SALEMANCODE', sql.VarChar(24), satir.SALEMANCODE)
          .input('PROJECT_CODE', sql.VarChar(100), satir.PROJECT_CODE)
          .input('AFFECT_RISK', sql.Int, satir.AFFECT_RISK)
          .input('error', sql.NVarChar(sql.MAX), errorMessage)
          .query(`
            INSERT INTO LogoSatisFaturalariSatirlari (
              satis_fatura_id, TYPE, MASTER_CODE, DISCEXP_CALC, SOURCEINDEX,
              SOURCE_COST_GRP, FACTORY, AUXIL_CODE, QUANTITY, TOTAL, PRICE, EDT_CURR, EDT_PRICE,
              TC_XRATE, DESCRIPTION, DISCOUNT_RATE, UNIT_CODE, VAT_INCLUDED, VAT_RATE,
              BILLED, SALEMANCODE, PROJECT_CODE, AFFECT_RISK, error
            )
            VALUES (
              @satis_fatura_id, @TYPE, @MASTER_CODE, @DISCEXP_CALC, @SOURCEINDEX,
              @SOURCE_COST_GRP, @FACTORY, @AUXIL_CODE, @QUANTITY, @TOTAL, @PRICE, @EDT_CURR, @EDT_PRICE,
              @TC_XRATE, @DESCRIPTION, @DISCOUNT_RATE, @UNIT_CODE, @VAT_INCLUDED, @VAT_RATE,
              @BILLED, @SALEMANCODE, @PROJECT_CODE, @AFFECT_RISK, @error
            );
          `)
      }

      await transaction.commit()
      consola.success(`Logo fatura satırları başarıyla kaydedildi. Fatura ID: ${logoSatisFaturaId}, Satır sayısı: ${satirlar.length}`)
      return { success: true }
    }
    catch (error: any) {
      if (transaction) {
        try {
          await transaction.rollback()
          consola.warn('Logo fatura satırları işlemi geri alındı')
        }
        catch (rollbackError) {
          consola.error('İşlem geri alınırken hata oluştu:', rollbackError)
        }
      }
      consola.error('Logo fatura satırları eklenirken hata oluştu:', error)
      return { error: error.message, success: false }
    }
  },
}

export default DatabaseService
