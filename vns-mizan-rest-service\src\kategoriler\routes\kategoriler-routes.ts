import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getKategoriler } from '../services/kategoriler-service.ts'

const router = Router()

/**
 * Get kategoriler validation schema
 */
const getKategorilerSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /kategoriler:
 *   get:
 *     tags: [Kategoriler]
 *     summary: Logo veritabanından kategorileri listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               title: KategorilerResponse
 *               description: Logo veritabanından alınan kategori listesi
 *               type: array
 *               items:
 *                 type: object
 *                 required: [id, kodu, adi, veritabani_id]
 *                 properties:
 *                   id:
 *                     type: number
 *                     description: Kategorinin benzersiz Logo ID'si
 *                   kodu:
 *                     type: string
 *                     description: Kategori kodu
 *                   adi:
 *                     type: string
 *                     description: Kategori adı
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
const getKategorilerHandler: RequestHandler = async (req, res) => {
  try {
    const result = getKategorilerSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const kategoriler = await getKategoriler({ veritabaniId: veritabani_id })
    res.json(kategoriler)
  }
  catch (error) {
    consola.error('Kategoriler alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Kategoriler alınırken bir hata oluştu',
    })
  }
}

router.get('/', getKategorilerHandler)

export default router
