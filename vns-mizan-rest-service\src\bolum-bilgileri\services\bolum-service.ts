import type { IResult } from 'mssql'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

interface Bolum {
  bolum_no: number
  bolum_adi: string
  veritabani_id: string
}

/**
 * Get bölüm bilgilerini Logo veritabanından getirir
 */
export async function getBolumBilgileri({ veritabaniId }: { veritabaniId: string }): Promise<Bolum[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        capidept.NR as bolum_no,
        capidept.NAME as bolum_adi,
        '${veritabaniId}' as veritabani_id
      FROM 
        ${logoConfig.erp.logodb_master}..L_CAPIDEPT as capidept
      WHERE 
        capidept.FIRMNR = ${logoConfig.erp.firma_numarasi}
    `
    const result: IResult<Bolum[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Bölüm bilgileri sorgulanırken hata:', error)
    throw error
  }
}
