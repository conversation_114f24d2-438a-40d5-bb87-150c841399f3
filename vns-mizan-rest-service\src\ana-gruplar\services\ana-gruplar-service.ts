import type { IResult } from 'mssql'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

interface AnaGrup {
  id: number
  kodu: string
  adi: string
  veritabani_id: string
}

/**
 * Get ana gruplar from Logo database
 */
export async function getAnaGruplar({ veritabaniId }: { veritabaniId: string }): Promise<AnaGrup[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        specodes.LOGICALREF AS id,
        specodes.SPECODE AS kodu,
        specodes.DEFINITION_ AS adi,
        '${veritabaniId}' as veritabani_id
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_SPECODES as specodes
      WHERE 
        specodes.CODETYPE = 1
        and specodes.SPECODETYPE = 1
        AND specodes.SPETYP1 = 1
      ORDER BY 
        specodes.SPECODE
    `
    const result: IResult<AnaGrup[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Ana gruplar sorgulanırken hata:', error)
    throw error
  }
}
