import { readFile } from 'node:fs/promises'
import { join } from 'node:path'
import process from 'node:process'
import { consola } from 'consola'
import { Router } from 'express'

const router = Router()

async function getOpenApiSpec() {
  const isDev = process.env.NODE_ENV !== 'production'
  const basePath = isDev ? join(import.meta.dirname, '..', '..', 'scripts') : process.cwd()
  try {
    const data = await readFile(
      join(basePath, 'openapi.json'),
      'utf-8',
    )
    const openapiSpecification = JSON.parse(data)
    return openapiSpecification
  }
  catch (error) {
    consola.error('Openapi dokümantasyonu okunurken hata alındı:', error)
    throw new Error('Openapi dokümantasyonu okunurken hata alındı.')
  }
}

router.get('/swagger.json', async (req, res) => {
  try {
    const spec = await getOpenApiSpec()
    res.setHeader('Content-Type', 'application/json')
    res.send(spec)
  }
  catch (error) {
    res.status(500).json({ error })
  }
})

export default router
