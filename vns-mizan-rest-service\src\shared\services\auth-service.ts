import crypto from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import { readConfig } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

/**
 * User data type
 */
interface User {
  id: number
  email: string
  password: string
  full_name: string
  created_at: Date
  updated_at: Date
}

/**
 * Login credentials type
 */
interface LoginCredentials {
  email: string
  password: string
}

/**
 * Create user data type
 */
interface CreateUserData {
  email: string
  password: string
  full_name: string
}

/**
 * Authentication service for user management
 */
const AuthService = {
  /**
   * Hash password using PBKDF2 with SHA-512
   * @param {string} password - Plain text password
   * @returns {Promise<string>} Hashed password with salt
   */
  async hashPassword(password: string): Promise<string> {
    const salt = crypto.randomBytes(32).toString('hex')
    const iterations = 100000
    const keylen = 64
    const digest = 'sha512'

    const hash = await new Promise<string>((resolve, reject) => {
      crypto.pbkdf2(password, salt, iterations, keylen, digest, (err, derivedKey) => {
        if (err)
          reject(err)
        resolve(`${salt}:${iterations}:${derivedKey.toString('hex')}`)
      })
    })

    return hash
  },

  /**
   * Compare password with hash
   * @param {string} password - Plain text password
   * @param {string} storedHash - Stored password hash with salt
   * @returns {Promise<boolean>} True if password matches
   */
  async comparePassword(password: string, storedHash: string): Promise<boolean> {
    try {
      const [storedSalt, storedIterations, hash] = storedHash.split(':')
      if (!storedSalt || !storedIterations || !hash) {
        consola.error('Hash formatı hatalı')
        return false
      }

      const keylen = 64
      const digest = 'sha512'

      const derivedKey = await new Promise<string>((resolve, reject) => {
        crypto.pbkdf2(password, storedSalt, Number(storedIterations), keylen, digest, (err, key) => {
          if (err)
            reject(err)
          resolve(key.toString('hex'))
        })
      })

      return derivedKey === hash
    }
    catch (error) {
      consola.error('Şifre karşılaştırma hatası:', error instanceof Error ? error.message : String(error))
      return false
    }
  },

  /**
   * Create a new user
   * @param {CreateUserData} userData - User data to create
   * @returns {Promise<User | null>} Created user or null if failed
   */
  async createUser({ email, password, full_name }: CreateUserData): Promise<User | null> {
    try {
      const pool = await DbService.getConnection('db')
      const hashedPassword = await this.hashPassword(password)

      const result = await pool
        .request()
        .input('email', sql.NVarChar, email)
        .input('password', sql.NVarChar, hashedPassword)
        .input('full_name', sql.NVarChar, full_name)
        .query(`INSERT INTO users (email, password, full_name)
          OUTPUT INSERTED.*
          VALUES (@email, @password, @full_name)
          `)

      return result.recordset[0] || null
    }
    catch (error) {
      consola.error('Kullanıcı oluşturulma hatası:', error instanceof Error ? error.message : String(error))
      return null
    }
  },

  /**
   * Get user by email
   * @param {string} email - User email
   * @returns {Promise<User | null>} User or null if not found
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const pool = await DbService.getConnection('db')
      const result = await pool.request().input('email', sql.NVarChar, email).query('SELECT * FROM users WHERE email = @email')

      return result.recordset[0] || null
    }
    catch (error) {
      consola.error('E-posta ile kullanıcı arama hatası:', error instanceof Error ? error.message : String(error))
      return null
    }
  },

  /**
   * Get user by ID
   * @param {number} id - User ID
   * @returns {Promise<User | null>} User or null if not found
   */
  async getUserById(id: number): Promise<User | null> {
    try {
      const pool = await DbService.getConnection('db')
      const result = await pool.request().input('id', sql.Int, id).query('SELECT id, email, full_name FROM users WHERE id = @id')

      return result.recordset[0] || null
    }
    catch (error) {
      consola.error('ID ile kullanıcı arama hatası:', error instanceof Error ? error.message : String(error))
      return null
    }
  },

  /**
   * Verify user credentials
   * @param {LoginCredentials} credentials - Login credentials
   * @returns {Promise<User | null>} User if credentials are valid, null otherwise
   */
  async verifyCredentials({ email, password }: LoginCredentials): Promise<User | null> {
    try {
      const user = await this.getUserByEmail(email)
      if (!user)
        return null

      const isValid = await this.comparePassword(password, user.password)
      return isValid ? user : null
    }
    catch (error) {
      consola.error('Kimlik doğrulama hatası:', error instanceof Error ? error.message : String(error))
      return null
    }
  },

  /**
   * Create initial admin user if no users exist
   * @returns {Promise<void>}
   */
  async createInitialAdminUser(): Promise<void> {
    try {
      const pool = await DbService.getConnection('db')
      const result = await pool.request().query('SELECT COUNT(*) as count FROM users')

      if (result.recordset[0].count === 0) {
        const config = await readConfig()
        const { admin } = config.project_settings

        if (!admin?.email || !admin?.password || !admin?.full_name) {
          consola.error('Admin kullanıcı bilgileri bulunmuyor. Lütfen config.json dosyasını kontrol edin.')
          return
        }

        const user = await this.createUser({
          email: admin.email,
          password: admin.password,
          full_name: admin.full_name,
        })

        if (user) {
          consola.log('Öndeğer admin kullanıcı oluşturuldu:', user)
          consola.log('E-posta:', user.email)
        }
        else {
          consola.error('Öndeğer admin kullanıcı oluşturulamadı. Lütfen veritabanı bağlantısını kontrol edin.')
        }
      }
    }
    catch (error) {
      consola.error('Admin kullanıcısı oluşturulurken hata aldı. error:', error instanceof Error ? error.message : String(error))
    }
  },
}

export default AuthService
