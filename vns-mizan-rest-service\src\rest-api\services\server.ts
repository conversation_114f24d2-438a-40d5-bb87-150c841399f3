import type { Express, NextFunction, Request, Response } from 'express'
import fs from 'node:fs'
import http from 'node:http'
import https from 'node:https'
import process from 'node:process'
import { apiReference } from '@scalar/express-api-reference'
import { consola } from 'consola'
import cors from 'cors'
import express from 'express'
import helmet from 'helmet'
import morgan from 'morgan'
import swaggerUi from 'swagger-ui-express'
import { sessionMiddleware } from '../../shared/middleware/session-middleware.ts'
import DbService from '../../shared/services/db-service.ts'

/**
 * Configuration options for the Express server
 */
export interface ServerConfig {
  port: number
  corsOrigins?: string[]
  apiPrefix?: string
  useHttps?: boolean
  httpsOptions?: {
    key?: string
    cert?: string
  }
}

interface GracefulServer {
  app: Express
  server: http.Server | https.Server
}

/**
 * Creates and configures an Express application
 * @param {ServerConfig} config - Configuration options for the server
 * @returns {Promise<GracefulServer>} Configured Express application and HTTP/HTTPS server
 */
export async function createServer({
  port,
  corsOrigins = ['*'],
  useHttps = false,
  httpsOptions = {},
}: ServerConfig): Promise<GracefulServer> {
  const app = express()

  // Trust proxy settings for proper IP detection behind reverse proxies
  app.set('trust proxy', 1)

  // Add middleware to redirect HTTP to HTTPS when HTTPS is enabled
  if (useHttps) {
    app.use((req: Request, res: Response, next: NextFunction) => {
      if (!req.secure) {
        // Check if the request is secure (HTTPS)
        const httpsUrl = `https://${req.hostname}${req.url}`
        consola.info(`Redirecting HTTP request to HTTPS: ${httpsUrl}`)
        return res.redirect(httpsUrl)
      }
      next()
    })
  }

  // Security middleware
  app.use(
    helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: [`'self'`, 'unpkg.com'],
          styleSrc: [
            `'self'`,
            `'unsafe-inline'`,
            'cdn.jsdelivr.net',
            'fonts.googleapis.com',
            'unpkg.com',
            'fonts.scalar.com',
          ],
          fontSrc: [`'self'`, 'fonts.gstatic.com', 'data:', 'fonts.scalar.com'],
          imgSrc: [`'self'`, 'data:', 'cdn.jsdelivr.net', 'fonts.scalar.com'],
          scriptSrc: [
            `'self'`,
            `https: 'unsafe-inline'`,
            `cdn.jsdelivr.net`,
            `'unsafe-eval'`,
            'fonts.scalar.com',
          ],
        },
      },
      // Ensure HSTS is disabled for HTTP and enabled for HTTPS
      hsts: useHttps
        ? {
            maxAge: 31536000, // 1 year in seconds
            includeSubDomains: true,
            preload: true,
          }
        : false,
    }),
  )

  // JSON parsing middleware with security options
  app.use(
    express.json({
      limit: '10mb',
      strict: true,
      type: 'application/json',
    }),
  )

  // URL-encoded parsing middleware
  app.use(
    express.urlencoded({
      extended: true,
      limit: '10mb',
    }),
  )

  // Logging middleware
  app.use(morgan('dev'))

  // CORS configuration using express cors middleware
  app.use(cors({
    origin: corsOrigins,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
    credentials: true,
    // Add additional CORS headers for HTTPS
    preflightContinue: false,
    optionsSuccessStatus: 204,
  }))

  // Request timeout middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    req.setTimeout(30000, () => {
      res.status(408).json({ message: 'İstek zaman aşımına uğradı' })
    })
    next()
  })

  // Initialize and apply session middleware
  const session = await sessionMiddleware()
  app.use(session)

  /**
   * @openapi
   * /docs:
   *   get:
   *     tags: [System]
   *     summary: Swagger UI dokumentasyon sayfasını sunar
   *     description: API dokümantasyonunu interaktif bir arayüz ile gösterir
   *     responses:
   *       200:
   *         description: Swagger UI HTML sayfası
   *         content:
   *           text/html:
   *             schema:
   *               type: string
   *
   * /swagger.json:
   *   get:
   *     tags: [System]
   *     summary: OpenAPI spesifikasyonunu JSON formatında sunar
   *     description: API'nin OpenAPI/Swagger spesifikasyonunu JSON formatında döndürür
   *     responses:
   *       200:
   *         description: OpenAPI spesifikasyonu
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *
   * /health:
   *   get:
   *     tags: [System]
   *     summary: Sistemin durumunu kontrol eder
   *     description: Sistemin çalışma durumu, bellek kullanımı ve diğer temel bilgileri döndürür
   *     responses:
   *       200:
   *         description: Sistem aktif
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 status:
   *                   type: string
   *                   example: Sistem aktif
   *                 time:
   *                   type: string
   *                   example: 21.12.2023 14:30:45
   *                 uptime:
   *                   type: number
   *                   description: Sistem çalışma süresi (saniye)
   *                   example: 3600
   *                 memory_usage:
   *                   type: object
   *                   properties:
   *                     heapTotal:
   *                       type: number
   *                       description: Toplam heap bellek (byte)
   *                     heapUsed:
   *                       type: number
   *                       description: Kullanılan heap bellek (byte)
   *                     rss:
   *                       type: number
   *                       description: Resident Set Size (byte)
   *                 ip:
   *                   type: string
   *                   description: İstemci IP adresi
   *                   example: ***********
   */

  // Health check endpoint with detailed status
  app.get('/v1/health', (req: Request, res: Response) => {
    res.status(200).json({
      status: 'Sistem aktif',
      time: new Date().toLocaleString('tr-TR', { timeZone: 'Europe/Istanbul' }),
      uptime: process.uptime(),
      memory_usage: process.memoryUsage(),
      ip: req.ip, // Added to verify proxy settings
    })
  })

  // Update Swagger UI setup with correct URL
  app.use('/v1/docs', swaggerUi.serve, swaggerUi.setup(undefined, {
    swaggerOptions: {
      url: '/v1/swagger.json',
    },
  }))

  app.use('/v1/scalar-docs', apiReference({ theme: 'default', url: '/v1/swagger.json' }))

  let server: http.Server | https.Server

  if (useHttps && httpsOptions.key && httpsOptions.cert) {
    try {
      const httpsCredentials = {
        key: fs.readFileSync(httpsOptions.key),
        cert: fs.readFileSync(httpsOptions.cert),
      }
      server = https.createServer(httpsCredentials, app)
      consola.success('HTTPS sunucusu başlatılıyor')
    }
    catch (error) {
      consola.error('HTTPS sertifikaları yüklenemedi, HTTP moduna geçiliyor:', error)
      server = http.createServer(app)
    }
  }
  else {
    server = http.createServer(app)
  }

  server.listen(port)

  // Add server timeout handling
  if ('timeout' in server)
    server.timeout = 30000
  if ('keepAliveTimeout' in server)
    server.keepAliveTimeout = 65000
  if ('headersTimeout' in server)
    server.headersTimeout = 66000 // Should be greater than keepAliveTimeout

  return { app, server }
}

/**
 * Gracefully shuts down the server and closes all connections
 * @param {http.Server | https.Server} server - The HTTP/HTTPS server instance to shut down
 * @returns {Promise<void>}
 */
export async function gracefulShutdown(server: http.Server | https.Server): Promise<void> {
  try {
    consola.info('Sunucu kapatma işlemi başlatılıyor...')

    // Set a timeout for the graceful shutdown
    const shutdownTimeout = setTimeout(() => {
      consola.warn('Kapatma zaman aşımına uğradı, zorla kapatılıyor')
      process.exit(1)
    }, 30000)

    // Close the HTTP server first (stop accepting new connections)
    await new Promise<void>((resolve, reject) => {
      server.close((err: Error | undefined) => {
        if (err) {
          reject(err)
          return
        }
        resolve()
      })
    })

    // Close database connections
    await DbService.closeAllPools()

    // Clear the timeout as shutdown was successful
    clearTimeout(shutdownTimeout)

    consola.success('Sunucu başarıyla kapatıldı')
    process.exit(0)
  }
  catch (error) {
    consola.error('Sunucu kapatma sırasında hata:', error)
    process.exit(1)
  }
}
