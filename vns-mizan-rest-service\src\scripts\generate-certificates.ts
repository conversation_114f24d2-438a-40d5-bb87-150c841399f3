import { existsSync, mkdirSync, writeFileSync } from 'node:fs'
import { dirname, join } from 'node:path'
import process from 'node:process'
import { consola } from 'consola'
import forge from 'node-forge'

/**
 * Generate self-signed certificates for development using node-forge
 */
export default function generateCertificates() {
  try {
    // Determine the correct path for certificates
    // We need to create certificates in the project root directory
    let execDir = process.cwd()

    // Check if we're running as an executable
    const isExe = process.argv[0]?.endsWith('node.exe') === false
      && (process.execPath?.endsWith('.exe')
        || process.argv[1]?.endsWith('vns-mizan-sertifika-olusturucu.exe')
        || process.argv[1]?.endsWith('generate-certificates.exe'))

    // If running as exe, use its directory
    if (isExe) {
      // Get the directory containing the executable
      execDir = dirname(process.execPath || '')
      consola.info(`Çalıştırılabilir dosya dizini: ${execDir}`)
    }
    else {
      // In development mode, always use the current working directory
      consola.info(`Geliştirme modu - Proje dizini: ${execDir}`)
    }

    // Create certs directory directly in the current folder
    const certsDir = 'certs'

    // Create the directory if it doesn't exist
    if (!existsSync(certsDir)) {
      mkdirSync(certsDir, { recursive: true })
    }

    // Paths for the certificate files
    const keyPath = join(certsDir, 'key.pem')
    const certPath = join(certsDir, 'cert.pem')

    // Generate private key and certificate
    consola.info('Özel anahtar ve sertifika oluşturuluyor...')

    // Generate certificates using node-forge
    try {
      // Generate private key and certificate
      consola.info('Özel anahtar ve sertifika oluşturuluyor...')

      // Generate a key pair
      consola.info('RSA anahtar çifti oluşturuluyor...')
      const keys = forge.pki.rsa.generateKeyPair(2048)
      consola.info('RSA anahtar çifti oluşturuldu')

      // Create a self-signed certificate
      consola.info('Kendinden imzalı sertifika oluşturuluyor...')

      // Create certificate
      const cert = forge.pki.createCertificate()

      // Set certificate properties
      cert.publicKey = keys.publicKey
      cert.serialNumber = '01'

      // Set validity period (1 year)
      const now = new Date()
      cert.validity.notBefore = now
      cert.validity.notAfter = new Date()
      cert.validity.notAfter.setFullYear(now.getFullYear() + 1)

      // Set certificate subject and issuer
      const attrs = [
        { name: 'commonName', value: 'localhost' },
        { name: 'countryName', value: 'TR' },
        { name: 'stateOrProvinceName', value: 'Istanbul' },
        { name: 'localityName', value: 'Istanbul' },
        { name: 'organizationName', value: 'Venus Bilisim' },
        { name: 'organizationalUnitName', value: 'Development' },
      ]

      cert.setSubject(attrs)
      cert.setIssuer(attrs)

      // Set extensions
      cert.setExtensions([
        {
          name: 'basicConstraints',
          cA: true,
        },
        {
          name: 'keyUsage',
          keyCertSign: true,
          digitalSignature: true,
          nonRepudiation: true,
          keyEncipherment: true,
          dataEncipherment: true,
        },
        {
          name: 'subjectAltName',
          altNames: [
            {
              type: 2, // DNS
              value: 'localhost',
            },
            {
              type: 7, // IP
              ip: '127.0.0.1',
            },
          ],
        },
      ])

      // Sign the certificate with the private key
      cert.sign(keys.privateKey, forge.md.sha256.create())
      consola.info('Sertifika imzalandı')

      // Convert to PEM format
      const privateKeyPem = forge.pki.privateKeyToPem(keys.privateKey)
      const certPem = forge.pki.certificateToPem(cert)

      // Write the private key and certificate to files
      writeFileSync(keyPath, privateKeyPem)
      writeFileSync(certPath, certPem)

      consola.info('Sertifika doğrulanıyor...')

      // Kendinden imzalı sertifikalar için doğrulama adımını atlıyoruz
      // Kendinden imzalı sertifikalar zaten güvenilir değildir, bu beklenen bir durumdur
      consola.info('Kendinden imzalı sertifika oluşturuldu (geliştirme amaçlı)')
      consola.success('Sertifika doğrulaması tamamlandı')
    }
    catch (error) {
      consola.error(`
      Sertifika oluşturulurken hata oluştu:
      ${error}

      Lütfen node-forge kütüphanesinin güncel olduğundan emin olun.
      `)
      throw error
    }

    consola.success(`Sertifikalar ${certsDir} dizinine oluşturuldu.`)

    // Update config.json with certificate paths
    consola.info('config.json dosyası güncelleniyor...')

    // Create sample config
    const configSample = {
      project_settings: {
        https: {
          enabled: true,
          key_path: 'certs/key.pem',
          cert_path: 'certs/cert.pem',
        },
      },
    }

    // Write sample config to file
    const sampleConfigPath = join(certsDir, 'https-config-sample.json')
    writeFileSync(sampleConfigPath, JSON.stringify(configSample, null, 2))

    consola.success(`
    Sertifikalar başarıyla oluşturuldu!

    config.json dosyanızı güncellemek için örnek yapılandırma dosyalarını kullanabilirsiniz.

    NOT: Bu sertifikalar yalnızca geliştirme amaçlıdır.
    Üretim ortamında gerçek bir sertifika yetkilisi tarafından imzalanmış sertifikalar kullanın.
    `)

    // Eğer exe olarak çalıştırılıyorsa, kullanıcıdan bir tuşa basmasını bekle
    if (process.argv[1]?.endsWith('.exe')
      || process.argv[1]?.endsWith('vns-mizan-sertifika-olusturucu.exe')
      || process.argv[1]?.endsWith('generate-certificates.exe')) {
      console.log('\nDevam etmek için herhangi bir tuşa basın...')
      // Kullanıcıdan bir tuşa basmasını bekle
      process.stdin.setRawMode?.(true)
      process.stdin.resume()
      process.stdin.on('data', () => process.exit(0))
    }
  }
  catch (error) {
    consola.error('Sertifika oluşturma hatası:', error)
  }
}

// Run the function if this is the main module
if (require.main === module
  || process.argv[1]?.endsWith('generate-certificates.exe')
  || process.argv[1]?.endsWith('vns-mizan-sertifika-olusturucu.exe')) {
  generateCertificates()
}
