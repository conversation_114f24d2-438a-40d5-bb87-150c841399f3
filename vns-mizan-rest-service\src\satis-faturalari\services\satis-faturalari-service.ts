import type { z } from 'zod'
import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import { transformToRestFormat } from '../transformers/rest-transformer.ts'
import { transformToSqlFormat } from '../transformers/sql-transformer.ts'
import SatisFaturaRestValidator from '../validators/rest-validator.ts'
import SatisFaturaSqlValidator from '../validators/sql-validator.ts'
import DatabaseService from './database-service.ts'
import SatisFaturalariLogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

/**
 * Simplified Sales Invoice Service using the new transformer pattern
 * This replaces the complex transformation logic with clean separation
 */

/**
 * Fatura yanıt tipi
 */
interface InvoiceResponse {
  status: string
  data?: any
  error?: string
  logoRef?: number
  ficheNo?: string
  irsaliyeNo?: string
}

/**
 * REST API yanıt tipi
 */
interface RestApiResponse {
  INTERNAL_REFERENCE: number
  DISPATCHES?: {
    items: {
      INTERNAL_REFERENCE?: number
      NUMBER?: string
      DATE?: string
      TIME?: number
    }[]
  }
}

const SatisFaturalariService = {

  /**
   * Ana fatura işleme fonksiyonu
   */
  async handleCreateInvoice({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Get Logo configuration to determine integration method
      const useRest = await LogoConfigService.getUseRestFlag(veritabaniId)

      if (useRest) {
        // Use REST API integration
        return await SatisFaturalariService.processWithRest({
          veritabaniId,
          requestPayload,
          logoCredentials,
        })
      }
      else {
        // Use direct SQL integration
        return await SatisFaturalariService.processWithSql({
          veritabaniId,
          requestPayload,
          logoCredentials,
        })
      }
    }
    catch (error: any) {
      consola.error('Fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'Bilinmeyen bir hata oluştu',
      }
    }
  },

  /**
   * REST API ile fatura işleme
   */
  async processWithRest({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for REST API
      const validationResult = await SatisFaturaRestValidator.validateSatisFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'REST doğrulama başarısız oldu',
        }
      }

      // Transform data for REST API
      const restData = await transformToRestFormat(requestPayload, veritabaniId)

      // Process line items (cost groups, currency types, etc.)
      await SatisFaturalariService.processInvoiceLineItems(restData, requestPayload, veritabaniId)

      // Send to Logo REST API
      const restResult = await SatisFaturalariService.sendInvoiceToRestApi(
        restData,
        veritabaniId,
        logoCredentials,
      )

      if (restResult.logoRestError) {
        // Save error to database
        await DatabaseService.insertSatisFatura({
          requestData: requestPayload,
          errorMessage: restResult.logoRestError,
          veritabaniId,
        })

        return {
          status: 'error',
          error: restResult.logoRestError,
        }
      }

      // Save successful result to database
      const logoRef = restResult.logoRestApiResponse?.INTERNAL_REFERENCE
      await DatabaseService.insertSatisFatura({
        requestData: requestPayload,
        logoFaturaNo: restResult.ficheNo,
        logoIrsaliyeNo: restResult.irsaliyeNo,
        logoFaturaLogicalRef: logoRef,
        veritabaniId,
      })

      return {
        status: 'success',
        logoRef,
        ficheNo: restResult.ficheNo,
        irsaliyeNo: restResult.irsaliyeNo,
        data: restResult.logoRestApiResponse,
      }
    }
    catch (error: any) {
      consola.error('REST API fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'REST API işlemi başarısız oldu',
      }
    }
  },

  /**
   * Direct SQL ile fatura işleme
   */
  async processWithSql({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for SQL integration
      const validationResult = await SatisFaturaSqlValidator.validateSatisFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'SQL doğrulama başarısız oldu',
        }
      }

      // Transform data for SQL integration
      const sqlData = await transformToSqlFormat(requestPayload, veritabaniId)

      // Process line items (cost groups, currency types, etc.)
      await SatisFaturalariService.processInvoiceLineItems(sqlData, requestPayload, veritabaniId)

      // Process with direct SQL
      const sqlResult = await LogoSqlService.processDirectSql({
        invoice: sqlData,
        veritabaniId,
        logoCredentials,
      })

      if (!sqlResult.success) {
        // Save error to database
        await DatabaseService.insertSatisFatura({
          requestData: requestPayload,
          errorMessage: sqlResult.error,
          veritabaniId,
        })

        return {
          status: 'error',
          error: sqlResult.error || 'SQL işlemi başarısız oldu',
        }
      }

      return {
        status: 'success',
        logoRef: sqlResult.logoRef,
        ficheNo: sqlResult.ficheNo,
        irsaliyeNo: sqlResult.irsaliyeNo,
      }
    }
    catch (error: any) {
      consola.error('SQL fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'SQL işlemi başarısız oldu',
      }
    }
  },

  /**
   * Fatura satırlarını işler ve maliyet grubu ve para birimi bilgilerini ayarlar
   */
  async processInvoiceLineItems(
    invoiceData: SatisFaturaInput,
    requestPayload: z.infer<typeof satisFaturalariSchema>,
    veritabaniId: string,
  ): Promise<void> {
    // Ana fatura için maliyet grubu ayarla
    if (invoiceData.INVOICE.SOURCE_WH) {
      const costGroup = await LogoSqlService.getSourceCostGrp({
        nr: invoiceData.INVOICE.SOURCE_WH,
        veritabaniId,
      })

      if (costGroup !== undefined) {
        invoiceData.INVOICE.SOURCE_COST_GRP = costGroup
      }
      else {
        consola.warn(`Ambar ${invoiceData.INVOICE.SOURCE_WH} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
        invoiceData.INVOICE.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP ?? 0
      }
    }

    // Fatura satırlarını işle
    if (invoiceData.TRANSACTIONS?.items && requestPayload.fatura_satirlari) {
      if (invoiceData.TRANSACTIONS.items.length === requestPayload.fatura_satirlari.length) {
        await Promise.all(invoiceData.TRANSACTIONS.items.map(async (lineItem, i) => {
          const requestLine = requestPayload.fatura_satirlari?.[i]

          // Her satır için maliyet grubu ayarla
          if (requestLine?.ambar_kodu) {
            const lineCostGroup = await LogoSqlService.getSourceCostGrp({
              nr: requestLine.ambar_kodu,
              veritabaniId,
            })

            if (lineCostGroup !== undefined) {
              lineItem.SOURCE_COST_GRP = lineCostGroup
            }
            else {
              // Satır için maliyet grubu bulunamazsa, ana faturanın maliyet grubunu kullan
              lineItem.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP
            }
          }
          else {
            // Satır için ambar kodu belirtilmemişse, ana faturanın maliyet grubunu kullan
            lineItem.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP
          }
        }))
      }
      else {
        consola.error('Dönüştürülen işlem kalemleri ile istek satırları arasında uyumsuzluk tespit edildi.')
        throw new Error('Fatura satırları işlenirken tutarsızlık tespit edildi (lookup).')
      }
    }
  },

  /**
   * Logo REST API ile fatura gönderimini gerçekleştirir
   */
  async sendInvoiceToRestApi(
    invoiceData: SatisFaturaInput,
    veritabaniId: string,
    logoCredentials?: { kullanici_adi: string, sifre: string },
  ): Promise<{
      logoRestApiResponse?: RestApiResponse
      logoRestError?: string
      ficheNo?: string
      irsaliyeNo?: string
    }> {
    let accessToken: string | null = null
    let logoRestApiResponse: RestApiResponse | undefined
    let logoRestError: string | undefined
    let ficheNo: string | undefined
    let irsaliyeNo: string | undefined

    try {
      accessToken = await SatisFaturalariLogoRestService.getToken({
        veritabaniId,
        logoCredentials,
      })
      logoRestApiResponse = await SatisFaturalariLogoRestService.postSatisFatura({
        accessToken,
        invoiceData,
        veritabaniId,
      })

      // LOGICALREF kullanarak FICHENO değerini al
      if (logoRestApiResponse?.INTERNAL_REFERENCE) {
        const invoiceInfo = await LogoSqlService.getInvoiceFichenoByLogicalref({
          logicalref: logoRestApiResponse.INTERNAL_REFERENCE,
          veritabaniId,
        })
        ficheNo = invoiceInfo?.ficheno

        // İrsaliye numarasını doğrudan REST API yanıtından al (eğer varsa)
        if (logoRestApiResponse.DISPATCHES?.items && logoRestApiResponse.DISPATCHES.items.length > 0) {
          const dispatchItem = logoRestApiResponse.DISPATCHES.items[0]
          if (dispatchItem && dispatchItem.NUMBER) {
            irsaliyeNo = dispatchItem.NUMBER
          }
        }
      }
    }
    catch (error: any) {
      logoRestError = error.message || 'REST API çağrısı başarısız oldu'
      consola.error('Logo REST API hatası:', error)
    }

    return {
      logoRestApiResponse,
      logoRestError,
      ficheNo,
      irsaliyeNo,
    }
  },
}

export default SatisFaturalariService
