{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "target": "ESNext",
    /* If your code doesn't run in the DOM: */
    "lib": ["es2022"],
    "moduleDetection": "force",
    "module": "NodeNext",
    "resolveJsonModule": true,
    /* Node Stuff */
    "allowImportingTsExtensions": true,
    "allowJs": true,
    /* Strictness */
    "strict": true,
    "noImplicitOverride": true,
    "noUncheckedIndexedAccess": true,
    /* Base Options: */
    "esModuleInterop": true,
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "erasableSyntaxOnly": true,
    "skipLibCheck": true,
    "rewriteRelativeImportExtensions": true
  },
  "include": ["src/**/*", "index.ts"]
}
