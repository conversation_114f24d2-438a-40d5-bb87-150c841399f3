import type { Server as HttpServer } from 'node:http'
import type { Config } from './src/shared/utils/config-utils.ts'
import cluster from 'node:cluster'
import os from 'node:os'
import process from 'node:process'
import { consola } from 'consola'
import { createMainRouter } from './src/rest-api/routes/index.ts'
import { createServer, gracefulShutdown } from './src/rest-api/services/server.ts'
import DbInitService from './src/shared/services/db-init-service.ts'
import DbService from './src/shared/services/db-service.ts'
import { readConfig } from './src/shared/utils/config-utils.ts'

const DB_READY_FLAG = 'VNS_DB_INITIALIZED'

interface ServerInstance {
  server: HttpServer | null
  isShuttingDown: boolean
}

const instance: ServerInstance = {
  server: null,
  isShuttingDown: false,
}

/**
 * Handles graceful shutdown of the master process
 */
async function masterShutdown() {
  if (instance.isShuttingDown)
    return
  instance.isShuttingDown = true

  consola.info('Ana sunucu kapanıyor, worker\'lar durdurulacak...')

  // Set a timeout for the entire shutdown process
  const forceExit = setTimeout(() => {
    consola.error('Worker\'lar yanıt vermedi, zorla kapatılıyor!')
    process.exit(1)
  }, 30000)

  // Notify all workers to shutdown
  for (const worker of Object.values(cluster.workers ?? {})) {
    if (!worker)
      continue
    worker.send('shutdown')
  }

  // Wait for all workers to exit
  await new Promise<void>((resolve) => {
    let remainingWorkers = Object.keys(cluster.workers ?? {}).length

    cluster.on('exit', (worker) => {
      consola.info(`${worker.process.pid} numaralı worker kapatıldı`)
      remainingWorkers--

      if (remainingWorkers === 0) {
        clearTimeout(forceExit)
        resolve()
      }
    })
  })

  consola.success('Tüm worker\'lar başarıyla kapatıldı, ana sunucu sonlandırılıyor')
  process.exit(0)
}

/**
 * Initializes the database in master process
 */
async function initializeDatabase() {
  try {
    process.env.TZ = 'Europe/Istanbul'
    const config: Config = await readConfig()
    process.env.NODE_ENV = config.node_env

    await DbInitService.initializeAll()
    process.env[DB_READY_FLAG] = 'true'
    consola.success('Veritabanı bağlantısı başarıyla kuruldu')
    return true
  }
  catch (error) {
    consola.error('Veritabanı bağlantısı kurulamadı:', error)
    return false
  }
}

/**
 * Starts a worker process for the Express server
 */
async function startWorker() {
  try {
    if (!process.env[DB_READY_FLAG]) {
      consola.error(`${process.pid} numaralı worker: Veritabanı hazır değil`)
      process.exit(1)
    }

    process.env.TZ = 'Europe/Istanbul'
    const config: Config = await readConfig()
    process.env.NODE_ENV = config.node_env

    const { app, server } = await createServer({
      port: config.project_settings.port,
      corsOrigins: ['*'],
      apiPrefix: '/v1',
      useHttps: config.project_settings.https?.enabled || false,
      httpsOptions: {
        key: config.project_settings.https?.key_path || '',
        cert: config.project_settings.https?.cert_path || '',
      },
    })

    instance.server = server
    app.use('/v1', createMainRouter())

    server.on('listening', () => {
      const protocol = config.project_settings.https?.enabled ? 'https' : 'http'
      consola.success(`${process.pid} numaralı worker hazır: ${protocol}://localhost:${config.project_settings.port}`)
    })

    server.on('error', (error: Error) => {
      consola.error(`${process.pid} numaralı worker'da hata oluştu:`, error)
      process.exit(1)
    })

    // Add message handler for worker
    process.on('message', async (msg) => {
      if (msg === 'shutdown') {
        consola.info(`${process.pid} numaralı worker kapatılıyor`)
        if (instance.server) {
          await gracefulShutdown(instance.server)
        }
        process.exit(0)
      }
    })
  }
  catch (error) {
    consola.error(`${process.pid} numaralı worker başlatılamadı:`, error)
    await DbService.closeAllPools()
    process.exit(1)
  }
}

function getWorkerCount({ project_settings }: Config) {
  const systemCpuCount = os.cpus().length
  const configuredCount = project_settings.instance_count
  if (!configuredCount)
    return systemCpuCount
  return Math.min(systemCpuCount, configuredCount)
}

async function startMaster() {
  const config = await readConfig()
  process.env.NODE_ENV = config.node_env
  const workerCount = getWorkerCount(config)
  consola.info(`Ana sunucu (${process.pid}) başlatılıyor`)
  consola.info(`Sistem ${os.cpus().length} çekirdekli, ${workerCount} worker başlatılacak`)

  const dbInitialized = await initializeDatabase()
  if (!dbInitialized) {
    consola.error('Veritabanı bağlantısı kurulamadığı için sunucu başlatılamıyor')
    process.exit(1)
  }
  for (let i = 0; i < workerCount; i++)
    cluster.fork()

  cluster.on('exit', (worker, code, signal) => {
    if (code !== 0)
      consola.warn(`${worker.process.pid} numaralı worker beklenmedik şekilde kapandı. Kod: ${code}, Sinyal: ${signal}`)
    cluster.fork() // New worker inherits env vars automatically
  })

  // Log when a worker comes online
  cluster.on('online', (worker) => {
    consola.info(`${worker.process.pid} numaralı worker çalışmaya başladı`)
  })
}

// Handle process signals for graceful shutdown
process.on('SIGTERM', async () => {
  if (cluster.isPrimary) {
    await masterShutdown()
  }
  else if (instance.server && !instance.isShuttingDown) {
    instance.isShuttingDown = true
    await gracefulShutdown(instance.server)
  }
})

process.on('SIGINT', async () => {
  if (cluster.isPrimary) {
    await masterShutdown()
  }
  else if (instance.server && !instance.isShuttingDown) {
    instance.isShuttingDown = true
    await gracefulShutdown(instance.server)
  }
})

// Handle uncaught errors
process.on('uncaughtException', async (error: Error) => {
  consola.error('Beklenmeyen bir hata oluştu:', error)
  if (cluster.isPrimary) {
    await masterShutdown()
  }
  else if (instance.server && !instance.isShuttingDown) {
    instance.isShuttingDown = true
    await gracefulShutdown(instance.server)
  }
})

process.on('unhandledRejection', async (reason: unknown) => {
  consola.error('İşlenmeyen bir Promise hatası:', reason)
  if (cluster.isPrimary) {
    await masterShutdown()
  }
  else if (instance.server && !instance.isShuttingDown) {
    instance.isShuttingDown = true
    await gracefulShutdown(instance.server)
  }
})

// Start the application based on process type
if (cluster.isPrimary)
  startMaster()
else
  startWorker()
