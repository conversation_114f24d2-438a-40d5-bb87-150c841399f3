/**
 * Types for the SatinalmaFaturalari module
 */

export interface SatinalmaFaturaLineItem {
  TYPE: number
  MASTER_CODE?: string
  SOURCEINDEX?: number
  SOURCE_COST_GRP?: number
  FACTORY?: number
  AUXIL_CODE?: string
  QUANTITY?: number
  PRICE?: number
  DESCRIPTION?: string
  UNIT_CODE?: string
  VAT_RATE?: number
  BILLED?: number
  EDT_PRICE?: number
  PROJECT_CODE?: string
  CANDEDUCT?: number
  DEDUCTION_PART1?: number
  DEDUCTION_PART2?: number
  DEDUCT_CODE?: string
  DEDUCT_DEF?: string
  OHP_CODE1?: string
  OHP_CODE3?: string
  OHP_CODE4?: string
}

export interface SatinalmaFaturaHeader {
  TYPE: number
  NUMBER: string
  DATE: string
  TIME: number
  DOC_NUMBER?: string
  AUXIL_CODE?: string
  ARP_CODE?: string
  SOURCE_WH?: number
  SOURCE_COST_GRP?: number
  FACTORY?: number
  NOTES1?: string
  NOTES2?: string
  NOTES3?: string
  NOTES4?: string
  NOTES5?: string
  NOTES6?: string
  CURR_INVOICE?: number
  TC_XRATE?: number
  RC_XRATE?: number
  DIVISION?: number
  DEPARTMENT?: number
  PAYMENT_CODE?: string
  CURRSEL_TOTALS?: number
  CURRSEL_DETAILS?: number
  PROJECT_CODE?: string
  AFFECT_RISK?: number
  DOC_DATE?: string
  DataObjectParameter?: {
    FillAccCodesOnPreSave?: boolean
  }
}

export interface SatinalmaFaturaInput {
  INVOICE: SatinalmaFaturaHeader
  TRANSACTIONS?: {
    items: SatinalmaFaturaLineItem[]
  }
  requestData?: any
}

// Database entity types for logging purposes
export interface SatinalmaFaturaEntity {
  id?: number
  fatura_turu: number
  fatura_no: string
  tarihi: string
  saati: string
  belge_no?: string
  ozel_kod?: string
  cari_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  aciklama?: string
  doviz_kuru?: number
  isyeri_kodu?: number
  bolum_kodu?: number
  odeme_kodu?: string
  proje_kodu?: string
  belge_tarihi?: string
  logo_fatura_no?: string
  logo_fatura_logicalref?: number
  invoice_data?: string // JSON string of LogoInvoice data
  stfiche_data?: string // JSON string of LogoStfiche data
  error?: string
  veritabani_id: string
  createdAt?: string
}

export interface SatinalmaFaturaSatirEntity {
  id?: number
  satinalma_fatura_id: number
  satir_turu: number
  malzeme_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  hareket_ozel_kodu?: string
  miktar?: number
  birim_fiyat?: number
  aciklama?: string
  birim_kodu?: string
  kdv_orani?: number
  proje_kodu?: string
  dovizli_birim_fiyat?: number
  tevkifat_yapilabilir?: number
  tevkifat_payi1?: number
  tevkifat_payi2?: number
  tevkifat_kodu?: string
  tevkifat_aciklamasi?: string
  masraf_merkezi1?: string
  masraf_merkezi3?: string
  masraf_merkezi4?: string
  stline_data?: string // JSON string of LogoStline data
  createdAt?: string
}

export interface InsertResult {
  id?: number
  error?: string
  success: boolean
}

// Endpoint request types - these represent the format expected in the HTTP request
export interface SatinalmaFaturaRequest {
  veritabani_id: string
  fatura_turu: number
  fatura_no?: string
  fatura_numarasi_formati?: string
  tarihi: string
  saati: string // HH:MM:SS format that will be converted to seconds
  belge_no?: string
  ozel_kod?: string
  cari_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  aciklama?: string
  doviz_kuru?: number
  isyeri_kodu?: number
  bolum_kodu?: number
  odeme_kodu?: string
  proje_kodu?: string
  belge_tarihi?: string
  fatura_satirlari?: SatinalmaFaturaSatirRequest[]
}

export interface SatinalmaFaturaSatirRequest {
  satir_turu: number
  malzeme_kodu?: string
  ambar_kodu?: number
  fabrika_kodu?: number
  hareket_ozel_kodu?: string
  miktar?: number
  birim_fiyat?: number
  aciklama?: string
  birim_kodu?: string
  kdv_orani?: number
  proje_kodu?: string
  dovizli_birim_fiyat?: number
  tevkifat_yapilabilir?: number
  tevkifat_payi1?: number
  tevkifat_payi2?: number
  tevkifat_kodu?: string
  tevkifat_aciklamasi?: string
  masraf_merkezi1?: string
  masraf_merkezi3?: string
  masraf_merkezi4?: string
}

// Logo ERP data structures (for JSON storage)
export interface LogoInvoiceData {
  LOGICALREF?: number
  GRPCODE?: number
  TRCODE?: number
  FICHENO?: string
  DATE_?: string
  TIME_?: number
  DOCODE?: string
  SPECODE?: string
  CYPHCODE?: string
  CLIENTREF?: number
  // ... other Logo Invoice fields as needed
}

export interface LogoStficheData {
  LOGICALREF?: number
  GRPCODE?: number
  TRCODE?: number
  IOCODE?: number
  FICHENO?: string
  DATE_?: string
  FTIME?: number
  DOCODE?: string
  INVNO?: string
  // ... other Logo Stfiche fields as needed
}

export interface LogoStlineData {
  LOGICALREF?: number
  STOCKREF?: number
  LINETYPE?: number
  PREVLINEREF?: number
  PREVLINENO?: number
  DETLINE?: number
  TRCODE?: number
  DATE_?: string
  FTIME?: number
  GLOBTRANS?: number
  CALCTYPE?: number
  // ... other Logo Stline fields as needed
}
