import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getMarkalar } from '../services/markalar-service.ts'

const router = Router()

/**
 * Get markalar validation schema
 */
const getMarkalarSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /markalar:
 *   get:
 *     tags: [Markalar]
 *     summary: Logo veritabanından markaları listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 required: [id, kodu, adi, veritabani_id]
 *                 properties:
 *                   id:
 *                     type: number
 *                     description: <PERSON><PERSON><PERSON><PERSON> ben<PERSON>'si
 *                   kodu:
 *                     type: string
 *                     description: Marka kodu
 *                   adi:
 *                     type: string
 *                     description: Marka adı
 *                   update_date:
 *                     type: string
 *                     format: date-time
 *                     description: Son güncelleme tarihi
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
const getMarkalarHandler: RequestHandler = async (req, res) => {
  try {
    const result = getMarkalarSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const markalar = await getMarkalar({ veritabaniId: veritabani_id })
    res.json(markalar)
  }
  catch (error) {
    consola.error('Markalar alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Markalar alınırken bir hata oluştu',
    })
  }
}

router.get('/', getMarkalarHandler)

export default router
