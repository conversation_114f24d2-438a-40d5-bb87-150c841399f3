import type { RequestHand<PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getBolumBilgileri } from '../services/bolum-service.ts'

const router = Router()

const getBolumBilgileriSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /bolumler:
 *   get:
 *     tags:
 *       - Bölüm Bilgileri
 *     summary: Logo veritabanından bölüm bilgilerini listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   bolum_no:
 *                     type: number
 *                   bolum_adi:
 *                     type: string
 *                   veritabani_id:
 *                     type: string
 *       400:
 *         description: Geçersiz istek
 *       500:
 *         description: <PERSON><PERSON><PERSON> hatası
 */
const getBolumBilgileriHandler: RequestHandler = async (req, res) => {
  try {
    const result = getBolumBilgileriSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const bolumBilgileri = await getBolumBilgileri({ veritabaniId: veritabani_id })
    res.json(bolumBilgileri)
  }
  catch (error) {
    consola.error('Bölüm bilgileri alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Bölüm bilgileri alınırken bir hata oluştu',
    })
  }
}

router.get('/', getBolumBilgileriHandler)

export default router
