# HTTPS Setup

This document explains the steps required to run the VNS Mizan Integration API with HTTPS.

## For Development Environment

You can use self-signed certificates in the development environment:

1. Generate certificates:
   ```
   pnpm generate:certs
   ```
   or
   ```
   dist/generate-certificates.exe
   ```

2. The generated certificates are stored in the `certs` folder:
   - `certs/key.pem`: Private key
   - `certs/cert.pem`: Certificate

3. Update your `config.json` file:
   ```json
   {
     "project_settings": {
       "https": {
         "enabled": true,
         "key_path": "certs/key.pem",
         "cert_path": "certs/cert.pem"
       }
     }
   }
   ```

4. Start the server:
   ```
   pnpm dev
   ```

## For Production Environment

In a production environment, you should use certificates signed by a real Certificate Authority (CA):

1. Obtain a certificate from a certificate authority (Let's Encrypt, DigiCert, etc.).

2. Place the certificate and private key files in a secure location.

3. Update your `config.json` file:
   ```json
   {
     "project_settings": {
       "https": {
         "enabled": true,
         "key_path": "/path/to/your/private-key.pem",
         "cert_path": "/path/to/your/certificate.pem"
       }
     }
   }
   ```

4. Start the server.

## Security Notes

- Self-signed certificates are only suitable for development environments.
- Always use certificates signed by a trusted certificate authority in production environments.
- Keep your private key secure and never include it in source control (git).
- The `certs` folder has been added to the `.gitignore` file.

## Troubleshooting

### SSL Protocol Error

If you encounter an `ERR_SSL_PROTOCOL_ERROR`:

1. Make sure the server is running with HTTPS.
2. Make sure your browser accepts the certificate (you may need to accept the security warning for self-signed certificates).
3. Make sure you are accessing the correct port.

### Certificate Generation Error

If you encounter an error during certificate generation:

1. Make sure OpenSSL is installed.
2. Verify that OpenSSL is working properly by running the `openssl version` command from the command line.
