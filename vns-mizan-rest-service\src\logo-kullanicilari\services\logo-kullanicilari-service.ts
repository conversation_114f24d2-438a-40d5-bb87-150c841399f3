import type { IResult } from 'mssql'
import type { LogoKullanici } from '../models/logo-kullanicilari-types.ts'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Get logo kullanıcıları from Logo database
 */
export async function getLogoKullanicilari({ veritabaniId }: { veritabaniId: string }): Promise<LogoKullanici[]> {
  try {
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const logoConfig = await getLogoConfigById(veritabaniId)
    const query = `
      SELECT 
        NR as numara,
        NAME as kullanici_adi,
        USERNAME as adi,
        USERSURNAME as soyadi,
        IIF(BLOCKED=0,1,0) as aktif,
        '${veritabaniId}' as veritabani_id
      FROM ${logoConfig.erp.logodb_master}..L_CAPIUSER
    `
    const result: IResult<LogoKullanici[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Logo kullanıcıları sorgulanırken hata:', error)
    throw error
  }
}
