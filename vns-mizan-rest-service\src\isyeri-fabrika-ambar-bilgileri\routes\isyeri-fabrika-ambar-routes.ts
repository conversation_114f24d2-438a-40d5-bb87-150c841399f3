import type { RequestHand<PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getIsyeriFabrikaAmbar } from '../services/isyeri-fabrika-ambar-service.ts'

const router = Router()

const getIsyeriFabrikaAmbarSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /isyeri-fabrika-ambar-bilgileri:
 *   get:
 *     tags:
 *       - İşyeri Fabrika Ambar
 *     summary: Logo veritabanından işyeri, fabrika ve ambar bilgilerini listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   isyeri_no:
 *                     type: number
 *                   isyeri_adi:
 *                     type: string
 *                   fabrika_no:
 *                     type: number
 *                   fabrika_adi:
 *                     type: string
 *                   ambar_no:
 *                     type: number
 *                   ambar_adi:
 *                     type: string
 *                   veritabani_id:
 *                     type: string
 *       400:
 *         description: Geçersiz istek
 *       500:
 *         description: Sunucu hatası
 */
const getIsyeriFabrikaAmbarHandler: RequestHandler = async (req, res) => {
  try {
    const result = getIsyeriFabrikaAmbarSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const isyeriFabrikaAmbar = await getIsyeriFabrikaAmbar({ veritabaniId: veritabani_id })
    res.json(isyeriFabrikaAmbar)
  }
  catch (error) {
    consola.error('İşyeri, fabrika ve ambar bilgileri alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'İşyeri, fabrika ve ambar bilgileri alınırken bir hata oluştu',
    })
  }
}

router.get('/', getIsyeriFabrikaAmbarHandler)

export default router
