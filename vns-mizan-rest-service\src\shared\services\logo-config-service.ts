import { consola } from 'consola'
import { getLogoConfigById } from '../utils/config-utils.ts'

/**
 * Shared Logo configuration service for common configuration operations
 * Used across all modules (cari-hesaplar, satis-faturalari, satinalma-faturalari, etc.)
 */
const LogoConfigService = {
  /**
   * Veritabanı yapılandırmasına göre REST API kullanılıp kullanılmayacağını belirler
   * @param veritabaniId Veritabanı ID
   * @returns REST API kullanılacak mı?
   */
  getUseRestFlag: async (veritabaniId: string): Promise<boolean> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      return !!logoConfig.erp.rest_settings.use_rest
    }
    catch (error) {
      consola.error(`${veritabaniId} için Logo yapılandırması alınırken hata oluştu:`, error)
      return true // Varsayılan olarak REST API kullan
    }
  },
}

export default LogoConfigService
