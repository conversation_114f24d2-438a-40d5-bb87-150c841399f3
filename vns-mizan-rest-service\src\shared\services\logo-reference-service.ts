import type { IResult } from 'mssql'
import consola from 'consola'
import sql from 'mssql'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

interface LogicalRefResult {
  LOGICALREF: number
}

interface ClientRefResult {
  LOGICALREF: number
}

interface UnitRefResult {
  LOGICALREF: number
}

/**
 * Service for reference lookup operations in Logo ERP
 */
const LogoReferenceService = {
  /**
   * Fetches the LOGICALREF from LG_SLSMAN based on salesman code.
   */
  getSalesmanRefFromCode: async (
    salesmanCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!salesmanCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically
      const tableName = `${logoConfig.erp.logodb_master}..LG_SLSMAN`

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM ${tableName}
        WHERE CODE = @salesmanCode
        AND FIRMNR in (${logoConfig.erp.firma_numarasi},-1)
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('salesmanCode', sql.VarChar, salesmanCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Satış temsilcisi referansı bulunamadı, kod: ${salesmanCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Satış temsilcisi referansı alınırken hata oluştu, kod: ${salesmanCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_XXX_PROJECT based on project code.
   */
  getProjectRefFromCode: async (
    projectCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!projectCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically with firm number
      const tableName = `LG_${logoConfig.erp.firma_numarasi}_PROJECT`

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM ${tableName}
        WHERE CODE = @projectCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('projectCode', sql.VarChar, projectCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Proje referansı bulunamadı, kod: ${projectCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Proje referansı alınırken hata oluştu, kod: ${projectCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_CLCARD based on client code.
   */
  getClientRefFromCode: async (
    clientCode: string | undefined,
    veritabaniId: string,
  ): Promise<{ logicalref: number } | undefined> => {
    if (!clientCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
        WHERE CODE = @clientCode
      `
      const result: IResult<ClientRefResult[]> = await logoConnection.request()
        .input('clientCode', sql.VarChar, clientCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return {
          logicalref: result.recordset[0].LOGICALREF,
        }
      }
      consola.warn(`Cari hesap referansı bulunamadı, kod: ${clientCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Cari hesap referansı alınırken hata oluştu, kod: ${clientCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_ITEMS based on item code.
   */
  getItemRefFromCode: async (
    itemCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!itemCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS
        WHERE CODE = @itemCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('itemCode', sql.VarChar, itemCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Malzeme referansı bulunamadı, kod: ${itemCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Malzeme referansı alınırken hata oluştu, kod: ${itemCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_SRVCARD based on service code.
   */
  getServiceRefFromCode: async (
    serviceCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!serviceCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD
        WHERE CODE = @serviceCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('serviceCode', sql.VarChar, serviceCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Hizmet referansı bulunamadı, kod: ${serviceCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Hizmet referansı alınırken hata oluştu, kod: ${serviceCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_UNITSETL based on unit code.
   */
  getUnitRefFromCode: async (
    unitCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!unitCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_UNITSETL
        WHERE CODE = @unitCode
      `
      const result: IResult<UnitRefResult[]> = await logoConnection.request()
        .input('unitCode', sql.VarChar, unitCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Birim referansı bulunamadı, kod: ${unitCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Birim referansı alınırken hata oluştu, kod: ${unitCode}:`, error)
      return undefined
    }
  },

  /**
   * Get user reference from L_CAPIUSER table
   */
  getUserRefFromCapiuser: async ({
    kullaniciAdi,
    veritabaniId,
  }: {
    kullaniciAdi: string
    veritabaniId: string
  }): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const { recordset } = await logoConnection
        .request()
        .input('kullaniciAdi', kullaniciAdi)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}..L_CAPIUSER
          WHERE NAME = @kullaniciAdi
        `)

      return recordset?.[0]?.NR
    }
    catch (error) {
      consola.error(`Logo kullanıcı referansı alınırken hata oluştu:`, error)
      return undefined
    }
  },
}

export default LogoReferenceService
