import type { SatisFaturaHeader, SatisFaturaLineItem } from '../models/sales-invoice.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for direct Logo ERP integration operations
 * Contains the actual implementations for inserting into Logo ERP tables
 */
const LogoErpIntegration = {
  /**
   * Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
   */
  insertLogoActualInvoice: async ({
    invoice,
    veritabaniId,
    requestData,
    totals,
    logoCredentials,
  }: {
    invoice: SatisFaturaHeader
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId,
        })
      }

      // Generate a unique fiche number if not provided
      let ficheno = invoice.NUMBER
      if (invoice.NUMBER === '~') {
        // Check if a format is provided
        const format = requestData?.fatura_numarasi_formati

        if (format && format.includes('_')) {
          // Use formatted fiche number generation
          ficheno = await LogoLookupService.generateFormattedFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            format,
            veritabaniId,
          })
        }
        else {
          // Use default fiche number generation
          ficheno = await LogoLookupService.generateNewFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            veritabaniId,
          })
        }
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const _currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided
      const totalVat = totals?.totalVat || 0
      const totalNet = totals?.totalNet || 0
      const totalGross = totals?.totalGross || 0
      const totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(
          invoice.DATE,
          invoice.CURR_INVOICE,
          veritabaniId,
        )
        tcXrate = exchangeRate || 1
        rcXrate = exchangeRate || 1
      }

      // Map invoice fields to Logo INVOICE table structure based on the provided JSON example
      await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, invoice.TYPE)
        .input('FICHENO', sql.VarChar(17), ficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('TIME_', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE || '')
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('CLIENTREF', sql.Int, clientRef || 0)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('ACCOUNTREF', sql.Int, 0) // Account reference
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('ACCOUNTED', sql.SmallInt, 0)
        .input('PAIDINCASH', sql.SmallInt, 0)
        .input('FROMKASA', sql.SmallInt, 0)
        .input('ENTEGSET', sql.SmallInt, 247) // Based on JSON example
        .input('VAT', sql.Float, 0)
        .input('ADDDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTED', sql.Float, totalDiscounted)
        .input('ADDEXPENSES', sql.Float, 0)
        .input('TOTALEXPENSES', sql.Float, 0)
        .input('DISTEXPENSE', sql.Float, 0)
        .input('TOTALDEPOZITO', sql.Float, 0)
        .input('TOTALPROMOTIONS', sql.Float, 0)
        .input('VATINCGROSS', sql.Float, 0) // Based on JSON example
        .input('TOTALVAT', sql.Float, totalVat)
        .input('GROSSTOTAL', sql.Float, totalGross)
        .input('NETTOTAL', sql.Float, totalNet)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1 || '')
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2 || '')
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3 || '')
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4 || '')
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5 || '')
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6 || '')
        .input('INTERESTAPP', sql.Float, 0)
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('TRNET', sql.Float, totalNet / tcXrate)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, totalNet / rcXrate)
        .input('ONLYONEPAYLINE', sql.SmallInt, 0)
        .input('KASTRANSREF', sql.Int, 0)
        .input('PAYDEFREF', sql.Int, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('GVATINC', sql.SmallInt, 0) // Based on JSON example
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('ACCFICHEREF', sql.Int, 0)
        .input('ADDEXPACCREF', sql.Int, 0)
        .input('ADDEXPCENTREF', sql.Int, 0)
        .input('DECPRDIFF', sql.Float, 0)
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREADEDDATE', sql.DateTime, now) // Note: CREADEDDATE not CREATEDDATE
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .input('SALESMANREF', sql.Int, salesmanRef || 0)
        .input('CANCELLEDACC', sql.SmallInt, 0)
        .input('SHPTYPCOD', sql.VarChar(13), '')
        .input('SHPAGNCOD', sql.VarChar(13), '')
        .input('TRACKNR', sql.VarChar(17), '')
        .input('GENEXCTYP', sql.SmallInt, 0)
        .input('LINEEXCTYP', sql.SmallInt, 0)
        .input('TRADINGGRP', sql.VarChar(17), '')
        .input('TEXTINC', sql.SmallInt, 0)
        .input('SITEID', sql.SmallInt, 0)
        .input('RECSTATUS', sql.SmallInt, 0)
        .input('ORGLOGICREF', sql.Int, 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('WFSTATUS', sql.SmallInt, 0)
        .input('SHIPINFOREF', sql.Int, 0)
        .input('DISTORDERREF', sql.Int, 0)
        .input('SENDCNT', sql.SmallInt, 0)
        .input('DLVCLIENT', sql.Int, 0)
        .input('COSTOFSALEFCREF', sql.Int, 0)
        .input('OPSTAT', sql.SmallInt, 0)
        .input('DOCTRACKINGNR', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('TOTALADDTAX', sql.Float, 0)
        .input('PAYMENTTYPE', sql.SmallInt, 0)
        .input('INFIDX', sql.Float, 0)
        .input('ACCOUNTEDCNT', sql.SmallInt, 0)
        .input('ORGLOGOID', sql.VarChar(25), '')
        .input('FROMEXIM', sql.SmallInt, 0)
        .input('FRGTYPCOD', sql.VarChar(17), '')
        .input('EXIMFCTYPE', sql.SmallInt, 0)
        .input('FROMORDWITHPAY', sql.SmallInt, 0)
        .input('PROJECTREF', sql.Int, projectRef || 0)
        .input('WFLOWCRDREF', sql.Int, 0)
        .input('STATUS', sql.SmallInt, 0)
        .input('DEDUCTIONPART1', sql.SmallInt, 0)
        .input('DEDUCTIONPART2', sql.SmallInt, 0)
        .input('TOTALEXADDTAX', sql.Float, 0)
        .input('EXACCOUNTED', sql.SmallInt, 0)
        .input('FROMBANK', sql.SmallInt, 0)
        .input('BNTRANSREF', sql.Int, 0)
        .input('AFFECTCOLLATRL', sql.SmallInt, 0)
        .input('GRPFIRMTRANS', sql.SmallInt, 0)
        .input('AFFECTRISK', sql.SmallInt, 0)
        .input('CONTROLINFO', sql.SmallInt, 0)
        .input('POSTRANSFERINFO', sql.SmallInt, 0)
        .input('TAXFREECHX', sql.SmallInt, 0)
        .input('PASSPORTNO', sql.VarChar(15), '')
        .input('CREDITCARDNO', sql.VarChar(16), '')
        .input('INEFFECTIVECOST', sql.SmallInt, 0)
        .input('REFLECTED', sql.SmallInt, 0)
        .input('REFLACCFICHEREF', sql.Int, 0)
        .input('CANCELLEDREFLACC', sql.SmallInt, 0)
        .input('CREDITCARDNUM', sql.VarChar(16), '')
        .input('APPROVE', sql.SmallInt, 0)
        .input('CANTCREDEDUCT', sql.SmallInt, 0)
        .input('ENTRUST', sql.SmallInt, 0)
        .input('EINVOICE', sql.SmallInt, 0)
        .input('PROFILEID', sql.SmallInt, invoice.PROFILE_ID || 1)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('ESTATUS', sql.SmallInt, 0)
        .input('EDESCRIPTION', sql.VarChar(51), '')
        .input('EDURATION', sql.Float, 0)
        .input('EDURATIONTYPE', sql.SmallInt, 0)
        .input('DEVIR', sql.SmallInt, 0)
        .input('DISTADJPRICEUFRS', sql.Float, 0)
        .input('COSFCREFUFRS', sql.Int, 0)
        .input('GLOBALID', sql.VarChar(51), '')
        .input('TOTALSERVICES', sql.Float, 0)
        .input('FROMLEASING', sql.SmallInt, 0)
        .input('CANCELEXP', sql.VarChar(51), '')
        .input('UNDOEXP', sql.VarChar(51), '')
        .input('VATEXCEPTREASON', sql.VarChar(51), '')
        .input('CAMPAIGNCODE', sql.VarChar(51), '')
        .input('CANCELDESPSINV', sql.SmallInt, 0)
        .input('FROMEXCHDIFF', sql.SmallInt, 0)
        .input('EXIMVAT', sql.SmallInt, 0)
        .input('SERIALCODE', sql.VarChar(17), '')
        .input('APPCLDEDUCTLIM', sql.SmallInt, 0)
        .input('EINVOICETYP', sql.SmallInt, 0)
        .input('VATEXCEPTCODE', sql.VarChar(51), '')
        .input('OFFERREF', sql.Int, 0)
        .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
        .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
        .input('FROMSTAFFOTHEREX', sql.SmallInt, 0)
        .input('NOCALCULATE', sql.SmallInt, 0)
        .input('INSTEADOFDESP', sql.SmallInt, 0)
        .input('OKCFICHE', sql.SmallInt, 0)
        .input('FRGTYPDESC', sql.VarChar(51), '')
        .input('MARKREF', sql.Int, 0)
        .input('DELIVERCODE', sql.VarChar(51), '')
        .input('ACCEPTEINVPUBLIC', sql.SmallInt, 0)
        .input('PUBLICBNACCREF', sql.Int, 0)
        .input('TYPECODE', sql.VarChar(51), '')
        .input('FUTMNTHYREXPINC', sql.SmallInt, 0)
        .input('DOCDETAIL', sql.SmallInt, 0)
        .input('CALCADDTAXVATSEP', sql.SmallInt, 0)
        .input('ELECTDOC', sql.SmallInt, 0)
        .input('NOTIFYCRDREF', sql.Int, 0)
        .input('GIBACCFICHEREF', sql.Int, 0)
        .input('FROMINTEGTYPE', sql.SmallInt, 0)
        .input('EPRINTCNT', sql.SmallInt, 0)
        .input('RIGHTOFRETURNTYP', sql.VarChar(51), '')
        .input('CLNOTREFLACNTRREF', sql.Int, 0)
        .input('CLNOTREFLAACCREF', sql.Int, 0)
        .input('COSFCREFINFL', sql.Int, 0)
        .input('ORDFICHECMREF', sql.Int, 0)
        .input('ESENDTIME', sql.Int, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE (
            GRPCODE, TRCODE, FICHENO, DATE_, TIME_, DOCODE, SPECODE, CYPHCODE,
            CLIENTREF, RECVREF, CENTERREF, ACCOUNTREF, SOURCEINDEX, SOURCECOSTGRP,
            CANCELLED, ACCOUNTED, PAIDINCASH, FROMKASA, ENTEGSET, VAT,
            ADDDISCOUNTS, TOTALDISCOUNTS, TOTALDISCOUNTED, ADDEXPENSES, TOTALEXPENSES,
            DISTEXPENSE, TOTALDEPOZITO, TOTALPROMOTIONS, VATINCGROSS, TOTALVAT,
            GROSSTOTAL, NETTOTAL, GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6,
            INTERESTAPP, TRCURR, TRRATE, TRNET, REPORTRATE, REPORTNET,
            ONLYONEPAYLINE, KASTRANSREF, PAYDEFREF, PRINTCNT, GVATINC, BRANCH, DEPARTMENT,
            ACCFICHEREF, ADDEXPACCREF, ADDEXPCENTREF, DECPRDIFF,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREADEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC, SALESMANREF, CANCELLEDACC, SHPTYPCOD, SHPAGNCOD,
            TRACKNR, GENEXCTYP, LINEEXCTYP, TRADINGGRP, TEXTINC, SITEID, RECSTATUS,
            ORGLOGICREF, FACTORYNR, WFSTATUS, SHIPINFOREF, DISTORDERREF, SENDCNT,
            DLVCLIENT, COSTOFSALEFCREF, OPSTAT, DOCTRACKINGNR, TOTALADDTAX,
            PAYMENTTYPE, INFIDX, ACCOUNTEDCNT, ORGLOGOID, FROMEXIM, FRGTYPCOD,
            EXIMFCTYPE, FROMORDWITHPAY, PROJECTREF, WFLOWCRDREF, STATUS,
            DEDUCTIONPART1, DEDUCTIONPART2, TOTALEXADDTAX, EXACCOUNTED, FROMBANK,
            BNTRANSREF, AFFECTCOLLATRL, GRPFIRMTRANS, AFFECTRISK, CONTROLINFO,
            POSTRANSFERINFO, TAXFREECHX, PASSPORTNO, CREDITCARDNO, INEFFECTIVECOST,
            REFLECTED, REFLACCFICHEREF, CANCELLEDREFLACC, CREDITCARDNUM, APPROVE,
            CANTCREDEDUCT, ENTRUST, EINVOICE, PROFILEID, GUID, ESTATUS, EDESCRIPTION,
            EDURATION, EDURATIONTYPE, DEVIR, DISTADJPRICEUFRS, COSFCREFUFRS,
            GLOBALID, TOTALSERVICES, FROMLEASING, CANCELEXP, UNDOEXP, VATEXCEPTREASON,
            CAMPAIGNCODE, CANCELDESPSINV, FROMEXCHDIFF, EXIMVAT, SERIALCODE,
            APPCLDEDUCTLIM, EINVOICETYP, VATEXCEPTCODE, OFFERREF, ATAXEXCEPTREASON,
            ATAXEXCEPTCODE, FROMSTAFFOTHEREX, NOCALCULATE, INSTEADOFDESP, OKCFICHE,
            FRGTYPDESC, MARKREF, DELIVERCODE, ACCEPTEINVPUBLIC, PUBLICBNACCREF,
            TYPECODE, FUTMNTHYREXPINC, DOCDETAIL, CALCADDTAXVATSEP, ELECTDOC,
            NOTIFYCRDREF, GIBACCFICHEREF, FROMINTEGTYPE, EPRINTCNT, RIGHTOFRETURNTYP,
            CLNOTREFLACNTRREF, CLNOTREFLAACCREF, COSFCREFINFL, ORDFICHECMREF, ESENDTIME
          )
          VALUES (
            @GRPCODE, @TRCODE, @FICHENO, @DATE_, @TIME_, @DOCODE, @SPECODE, @CYPHCODE,
            @CLIENTREF, @RECVREF, @CENTERREF, @ACCOUNTREF, @SOURCEINDEX, @SOURCECOSTGRP,
            @CANCELLED, @ACCOUNTED, @PAIDINCASH, @FROMKASA, @ENTEGSET, @VAT,
            @ADDDISCOUNTS, @TOTALDISCOUNTS, @TOTALDISCOUNTED, @ADDEXPENSES, @TOTALEXPENSES,
            @DISTEXPENSE, @TOTALDEPOZITO, @TOTALPROMOTIONS, @VATINCGROSS, @TOTALVAT,
            @GROSSTOTAL, @NETTOTAL, @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6,
            @INTERESTAPP, @TRCURR, @TRRATE, @TRNET, @REPORTRATE, @REPORTNET,
            @ONLYONEPAYLINE, @KASTRANSREF, @PAYDEFREF, @PRINTCNT, @GVATINC, @BRANCH, @DEPARTMENT,
            @ACCFICHEREF, @ADDEXPACCREF, @ADDEXPCENTREF, @DECPRDIFF,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREADEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC, @SALESMANREF, @CANCELLEDACC, @SHPTYPCOD, @SHPAGNCOD,
            @TRACKNR, @GENEXCTYP, @LINEEXCTYP, @TRADINGGRP, @TEXTINC, @SITEID, @RECSTATUS,
            @ORGLOGICREF, @FACTORYNR, @WFSTATUS, @SHIPINFOREF, @DISTORDERREF, @SENDCNT,
            @DLVCLIENT, @COSTOFSALEFCREF, @OPSTAT, @DOCTRACKINGNR, @TOTALADDTAX,
            @PAYMENTTYPE, @INFIDX, @ACCOUNTEDCNT, @ORGLOGOID, @FROMEXIM, @FRGTYPCOD,
            @EXIMFCTYPE, @FROMORDWITHPAY, @PROJECTREF, @WFLOWCRDREF, @STATUS,
            @DEDUCTIONPART1, @DEDUCTIONPART2, @TOTALEXADDTAX, @EXACCOUNTED, @FROMBANK,
            @BNTRANSREF, @AFFECTCOLLATRL, @GRPFIRMTRANS, @AFFECTRISK, @CONTROLINFO,
            @POSTRANSFERINFO, @TAXFREECHX, @PASSPORTNO, @CREDITCARDNO, @INEFFECTIVECOST,
            @REFLECTED, @REFLACCFICHEREF, @CANCELLEDREFLACC, @CREDITCARDNUM, @APPROVE,
            @CANTCREDEDUCT, @ENTRUST, @EINVOICE, @PROFILEID, @GUID, @ESTATUS, @EDESCRIPTION,
            @EDURATION, @EDURATIONTYPE, @DEVIR, @DISTADJPRICEUFRS, @COSFCREFUFRS,
            @GLOBALID, @TOTALSERVICES, @FROMLEASING, @CANCELEXP, @UNDOEXP, @VATEXCEPTREASON,
            @CAMPAIGNCODE, @CANCELDESPSINV, @FROMEXCHDIFF, @EXIMVAT, @SERIALCODE,
            @APPCLDEDUCTLIM, @EINVOICETYP, @VATEXCEPTCODE, @OFFERREF, @ATAXEXCEPTREASON,
            @ATAXEXCEPTCODE, @FROMSTAFFOTHEREX, @NOCALCULATE, @INSTEADOFDESP, @OKCFICHE,
            @FRGTYPDESC, @MARKREF, @DELIVERCODE, @ACCEPTEINVPUBLIC, @PUBLICBNACCREF,
            @TYPECODE, @FUTMNTHYREXPINC, @DOCDETAIL, @CALCADDTAXVATSEP, @ELECTDOC,
            @NOTIFYCRDREF, @GIBACCFICHEREF, @FROMINTEGTYPE, @EPRINTCNT, @RIGHTOFRETURNTYP,
            @CLNOTREFLACNTRREF, @CLNOTREFLAACCREF, @COSFCREFINFL, @ORDFICHECMREF, @ESENDTIME
          )
        `)

      // Get the inserted record's LOGICALREF
      const selectResult = await logoConnection.request()
        .input('FICHENO', sql.VarChar(17), ficheno)
        .query(`
          SELECT TOP 1 LOGICALREF, FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE FICHENO = @FICHENO
          ORDER BY LOGICALREF DESC
        `)

      if (selectResult.recordset.length > 0 && selectResult.recordset[0]) {
        const insertedRecord = selectResult.recordset[0]
        consola.success(`Logo INVOICE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO,
        }
      }

      throw new Error('INVOICE tablosuna ekleme başarısız oldu - LOGICALREF alınamadı')
    }
    catch (error) {
      consola.error('Logo INVOICE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },

  /**
   * Insert into actual Logo STFICHE table (LG_{FFF}_{DD}_STFICHE)
   */
  insertLogoActualStfiche: async ({
    invoice,
    invoiceRef,
    ficheNo,
    veritabaniId,
    requestData,
    totals,
    logoCredentials,
  }: {
    invoice: SatisFaturaHeader
    invoiceRef: number
    ficheNo: string
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId,
        })
      }

      // Generate a unique stfiche number
      let stficheno
      // Check if a format is provided for irsaliye_no
      const irsaliyeFormat = requestData?.irsaliye_numarasi_formati

      if (irsaliyeFormat && irsaliyeFormat.includes('_')) {
        // Use formatted fiche number generation
        stficheno = await LogoLookupService.generateFormattedFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          format: irsaliyeFormat,
          veritabaniId,
        })
      }
      else {
        // Use default fiche number generation
        stficheno = await LogoLookupService.generateNewFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          veritabaniId,
        })
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const _currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided (not used in STFICHE but kept for consistency)
      const _totalVat = totals?.totalVat || 0
      const _totalNet = totals?.totalNet || 0
      const _totalGross = totals?.totalGross || 0
      const _totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(
          invoice.DATE,
          invoice.CURR_INVOICE,
          veritabaniId,
        )
        tcXrate = exchangeRate || 1
        rcXrate = exchangeRate || 1
      }

      // Map invoice fields to Logo STFICHE table structure based on the provided JSON example
      await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, 7) // 7 is for sales invoice in STFICHE
        .input('IOCODE', sql.SmallInt, 3) // 3 is for output
        .input('FICHENO', sql.VarChar(17), stficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('FTIME', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('INVNO', sql.VarChar(17), ficheNo)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE || '')
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('INVOICEREF', sql.Int, invoiceRef)
        .input('CLIENTREF', sql.Int, clientRef || 0)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('ACCOUNTREF', sql.Int, 0) // Account reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('PRODORDERREF', sql.Int, 0) // Production order reference
        .input('PORDERFICHENO', sql.VarChar(17), '') // Production order fiche number
        .input('SOURCETYPE', sql.SmallInt, 0)
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCEWSREF', sql.Int, 0)
        .input('SOURCEPOLNREF', sql.Int, 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('DESTTYPE', sql.SmallInt, 0)
        .input('DESTINDEX', sql.SmallInt, 0)
        .input('DESTWSREF', sql.Int, 0)
        .input('DESTPOLNREF', sql.Int, 0)
        .input('DESTCOSTGRP', sql.SmallInt, 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('COMPBRANCH', sql.SmallInt, 0)
        .input('COMPDEPARTMENT', sql.SmallInt, 0)
        .input('COMPFACTORY', sql.SmallInt, 0)
        .input('PRODSTAT', sql.SmallInt, 0)
        .input('DEVIR', sql.SmallInt, 0)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('BILLED', sql.SmallInt, 1)
        .input('ACCOUNTED', sql.SmallInt, 0)
        .input('UPDCURR', sql.SmallInt, 0)
        .input('INUSE', sql.SmallInt, 0)
        .input('INVKIND', sql.SmallInt, 0)
        .input('ADDDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTED', sql.Float, totals?.totalDiscounted || 0)
        .input('ADDEXPENSES', sql.Float, 0)
        .input('TOTALEXPENSES', sql.Float, 0)
        .input('TOTALDEPOZITO', sql.Float, 0)
        .input('TOTALPROMOTIONS', sql.Float, 0)
        .input('TOTALVAT', sql.Float, totals?.totalVat || 0)
        .input('GROSSTOTAL', sql.Float, totals?.totalGross || 0)
        .input('NETTOTAL', sql.Float, totals?.totalNet || 0)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1 || '')
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2 || '')
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3 || '')
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4 || '')
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5 || '')
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6 || '')
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, (totals?.totalNet || 0) / rcXrate)
        .input('EXTENREF', sql.Int, 0)
        .input('PAYDEFREF', sql.Int, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('FICHECNT', sql.SmallInt, 1)
        .input('ACCFICHEREF', sql.Int, 0)
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREADEDDATE', sql.DateTime, now) // Note: CREADEDDATE not CREATEDDATE
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .input('SALESMANREF', sql.Int, salesmanRef || 0)
        .input('CANCELLEDACC', sql.SmallInt, 0)
        .input('SHPTYPCOD', sql.VarChar(13), '')
        .input('SHPAGNCOD', sql.VarChar(13), '')
        .input('TRACKNR', sql.VarChar(17), '')
        .input('GENEXCTYP', sql.SmallInt, 0)
        .input('LINEEXCTYP', sql.SmallInt, 0)
        .input('TRADINGGRP', sql.VarChar(17), '')
        .input('TEXTINC', sql.SmallInt, 0)
        .input('SITEID', sql.SmallInt, 0)
        .input('RECSTATUS', sql.SmallInt, 0)
        .input('ORGLOGICREF', sql.Int, 0)
        .input('WFSTATUS', sql.SmallInt, 0)
        .input('SHIPINFOREF', sql.Int, 0)
        .input('DISTORDERREF', sql.Int, 0)
        .input('SENDCNT', sql.SmallInt, 0)
        .input('DLVCLIENT', sql.Int, 0)
        .input('DOCTRACKINGNR', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('ADDTAXCALC', sql.SmallInt, 0)
        .input('TOTALADDTAX', sql.Float, 0)
        .input('UGIRTRACKINGNO', sql.VarChar(51), '')
        .input('QPRODFCREF', sql.Int, 0)
        .input('VAACCREF', sql.Int, 0)
        .input('VACENTERREF', sql.Int, 0)
        .input('ORGLOGOID', sql.VarChar(25), '')
        .input('FROMEXIM', sql.SmallInt, 0)
        .input('FRGTYPCOD', sql.VarChar(17), '')
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('TRNET', sql.Float, (totals?.totalNet || 0) / tcXrate)
        .input('EXIMWHFCREF', sql.Int, 0)
        .input('EXIMFCTYPE', sql.SmallInt, 0)
        .input('MAINSTFCREF', sql.Int, 0)
        .input('FROMORDWITHPAY', sql.SmallInt, 0)
        .input('PROJECTREF', sql.Int, projectRef || 0)
        .input('WFLOWCRDREF', sql.Int, 0)
        .input('STATUS', sql.SmallInt, 0)
        .input('UPDTRCURR', sql.SmallInt, 0)
        .input('TOTALEXADDTAX', sql.Float, 0)
        .input('AFFECTCOLLATRL', sql.SmallInt, 0)
        .input('DEDUCTIONPART1', sql.SmallInt, 0)
        .input('DEDUCTIONPART2', sql.SmallInt, 0)
        .input('GRPFIRMTRANS', sql.SmallInt, 0)
        .input('AFFECTRISK', sql.SmallInt, 0)
        .input('DISPSTATUS', sql.SmallInt, 0)
        .input('APPROVE', sql.SmallInt, 0)
        .input('CANTCREDEDUCT', sql.SmallInt, 0)
        .input('SHIPDATE', sql.DateTime, requestData?.irsaliye_tarihi ? new Date(requestData.irsaliye_tarihi) : new Date(invoice.DATE))
        .input('SHIPTIME', sql.Int, invoice.TIME)
        .input('ENTRUSTDEVIR', sql.SmallInt, 0)
        .input('RELTRANSFCREF', sql.Int, 0)
        .input('FROMTRANSFER', sql.SmallInt, 0)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('GLOBALID', sql.VarChar(51), '')
        .input('COMPSTFCREF', sql.Int, 0)
        .input('COMPINVREF', sql.Int, 0)
        .input('TOTALSERVICES', sql.Float, 0)
        .input('CAMPAIGNCODE', sql.VarChar(51), '')
        .input('OFFERREF', sql.Int, 0)
        .input('EINVOICETYP', sql.SmallInt, 0)
        .input('EINVOICE', sql.SmallInt, 0)
        .input('NOCALCULATE', sql.SmallInt, 0)
        .input('PRODORDERTYP', sql.SmallInt, 0)
        .input('QPRODFCTYP', sql.SmallInt, 0)
        .input('PRDORDSLPLNRESERVE', sql.SmallInt, 0)
        .input('CONTROLINFO', sql.SmallInt, 0)
        .input('EDESPATCH', sql.SmallInt, 0)
        .input('DOCTIME', sql.Int, invoice.TIME)
        .input('EDESPSTATUS', sql.SmallInt, 0)
        .input('PROFILEID', sql.SmallInt, 0)
        .input('DELIVERYCODE', sql.VarChar(51), '')
        .input('DESTSTATUS', sql.SmallInt, 0)
        .input('CANCELEXP', sql.VarChar(51), '')
        .input('UNDOEXP', sql.VarChar(51), '')
        .input('CREATEWHERE', sql.SmallInt, 0)
        .input('PUBLICBNACCREF', sql.Int, 0)
        .input('ACCEPTEINVPUBLIC', sql.SmallInt, 0)
        .input('VATEXCEPTCODE', sql.VarChar(51), '')
        .input('VATEXCEPTREASON', sql.VarChar(51), '')
        .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
        .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
        .input('TAXFREECHX', sql.SmallInt, 0)
        .input('MNTORDERFREF', sql.Int, 0)
        .input('PRINTEDDESPFCNO', sql.VarChar(17), '')
        .input('OKCFICHE', sql.SmallInt, 0)
        .input('NOTIFYCRDREF', sql.Int, 0)
        .input('CANCELLEDINVREF1', sql.Int, 0)
        .input('CANCELLEDINVREF2', sql.Int, 0)
        .input('CANCELLEDINVREF3', sql.Int, 0)
        .input('CANCELLEDINVREF4', sql.Int, 0)
        .input('FROMINTEGTYPE', sql.SmallInt, 0)
        .input('FROMINTEGREF', sql.Int, 0)
        .input('EPRINTCNT', sql.SmallInt, 0)
        .input('CLNOTREFLACNTRREF', sql.Int, 0)
        .input('CLNOTREFLAACCREF', sql.Int, 0)
        .input('FORENTRUST', sql.SmallInt, 0)
        .input('PAYERCRKEY', sql.VarChar(51), '')
        .input('PAYERCRPROVIDER', sql.VarChar(51), '')
        .input('ORDFICHECMREF', sql.Int, 0)
        .input('ESENDTIME', sql.Int, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE (
            GRPCODE, TRCODE, IOCODE, FICHENO, DATE_, FTIME, DOCODE, INVNO, SPECODE, CYPHCODE,
            INVOICEREF, CLIENTREF, RECVREF, ACCOUNTREF, CENTERREF, PRODORDERREF, PORDERFICHENO,
            SOURCETYPE, SOURCEINDEX, SOURCEWSREF, SOURCEPOLNREF, SOURCECOSTGRP, DESTTYPE,
            DESTINDEX, DESTWSREF, DESTPOLNREF, DESTCOSTGRP, FACTORYNR, BRANCH, DEPARTMENT,
            COMPBRANCH, COMPDEPARTMENT, COMPFACTORY, PRODSTAT, DEVIR, CANCELLED, BILLED,
            ACCOUNTED, UPDCURR, INUSE, INVKIND, ADDDISCOUNTS, TOTALDISCOUNTS, TOTALDISCOUNTED,
            ADDEXPENSES, TOTALEXPENSES, TOTALDEPOZITO, TOTALPROMOTIONS, TOTALVAT, GROSSTOTAL,
            NETTOTAL, GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6, REPORTRATE,
            REPORTNET, EXTENREF, PAYDEFREF, PRINTCNT, FICHECNT, ACCFICHEREF,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREADEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC, SALESMANREF, CANCELLEDACC, SHPTYPCOD, SHPAGNCOD,
            TRACKNR, GENEXCTYP, LINEEXCTYP, TRADINGGRP, TEXTINC, SITEID, RECSTATUS,
            ORGLOGICREF, WFSTATUS, SHIPINFOREF, DISTORDERREF, SENDCNT, DLVCLIENT,
            DOCTRACKINGNR, ADDTAXCALC, TOTALADDTAX, UGIRTRACKINGNO, QPRODFCREF,
            VAACCREF, VACENTERREF, ORGLOGOID, FROMEXIM, FRGTYPCOD, TRCURR, TRRATE,
            TRNET, EXIMWHFCREF, EXIMFCTYPE, MAINSTFCREF, FROMORDWITHPAY, PROJECTREF,
            WFLOWCRDREF, STATUS, UPDTRCURR, TOTALEXADDTAX, AFFECTCOLLATRL, DEDUCTIONPART1,
            DEDUCTIONPART2, GRPFIRMTRANS, AFFECTRISK, DISPSTATUS, APPROVE, CANTCREDEDUCT,
            SHIPDATE, SHIPTIME, ENTRUSTDEVIR, RELTRANSFCREF, FROMTRANSFER, GUID, GLOBALID,
            COMPSTFCREF, COMPINVREF, TOTALSERVICES, CAMPAIGNCODE, OFFERREF, EINVOICETYP,
            EINVOICE, NOCALCULATE, PRODORDERTYP, QPRODFCTYP, PRDORDSLPLNRESERVE,
            CONTROLINFO, EDESPATCH, DOCTIME, EDESPSTATUS, PROFILEID, DELIVERYCODE,
            DESTSTATUS, CANCELEXP, UNDOEXP, CREATEWHERE, PUBLICBNACCREF, ACCEPTEINVPUBLIC,
            VATEXCEPTCODE, VATEXCEPTREASON, ATAXEXCEPTCODE, ATAXEXCEPTREASON, TAXFREECHX,
            MNTORDERFREF, PRINTEDDESPFCNO, OKCFICHE, NOTIFYCRDREF, CANCELLEDINVREF1,
            CANCELLEDINVREF2, CANCELLEDINVREF3, CANCELLEDINVREF4, FROMINTEGTYPE,
            FROMINTEGREF, EPRINTCNT, CLNOTREFLACNTRREF, CLNOTREFLAACCREF, FORENTRUST,
            PAYERCRKEY, PAYERCRPROVIDER, ORDFICHECMREF, ESENDTIME
          )
          VALUES (
            @GRPCODE, @TRCODE, @IOCODE, @FICHENO, @DATE_, @FTIME, @DOCODE, @INVNO, @SPECODE, @CYPHCODE,
            @INVOICEREF, @CLIENTREF, @RECVREF, @ACCOUNTREF, @CENTERREF, @PRODORDERREF, @PORDERFICHENO,
            @SOURCETYPE, @SOURCEINDEX, @SOURCEWSREF, @SOURCEPOLNREF, @SOURCECOSTGRP, @DESTTYPE,
            @DESTINDEX, @DESTWSREF, @DESTPOLNREF, @DESTCOSTGRP, @FACTORYNR, @BRANCH, @DEPARTMENT,
            @COMPBRANCH, @COMPDEPARTMENT, @COMPFACTORY, @PRODSTAT, @DEVIR, @CANCELLED, @BILLED,
            @ACCOUNTED, @UPDCURR, @INUSE, @INVKIND, @ADDDISCOUNTS, @TOTALDISCOUNTS, @TOTALDISCOUNTED,
            @ADDEXPENSES, @TOTALEXPENSES, @TOTALDEPOZITO, @TOTALPROMOTIONS, @TOTALVAT, @GROSSTOTAL,
            @NETTOTAL, @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6, @REPORTRATE,
            @REPORTNET, @EXTENREF, @PAYDEFREF, @PRINTCNT, @FICHECNT, @ACCFICHEREF,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREADEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC, @SALESMANREF, @CANCELLEDACC, @SHPTYPCOD, @SHPAGNCOD,
            @TRACKNR, @GENEXCTYP, @LINEEXCTYP, @TRADINGGRP, @TEXTINC, @SITEID, @RECSTATUS,
            @ORGLOGICREF, @WFSTATUS, @SHIPINFOREF, @DISTORDERREF, @SENDCNT, @DLVCLIENT,
            @DOCTRACKINGNR, @ADDTAXCALC, @TOTALADDTAX, @UGIRTRACKINGNO, @QPRODFCREF,
            @VAACCREF, @VACENTERREF, @ORGLOGOID, @FROMEXIM, @FRGTYPCOD, @TRCURR, @TRRATE,
            @TRNET, @EXIMWHFCREF, @EXIMFCTYPE, @MAINSTFCREF, @FROMORDWITHPAY, @PROJECTREF,
            @WFLOWCRDREF, @STATUS, @UPDTRCURR, @TOTALEXADDTAX, @AFFECTCOLLATRL, @DEDUCTIONPART1,
            @DEDUCTIONPART2, @GRPFIRMTRANS, @AFFECTRISK, @DISPSTATUS, @APPROVE, @CANTCREDEDUCT,
            @SHIPDATE, @SHIPTIME, @ENTRUSTDEVIR, @RELTRANSFCREF, @FROMTRANSFER, @GUID, @GLOBALID,
            @COMPSTFCREF, @COMPINVREF, @TOTALSERVICES, @CAMPAIGNCODE, @OFFERREF, @EINVOICETYP,
            @EINVOICE, @NOCALCULATE, @PRODORDERTYP, @QPRODFCTYP, @PRDORDSLPLNRESERVE,
            @CONTROLINFO, @EDESPATCH, @DOCTIME, @EDESPSTATUS, @PROFILEID, @DELIVERYCODE,
            @DESTSTATUS, @CANCELEXP, @UNDOEXP, @CREATEWHERE, @PUBLICBNACCREF, @ACCEPTEINVPUBLIC,
            @VATEXCEPTCODE, @VATEXCEPTREASON, @ATAXEXCEPTCODE, @ATAXEXCEPTREASON, @TAXFREECHX,
            @MNTORDERFREF, @PRINTEDDESPFCNO, @OKCFICHE, @NOTIFYCRDREF, @CANCELLEDINVREF1,
            @CANCELLEDINVREF2, @CANCELLEDINVREF3, @CANCELLEDINVREF4, @FROMINTEGTYPE,
            @FROMINTEGREF, @EPRINTCNT, @CLNOTREFLACNTRREF, @CLNOTREFLAACCREF, @FORENTRUST,
            @PAYERCRKEY, @PAYERCRPROVIDER, @ORDFICHECMREF, @ESENDTIME
          )
        `)

      // Get the inserted record's LOGICALREF
      const selectResult = await logoConnection.request()
        .input('FICHENO', sql.VarChar(17), stficheno)
        .query(`
          SELECT TOP 1 LOGICALREF, FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
          WHERE FICHENO = @FICHENO
          ORDER BY LOGICALREF DESC
        `)

      if (selectResult.recordset.length > 0 && selectResult.recordset[0]) {
        const insertedRecord = selectResult.recordset[0]
        consola.success(`Logo STFICHE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO,
        }
      }

      throw new Error('STFICHE tablosuna ekleme başarısız oldu - LOGICALREF alınamadı')
    }
    catch (error) {
      consola.error('Logo STFICHE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },

  /**
   * Insert into actual Logo STLINE table (LG_{FFF}_{DD}_STLINE)
   */
  insertLogoActualStlines: async ({
    invoiceRef,
    stficheRef,
    lines,
    veritabaniId,
    invoiceDate,
  }: {
    invoiceRef: number
    stficheRef: number
    lines: SatisFaturaLineItem[]
    veritabaniId: string
    invoiceDate: string
  }): Promise<boolean> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      consola.info(`STLINE ekleme başlatılıyor. invoiceRef: ${invoiceRef}, satır sayısı: ${lines.length}`)

      let lineNo = 0
      for (const line of lines) {
        lineNo++

        // Get stock reference from MASTER_CODE
        let stockRef = null
        if (line.MASTER_CODE) {
          stockRef = await LogoLookupService.getItemRefFromCode(line.MASTER_CODE, veritabaniId)
        }

        // Resolve PROJECTREF from project_code if provided
        let projectRef = null
        if (line.PROJECT_CODE) {
          projectRef = await LogoLookupService.getProjectRefFromCode(line.PROJECT_CODE, veritabaniId)
        }

        // Resolve SALESMANREF from salesman_code if provided
        let _salesmanRef = null
        if (line.SALEMANCODE) {
          _salesmanRef = await LogoLookupService.getSalesmanRefFromCode(line.SALEMANCODE, veritabaniId)
        }

        // Get unit reference from UNIT_CODE
        let uomRef = null
        if (line.UNIT_CODE) {
          uomRef = await LogoLookupService.getUnitRefFromCode(line.UNIT_CODE, veritabaniId)
        }

        // Calculate line totals
        const quantity = line.QUANTITY || 0
        const price = line.PRICE || 0
        const total = line.TOTAL || (quantity * price)
        const vatRate = line.VAT_RATE || 0
        const vatAmount = total * (vatRate / 100)
        const _lineNet = total + vatAmount

        // Get exchange rates (not used in current implementation but kept for future use)
        let _tcXrate = 1
        let _rcXrate = 1
        if (line.EDT_CURR && line.EDT_CURR !== 0) {
          const exchangeRate = await LogoLookupService.getExchangeRate(
            invoiceDate,
            line.EDT_CURR,
            veritabaniId,
          )
          _tcXrate = exchangeRate || 1
          _rcXrate = exchangeRate || 1
        }

        // Insert the line with comprehensive field mapping based on JSON structure
        const request = logoConnection.request()
        await request
          .input('STOCKREF', sql.Int, stockRef || 0)
          .input('LINETYPE', sql.SmallInt, 0) // 0 based on example
          .input('PREVLINEREF', sql.Int, 0)
          .input('PREVLINENO', sql.SmallInt, 0)
          .input('DETLINE', sql.SmallInt, 0)
          .input('TRCODE', sql.SmallInt, 7) // 7 for sales invoice
          .input('DATE_', sql.DateTime, new Date(invoiceDate))
          .input('FTIME', sql.Int, 185999616) // Based on example
          .input('GLOBTRANS', sql.SmallInt, 0)
          .input('CALCTYPE', sql.SmallInt, 0)
          .input('PRODORDERREF', sql.Int, 0)
          .input('SOURCETYPE', sql.SmallInt, 0)
          .input('SOURCEINDEX', sql.SmallInt, line.SOURCEINDEX || 0)
          .input('SOURCECOSTGRP', sql.SmallInt, line.SOURCE_COST_GRP || 0)
          .input('SOURCEWSREF', sql.Int, 0)
          .input('SOURCEPOLNREF', sql.Int, 0)
          .input('DESTTYPE', sql.SmallInt, 0)
          .input('DESTINDEX', sql.SmallInt, 0)
          .input('DESTCOSTGRP', sql.SmallInt, 0)
          .input('DESTWSREF', sql.Int, 0)
          .input('DESTPOLNREF', sql.Int, 0)
          .input('FACTORYNR', sql.SmallInt, line.FACTORY || 0)
          .input('IOCODE', sql.SmallInt, 4) // 4 based on example
          .input('STFICHEREF', sql.Int, stficheRef)
          .input('STFICHELNNO', sql.SmallInt, lineNo)
          .input('INVOICEREF', sql.Int, invoiceRef)
          .input('INVOICELNNO', sql.SmallInt, lineNo)
          .input('CLIENTREF', sql.Int, 0) // Will be set from invoice
          .input('ORDTRANSREF', sql.Int, 0)
          .input('ORDFICHEREF', sql.Int, 0)
          .input('CENTERREF', sql.Int, 0)
          .input('ACCOUNTREF', sql.Int, 0)
          .input('VATACCREF', sql.Int, 0)
          .input('VATCENTERREF', sql.Int, 0)
          .input('PRACCREF', sql.Int, 0)
          .input('PRCENTERREF', sql.Int, 0)
          .input('PRVATACCREF', sql.Int, 0)
          .input('PRVATCENREF', sql.Int, 0)
          .input('PROMREF', sql.Int, 0)
          .input('PAYDEFREF', sql.Int, 0)
          .input('SPECODE', sql.VarChar(11), line.AUXIL_CODE || '')
          .input('DELVRYCODE', sql.VarChar(51), '')
          .input('AMOUNT', sql.Float, quantity)
          .input('PRICE', sql.Float, price)
          .input('TOTAL', sql.Float, total)
          .input('PRCURR', sql.SmallInt, 0)
          .input('PRPRICE', sql.Float, 0)
          .input('TRCURR', sql.SmallInt, line.EDT_CURR || 0)
          .input('TRRATE', sql.Float, 1)
          .input('REPORTRATE', sql.Float, 1)
          .input('DISTCOST', sql.Float, 0)
          .input('DISTDISC', sql.Float, 0)
          .input('DISTEXP', sql.Float, 0)
          .input('DISTPROM', sql.Float, 0)
          .input('DISCPER', sql.Float, line.DISCOUNT_RATE || 0)
          .input('LINEEXP', sql.VarChar(51), line.DESCRIPTION || '')
          .input('UOMREF', sql.SmallInt, uomRef || 0)
          .input('USREF', sql.SmallInt, 0)
          .input('UINFO1', sql.Float, 1)
          .input('UINFO2', sql.Float, 1)
          .input('UINFO3', sql.Float, 0)
          .input('UINFO4', sql.Float, 0)
          .input('UINFO5', sql.Float, 0)
          .input('UINFO6', sql.Float, 0)
          .input('UINFO7', sql.Float, 0)
          .input('UINFO8', sql.Float, 0)
          .input('PLNAMOUNT', sql.Float, 0)
          .input('VATINC', sql.SmallInt, line.VAT_INCLUDED || 0)
          .input('VAT', sql.Float, vatRate)
          .input('VATAMNT', sql.Float, vatAmount)
          .input('VATMATRAH', sql.Float, total)
          .input('BILLEDITEM', sql.SmallInt, 0)
          .input('BILLED', sql.SmallInt, line.BILLED || 1)
          .input('CPSTFLAG', sql.SmallInt, 0)
          .input('RETCOSTTYPE', sql.SmallInt, 0)
          .input('SOURCELINK', sql.SmallInt, 0)
          .input('RETCOST', sql.Float, 0)
          .input('RETCOSTCURR', sql.Float, 0)
          .input('OUTCOST', sql.Float, 0)
          .input('OUTCOSTCURR', sql.Float, 0)
          .input('RETAMOUNT', sql.Float, 0)
          .input('FAREGREF', sql.Int, 0)
          .input('FAATTRIB', sql.SmallInt, 0)
          .input('CANCELLED', sql.SmallInt, 0)
          .input('LINENET', sql.Float, total)
          .input('DISTADDEXP', sql.Float, 0)
          .input('FADACCREF', sql.Int, 0)
          .input('FADCENTERREF', sql.Int, 0)
          .input('FARACCREF', sql.Int, 0)
          .input('FARCENTERREF', sql.Int, 0)
          .input('DIFFPRICE', sql.Float, 0)
          .input('DIFFPRCOST', sql.Float, 0)
          .input('DECPRDIFF', sql.SmallInt, 0)
          .input('LPRODSTAT', sql.SmallInt, 0)
          .input('PRDEXPTOTAL', sql.Float, 0)
          .input('DIFFREPPRICE', sql.Float, 0)
          .input('DIFFPRCRCOST', sql.Float, 0)
          .input('SALESMANREF', sql.Int, projectRef || 0) // Using projectRef as placeholder
          .input('FAPLACCREF', sql.Int, 0)
          .input('FAPLCENTERREF', sql.Int, 0)
          .input('OUTPUTIDCODE', sql.VarChar(51), '')
          .input('DREF', sql.Int, 0)
          .input('COSTRATE', sql.Float, 0)
          .input('XPRICEUPD', sql.SmallInt, 0)
          .input('XPRICE', sql.Float, 0)
          .input('XREPRATE', sql.Float, 0)
          .input('DISTCOEF', sql.Float, 0)
          .input('TRANSQCOK', sql.SmallInt, 0)
          .input('SITEID', sql.SmallInt, 0)
          .input('RECSTATUS', sql.SmallInt, 0)
          .input('ORGLOGICREF', sql.Int, 0)
          .input('WFSTATUS', sql.SmallInt, 0)
          .input('POLINEREF', sql.Int, 0)
          .input('PLNSTTRANSREF', sql.Int, 0)
          .input('NETDISCFLAG', sql.SmallInt, 0)
          .input('NETDISCPERC', sql.Float, 0)
          .input('NETDISCAMNT', sql.Float, 0)
          .input('VATCALCDIFF', sql.Float, 0)
          .input('CONDITIONREF', sql.Int, 0)
          .input('DISTORDERREF', sql.Int, 0)
          .input('DISTORDLINEREF', sql.Int, 0)
          .input('CAMPAIGNREFS1', sql.Int, 0)
          .input('CAMPAIGNREFS2', sql.Int, 0)
          .input('CAMPAIGNREFS3', sql.Int, 0)
          .input('CAMPAIGNREFS4', sql.Int, 0)
          .input('CAMPAIGNREFS5', sql.Int, 0)
          .input('POINTCAMPREF', sql.Int, 0)
          .input('CAMPPOINT', sql.Float, 0)
          .input('PROMCLASITEMREF', sql.Int, 0)
          .input('CMPGLINEREF', sql.Int, 0)
          .input('PLNSTTRANSPERNR', sql.SmallInt, 0)
          .input('PORDCLSPLNAMNT', sql.Float, 0)
          .input('VENDCOMM', sql.Float, 0)
          .input('PREVIOUSOUTCOST', sql.Float, 0)
          .input('COSTOFSALEACCREF', sql.Int, 0)
          .input('PURCHACCREF', sql.Int, 0)
          .input('COSTOFSALECNTREF', sql.Int, 0)
          .input('PURCHCENTREF', sql.Int, 0)
          .input('PREVOUTCOSTCURR', sql.Float, 0)
          .input('ABVATAMOUNT', sql.Float, 0)
          .input('ABVATSTATUS', sql.SmallInt, 0)
          .input('PRRATE', sql.Float, 0)
          .input('ADDTAXRATE', sql.Float, 0)
          .input('ADDTAXCONVFACT', sql.Float, 0)
          .input('ADDTAXAMOUNT', sql.Float, 0)
          .input('ADDTAXPRCOST', sql.Float, 0)
          .input('ADDTAXRETCOST', sql.Float, 0)
          .input('ADDTAXRETCOSTCURR', sql.Float, 0)
          .input('GROSSUINFO1', sql.Float, 0)
          .input('GROSSUINFO2', sql.Float, 0)
          .input('ADDTAXPRCOSTCURR', sql.Float, 0)
          .input('ADDTAXACCREF', sql.Int, 0)
          .input('ADDTAXCENTERREF', sql.Int, 0)
          .input('ADDTAXAMNTISUPD', sql.SmallInt, 0)
          .input('INFIDX', sql.Float, 0)
          .input('ADDTAXCOSACCREF', sql.Int, 0)
          .input('ADDTAXCOSCNTREF', sql.Int, 0)
          .input('PREVIOUSATAXPRCOST', sql.Float, 0)
          .input('PREVATAXPRCOSTCURR', sql.Float, 0)
          .input('PRDORDTOTCOEF', sql.Float, 0)
          .input('DEMPEGGEDAMNT', sql.Float, 0)
          .input('STDUNITCOST', sql.Float, 0)
          .input('STDRPUNITCOST', sql.Float, 0)
          .input('COSTDIFFACCREF', sql.Int, 0)
          .input('COSTDIFFCENREF', sql.Int, 0)
          .input('TEXTINC', sql.SmallInt, 0)
          .input('ADDTAXDISCAMOUNT', sql.Float, 0)
          .input('ORGLOGOID', sql.VarChar(25), '')
          .input('EXIMFICHENO', sql.VarChar(17), '')
          .input('EXIMFCTYPE', sql.SmallInt, 0)
          .input('TRANSEXPLINE', sql.SmallInt, 0)
          .input('INSEXPLINE', sql.SmallInt, 0)
          .input('EXIMWHFCREF', sql.Int, 0)
          .input('EXIMWHLNREF', sql.Int, 0)
          .input('EXIMFILEREF', sql.Int, 0)
          .input('EXIMPROCNR', sql.Int, 0)
          .input('EISRVDSTTYP', sql.SmallInt, 0)
          .input('MAINSTLNREF', sql.Int, 0)
          .input('MADEOFSHRED', sql.SmallInt, 0)
          .input('FROMORDWITHPAY', sql.SmallInt, 0)
          .input('PROJECTREF', sql.Int, projectRef || 0)
          .input('STATUS', sql.SmallInt, 0)
          .input('DORESERVE', sql.SmallInt, 0)
          .input('POINTCAMPREFS1', sql.Int, 0)
          .input('POINTCAMPREFS2', sql.Int, 0)
          .input('POINTCAMPREFS3', sql.Int, 0)
          .input('POINTCAMPREFS4', sql.Int, 0)
          .input('CAMPPOINTS1', sql.Float, 0)
          .input('CAMPPOINTS2', sql.Float, 0)
          .input('CAMPPOINTS3', sql.Float, 0)
          .input('CAMPPOINTS4', sql.Float, 0)
          .input('CMPGLINEREFS1', sql.Int, 0)
          .input('CMPGLINEREFS2', sql.Int, 0)
          .input('CMPGLINEREFS3', sql.Int, 0)
          .input('CMPGLINEREFS4', sql.Int, 0)
          .input('PRCLISTREF', sql.Int, 0)
          .input('PORDSYMOUTLN', sql.SmallInt, 0)
          .input('MONTH_', sql.SmallInt, new Date(invoiceDate).getMonth() + 1)
          .input('YEAR_', sql.SmallInt, new Date(invoiceDate).getFullYear())
          .input('EXADDTAXRATE', sql.Float, 0)
          .input('EXADDTAXCONVF', sql.Float, 0)
          .input('EXADDTAXAREF', sql.Int, 0)
          .input('EXADDTAXCREF', sql.Int, 0)
          .input('OTHRADDTAXAREF', sql.Int, 0)
          .input('OTHRADDTAXCREF', sql.Int, 0)
          .input('EXADDTAXAMNT', sql.Float, 0)
          .input('AFFECTCOLLATRL', sql.SmallInt, 0)
          .input('ALTPROMFLAG', sql.SmallInt, 0)
          .input('EIDISTFLNNR', sql.SmallInt, 0)
          .input('EXIMTYPE', sql.SmallInt, 0)
          .input('VARIANTREF', sql.Int, 0)
          .input('CANDEDUCT', sql.SmallInt, 0)
          .input('OUTREMAMNT', sql.Float, 0)
          .input('OUTREMCOST', sql.Float, 0)
          .input('OUTREMCOSTCURR', sql.Float, 0)
          .input('REFLVATACCREF', sql.Int, 0)
          .input('REFLVATOTHACCREF', sql.Int, 0)
          .input('PARENTLNREF', sql.Int, 0)
          .input('AFFECTRISK', sql.SmallInt, 0)
          .input('INEFFECTIVECOST', sql.SmallInt, 0)
          .input('ADDTAXVATMATRAH', sql.Float, 0)
          .input('REFLACCREF', sql.Int, 0)
          .input('REFLOTHACCREF', sql.Int, 0)
          .input('CAMPPAYDEFREF', sql.Int, 0)
          .input('RELTRANSLNREF', sql.Int, 0)
          .input('FROMTRANSFER', sql.SmallInt, 0)
          .input('COSTDISTPRICE', sql.Float, 0)
          .input('COSTDISTREPPRICE', sql.Float, 0)
          .input('DIFFPRICEUFRS', sql.Float, 0)
          .input('DIFFREPPRICEUFRS', sql.Float, 0)
          .input('OUTCOSTUFRS', sql.Float, 0)
          .input('OUTCOSTCURRUFRS', sql.Float, 0)
          .input('DIFFPRCOSTUFRS', sql.Float, 0)
          .input('DIFFPRCRCOSTUFRS', sql.Float, 0)
          .input('RETCOSTUFRS', sql.Float, 0)
          .input('RETCOSTCURRUFRS', sql.Float, 0)
          .input('OUTREMCOSTUFRS', sql.Float, 0)
          .input('OUTREMCOSTCURRUFRS', sql.Float, 0)
          .input('INFIDXUFRS', sql.Float, 0)
          .input('ADJPRICEUFRS', sql.Float, 0)
          .input('ADJREPPRICEUFRS', sql.Float, 0)
          .input('ADJPRCOSTUFRS', sql.Float, 0)
          .input('ADJPRCRCOSTUFRS', sql.Float, 0)
          .input('COSTDISTPRICEUFRS', sql.Float, 0)
          .input('COSTDISTREPPRICEUFRS', sql.Float, 0)
          .input('PURCHACCREFUFRS', sql.Int, 0)
          .input('PURCHCENTREFUFRS', sql.Int, 0)
          .input('COSACCREFUFRS', sql.Int, 0)
          .input('COSCNTREFUFRS', sql.Int, 0)
          .input('PROUTCOSTUFRSDIFF', sql.Float, 0)
          .input('PROUTCOSTCRUFRSDIFF', sql.Float, 0)
          .input('UNDERDEDUCTLIMIT', sql.SmallInt, 0)
          .input('GLOBALID', sql.VarChar(51), '')
          .input('DEDUCTIONPART1', sql.SmallInt, 0)
          .input('DEDUCTIONPART2', sql.SmallInt, 0)
          .input('GUID', sql.VarChar(37), randomUUID())
          .input('SPECODE2', sql.VarChar(11), '')
          .input('OFFERREF', sql.Int, 0)
          .input('OFFTRANSREF', sql.Int, 0)
          .input('VATEXCEPTREASON', sql.VarChar(51), '')
          .input('PLNDEFSERILOTNO', sql.VarChar(51), '')
          .input('PLNUNRSRVAMOUNT', sql.Float, 0)
          .input('PORDCLSPLNUNRSRVAMNT', sql.Float, 0)
          .input('LPRODRSRVSTAT', sql.SmallInt, 0)
          .input('FALINKTYPE', sql.SmallInt, 0)
          .input('DEDUCTCODE', sql.VarChar(51), '')
          .input('UPDTHISLINE', sql.SmallInt, 0)
          .input('VATEXCEPTCODE', sql.VarChar(51), '')
          .input('PORDERFICHENO', sql.VarChar(17), '')
          .input('QPRODFCREF', sql.Int, 0)
          .input('RELTRANSFCREF', sql.Int, 0)
          .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
          .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
          .input('PRODORDERTYP', sql.SmallInt, 0)
          .input('SUBCONTORDERREF', sql.Int, 0)
          .input('QPRODFCTYP', sql.SmallInt, 0)
          .input('PRDORDSLPLNRESERVE', sql.SmallInt, 0)
          .input('DESTSTATUS', sql.SmallInt, 0)
          .input('REGTYPREF', sql.Int, 0)
          .input('FAPROFITACCREF', sql.Int, 0)
          .input('FAPROFITCENTREF', sql.Int, 0)
          .input('FALOSSACCREF', sql.Int, 0)
          .input('FALOSSCENTREF', sql.Int, 0)
          .input('CPACODE', sql.VarChar(51), '')
          .input('GTIPCODE', sql.VarChar(51), '')
          .input('PUBLICCOUNTRYREF', sql.Int, 0)
          .input('QPRODITEMTYPE', sql.SmallInt, 0)
          .input('FUTMONTHCNT', sql.SmallInt, 0)
          .input('FUTMONTHBEGDATE', sql.Int, 0)
          .input('QCTRANSFERREF', sql.Int, 0)
          .input('QCTRANSFERAMNT', sql.Float, 0)
          .input('KKEGACCREF', sql.Int, 0)
          .input('KKEGCENTREF', sql.Int, 0)
          .input('MNTORDERFREF', sql.Int, 0)
          .input('FAKKEGAMOUNT', sql.Float, 0)
          .input('MIDDLEMANEXPTYP', sql.SmallInt, 0)
          .input('EXPRACCREF', sql.Int, 0)
          .input('EXPRCNTRREF', sql.Int, 0)
          .input('KKEGVATACCREF', sql.Int, 0)
          .input('KKEGVATCENTREF', sql.Int, 0)
          .input('MARKINGTAGNO', sql.VarChar(51), '')
          .input('OWNER', sql.VarChar(51), '')
          .input('TCKTAXNR', sql.VarChar(51), '')
          .input('ADDTAXVATACCREF', sql.Int, 0)
          .input('ADDTAXVATCENREF', sql.Int, 0)
          .input('EXPDAYS', sql.SmallInt, 0)
          .input('CANCELLEDINVREF1', sql.Int, 0)
          .input('CANCELLEDINVREF2', sql.Int, 0)
          .input('CANCELLEDINVREF3', sql.Int, 0)
          .input('CANCELLEDINVREF4', sql.Int, 0)
          .input('FROMINTEGTYPE', sql.SmallInt, 0)
          .input('FROMINTEGREF', sql.Int, 0)
          .input('QCTRANSFERREF2', sql.Int, 0)
          .input('QCTRANSFERAMNT2', sql.Float, 0)
          .input('EISRVDSTADDTAXINC', sql.SmallInt, 0)
          .input('TAXFREEACCREF', sql.Int, 0)
          .input('TAXFREECNTRREF', sql.Int, 0)
          .input('ADDTAXEFFECTKDV', sql.SmallInt, 0)
          .input('ADDTAXINLINENET', sql.SmallInt, 0)
          .input('ITMDISC', sql.SmallInt, 0)
          .input('ADDTAXREF', sql.Int, 0)
          .input('COSCNTREFINFL', sql.Int, 0)
          .input('PROUTCOSTINFLDIFF', sql.Float, 0)
          .input('PROUTCOSTCRINFLDIFF', sql.Float, 0)
          .input('COSACCREFINFL', sql.Int, 0)
          .input('ORDFICHECMREF', sql.Int, 0)
          .input('PURCHACCREFINFL', sql.Int, 0)
          .input('PURCHCENTREFINFL', sql.Int, 0)
          .input('DIIBLINECODE', sql.VarChar(51), '')
          .input('RETSOURCELINK', sql.SmallInt, 0)
          .input('ORGPRICE', sql.Float, 0)
          .query(`
            INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STLINE (
              STOCKREF, LINETYPE, PREVLINEREF, PREVLINENO, DETLINE, TRCODE, DATE_, FTIME,
              GLOBTRANS, CALCTYPE, PRODORDERREF, SOURCETYPE, SOURCEINDEX, SOURCECOSTGRP,
              SOURCEWSREF, SOURCEPOLNREF, DESTTYPE, DESTINDEX, DESTCOSTGRP, DESTWSREF,
              DESTPOLNREF, FACTORYNR, IOCODE, STFICHEREF, STFICHELNNO, INVOICEREF,
              INVOICELNNO, CLIENTREF, ORDTRANSREF, ORDFICHEREF, CENTERREF, ACCOUNTREF,
              VATACCREF, VATCENTERREF, PRACCREF, PRCENTERREF, PRVATACCREF, PRVATCENREF,
              PROMREF, PAYDEFREF, SPECODE, DELVRYCODE, AMOUNT, PRICE, TOTAL, PRCURR,
              PRPRICE, TRCURR, TRRATE, REPORTRATE, DISTCOST, DISTDISC, DISTEXP, DISTPROM,
              DISCPER, LINEEXP, UOMREF, USREF, UINFO1, UINFO2, UINFO3, UINFO4, UINFO5,
              UINFO6, UINFO7, UINFO8, PLNAMOUNT, VATINC, VAT, VATAMNT, VATMATRAH,
              BILLEDITEM, BILLED, CPSTFLAG, RETCOSTTYPE, SOURCELINK, RETCOST, RETCOSTCURR,
              OUTCOST, OUTCOSTCURR, RETAMOUNT, FAREGREF, FAATTRIB, CANCELLED, LINENET,
              DISTADDEXP, FADACCREF, FADCENTERREF, FARACCREF, FARCENTERREF, DIFFPRICE,
              DIFFPRCOST, DECPRDIFF, LPRODSTAT, PRDEXPTOTAL, DIFFREPPRICE, DIFFPRCRCOST,
              SALESMANREF, FAPLACCREF, FAPLCENTERREF, OUTPUTIDCODE, DREF, COSTRATE,
              XPRICEUPD, XPRICE, XREPRATE, DISTCOEF, TRANSQCOK, SITEID, RECSTATUS,
              ORGLOGICREF, WFSTATUS, POLINEREF, PLNSTTRANSREF, NETDISCFLAG, NETDISCPERC,
              NETDISCAMNT, VATCALCDIFF, CONDITIONREF, DISTORDERREF, DISTORDLINEREF,
              CAMPAIGNREFS1, CAMPAIGNREFS2, CAMPAIGNREFS3, CAMPAIGNREFS4, CAMPAIGNREFS5,
              POINTCAMPREF, CAMPPOINT, PROMCLASITEMREF, CMPGLINEREF, PLNSTTRANSPERNR,
              PORDCLSPLNAMNT, VENDCOMM, PREVIOUSOUTCOST, COSTOFSALEACCREF, PURCHACCREF,
              COSTOFSALECNTREF, PURCHCENTREF, PREVOUTCOSTCURR, ABVATAMOUNT, ABVATSTATUS,
              PRRATE, ADDTAXRATE, ADDTAXCONVFACT, ADDTAXAMOUNT, ADDTAXPRCOST, ADDTAXRETCOST,
              ADDTAXRETCOSTCURR, GROSSUINFO1, GROSSUINFO2, ADDTAXPRCOSTCURR, ADDTAXACCREF,
              ADDTAXCENTERREF, ADDTAXAMNTISUPD, INFIDX, ADDTAXCOSACCREF, ADDTAXCOSCNTREF,
              PREVIOUSATAXPRCOST, PREVATAXPRCOSTCURR, PRDORDTOTCOEF, DEMPEGGEDAMNT,
              STDUNITCOST, STDRPUNITCOST, COSTDIFFACCREF, COSTDIFFCENREF, TEXTINC,
              ADDTAXDISCAMOUNT, ORGLOGOID, EXIMFICHENO, EXIMFCTYPE, TRANSEXPLINE,
              INSEXPLINE, EXIMWHFCREF, EXIMWHLNREF, EXIMFILEREF, EXIMPROCNR, EISRVDSTTYP,
              MAINSTLNREF, MADEOFSHRED, FROMORDWITHPAY, PROJECTREF, STATUS, DORESERVE,
              POINTCAMPREFS1, POINTCAMPREFS2, POINTCAMPREFS3, POINTCAMPREFS4, CAMPPOINTS1,
              CAMPPOINTS2, CAMPPOINTS3, CAMPPOINTS4, CMPGLINEREFS1, CMPGLINEREFS2,
              CMPGLINEREFS3, CMPGLINEREFS4, PRCLISTREF, PORDSYMOUTLN, MONTH_, YEAR_,
              EXADDTAXRATE, EXADDTAXCONVF, EXADDTAXAREF, EXADDTAXCREF, OTHRADDTAXAREF,
              OTHRADDTAXCREF, EXADDTAXAMNT, AFFECTCOLLATRL, ALTPROMFLAG, EIDISTFLNNR,
              EXIMTYPE, VARIANTREF, CANDEDUCT, OUTREMAMNT, OUTREMCOST, OUTREMCOSTCURR,
              REFLVATACCREF, REFLVATOTHACCREF, PARENTLNREF, AFFECTRISK, INEFFECTIVECOST,
              ADDTAXVATMATRAH, REFLACCREF, REFLOTHACCREF, CAMPPAYDEFREF, RELTRANSLNREF,
              FROMTRANSFER, COSTDISTPRICE, COSTDISTREPPRICE, DIFFPRICEUFRS, DIFFREPPRICEUFRS,
              OUTCOSTUFRS, OUTCOSTCURRUFRS, DIFFPRCOSTUFRS, DIFFPRCRCOSTUFRS, RETCOSTUFRS,
              RETCOSTCURRUFRS, OUTREMCOSTUFRS, OUTREMCOSTCURRUFRS, INFIDXUFRS, ADJPRICEUFRS,
              ADJREPPRICEUFRS, ADJPRCOSTUFRS, ADJPRCRCOSTUFRS, COSTDISTPRICEUFRS,
              COSTDISTREPPRICEUFRS, PURCHACCREFUFRS, PURCHCENTREFUFRS, COSACCREFUFRS,
              COSCNTREFUFRS, PROUTCOSTUFRSDIFF, PROUTCOSTCRUFRSDIFF, UNDERDEDUCTLIMIT,
              GLOBALID, DEDUCTIONPART1, DEDUCTIONPART2, GUID, SPECODE2, OFFERREF,
              OFFTRANSREF, VATEXCEPTREASON, PLNDEFSERILOTNO, PLNUNRSRVAMOUNT,
              PORDCLSPLNUNRSRVAMNT, LPRODRSRVSTAT, FALINKTYPE, DEDUCTCODE, UPDTHISLINE,
              VATEXCEPTCODE, PORDERFICHENO, QPRODFCREF, RELTRANSFCREF, ATAXEXCEPTREASON,
              ATAXEXCEPTCODE, PRODORDERTYP, SUBCONTORDERREF, QPRODFCTYP, PRDORDSLPLNRESERVE,
              DESTSTATUS, REGTYPREF, FAPROFITACCREF, FAPROFITCENTREF, FALOSSACCREF,
              FALOSSCENTREF, CPACODE, GTIPCODE, PUBLICCOUNTRYREF, QPRODITEMTYPE,
              FUTMONTHCNT, FUTMONTHBEGDATE, QCTRANSFERREF, QCTRANSFERAMNT, KKEGACCREF,
              KKEGCENTREF, MNTORDERFREF, FAKKEGAMOUNT, MIDDLEMANEXPTYP, EXPRACCREF,
              EXPRCNTRREF, KKEGVATACCREF, KKEGVATCENTREF, MARKINGTAGNO, OWNER, TCKTAXNR,
              ADDTAXVATACCREF, ADDTAXVATCENREF, EXPDAYS, CANCELLEDINVREF1, CANCELLEDINVREF2,
              CANCELLEDINVREF3, CANCELLEDINVREF4, FROMINTEGTYPE, FROMINTEGREF,
              QCTRANSFERREF2, QCTRANSFERAMNT2, EISRVDSTADDTAXINC, TAXFREEACCREF,
              TAXFREECNTRREF, ADDTAXEFFECTKDV, ADDTAXINLINENET, ITMDISC, ADDTAXREF,
              COSCNTREFINFL, PROUTCOSTINFLDIFF, PROUTCOSTCRINFLDIFF, COSACCREFINFL,
              ORDFICHECMREF, PURCHACCREFINFL, PURCHCENTREFINFL, DIIBLINECODE,
              RETSOURCELINK, ORGPRICE
            )
            VALUES (
              @STOCKREF, @LINETYPE, @PREVLINEREF, @PREVLINENO, @DETLINE, @TRCODE, @DATE_, @FTIME,
              @GLOBTRANS, @CALCTYPE, @PRODORDERREF, @SOURCETYPE, @SOURCEINDEX, @SOURCECOSTGRP,
              @SOURCEWSREF, @SOURCEPOLNREF, @DESTTYPE, @DESTINDEX, @DESTCOSTGRP, @DESTWSREF,
              @DESTPOLNREF, @FACTORYNR, @IOCODE, @STFICHEREF, @STFICHELNNO, @INVOICEREF,
              @INVOICELNNO, @CLIENTREF, @ORDTRANSREF, @ORDFICHEREF, @CENTERREF, @ACCOUNTREF,
              @VATACCREF, @VATCENTERREF, @PRACCREF, @PRCENTERREF, @PRVATACCREF, @PRVATCENREF,
              @PROMREF, @PAYDEFREF, @SPECODE, @DELVRYCODE, @AMOUNT, @PRICE, @TOTAL, @PRCURR,
              @PRPRICE, @TRCURR, @TRRATE, @REPORTRATE, @DISTCOST, @DISTDISC, @DISTEXP, @DISTPROM,
              @DISCPER, @LINEEXP, @UOMREF, @USREF, @UINFO1, @UINFO2, @UINFO3, @UINFO4, @UINFO5,
              @UINFO6, @UINFO7, @UINFO8, @PLNAMOUNT, @VATINC, @VAT, @VATAMNT, @VATMATRAH,
              @BILLEDITEM, @BILLED, @CPSTFLAG, @RETCOSTTYPE, @SOURCELINK, @RETCOST, @RETCOSTCURR,
              @OUTCOST, @OUTCOSTCURR, @RETAMOUNT, @FAREGREF, @FAATTRIB, @CANCELLED, @LINENET,
              @DISTADDEXP, @FADACCREF, @FADCENTERREF, @FARACCREF, @FARCENTERREF, @DIFFPRICE,
              @DIFFPRCOST, @DECPRDIFF, @LPRODSTAT, @PRDEXPTOTAL, @DIFFREPPRICE, @DIFFPRCRCOST,
              @SALESMANREF, @FAPLACCREF, @FAPLCENTERREF, @OUTPUTIDCODE, @DREF, @COSTRATE,
              @XPRICEUPD, @XPRICE, @XREPRATE, @DISTCOEF, @TRANSQCOK, @SITEID, @RECSTATUS,
              @ORGLOGICREF, @WFSTATUS, @POLINEREF, @PLNSTTRANSREF, @NETDISCFLAG, @NETDISCPERC,
              @NETDISCAMNT, @VATCALCDIFF, @CONDITIONREF, @DISTORDERREF, @DISTORDLINEREF,
              @CAMPAIGNREFS1, @CAMPAIGNREFS2, @CAMPAIGNREFS3, @CAMPAIGNREFS4, @CAMPAIGNREFS5,
              @POINTCAMPREF, @CAMPPOINT, @PROMCLASITEMREF, @CMPGLINEREF, @PLNSTTRANSPERNR,
              @PORDCLSPLNAMNT, @VENDCOMM, @PREVIOUSOUTCOST, @COSTOFSALEACCREF, @PURCHACCREF,
              @COSTOFSALECNTREF, @PURCHCENTREF, @PREVOUTCOSTCURR, @ABVATAMOUNT, @ABVATSTATUS,
              @PRRATE, @ADDTAXRATE, @ADDTAXCONVFACT, @ADDTAXAMOUNT, @ADDTAXPRCOST, @ADDTAXRETCOST,
              @ADDTAXRETCOSTCURR, @GROSSUINFO1, @GROSSUINFO2, @ADDTAXPRCOSTCURR, @ADDTAXACCREF,
              @ADDTAXCENTERREF, @ADDTAXAMNTISUPD, @INFIDX, @ADDTAXCOSACCREF, @ADDTAXCOSCNTREF,
              @PREVIOUSATAXPRCOST, @PREVATAXPRCOSTCURR, @PRDORDTOTCOEF, @DEMPEGGEDAMNT,
              @STDUNITCOST, @STDRPUNITCOST, @COSTDIFFACCREF, @COSTDIFFCENREF, @TEXTINC,
              @ADDTAXDISCAMOUNT, @ORGLOGOID, @EXIMFICHENO, @EXIMFCTYPE, @TRANSEXPLINE,
              @INSEXPLINE, @EXIMWHFCREF, @EXIMWHLNREF, @EXIMFILEREF, @EXIMPROCNR, @EISRVDSTTYP,
              @MAINSTLNREF, @MADEOFSHRED, @FROMORDWITHPAY, @PROJECTREF, @STATUS, @DORESERVE,
              @POINTCAMPREFS1, @POINTCAMPREFS2, @POINTCAMPREFS3, @POINTCAMPREFS4, @CAMPPOINTS1,
              @CAMPPOINTS2, @CAMPPOINTS3, @CAMPPOINTS4, @CMPGLINEREFS1, @CMPGLINEREFS2,
              @CMPGLINEREFS3, @CMPGLINEREFS4, @PRCLISTREF, @PORDSYMOUTLN, @MONTH_, @YEAR_,
              @EXADDTAXRATE, @EXADDTAXCONVF, @EXADDTAXAREF, @EXADDTAXCREF, @OTHRADDTAXAREF,
              @OTHRADDTAXCREF, @EXADDTAXAMNT, @AFFECTCOLLATRL, @ALTPROMFLAG, @EIDISTFLNNR,
              @EXIMTYPE, @VARIANTREF, @CANDEDUCT, @OUTREMAMNT, @OUTREMCOST, @OUTREMCOSTCURR,
              @REFLVATACCREF, @REFLVATOTHACCREF, @PARENTLNREF, @AFFECTRISK, @INEFFECTIVECOST,
              @ADDTAXVATMATRAH, @REFLACCREF, @REFLOTHACCREF, @CAMPPAYDEFREF, @RELTRANSLNREF,
              @FROMTRANSFER, @COSTDISTPRICE, @COSTDISTREPPRICE, @DIFFPRICEUFRS, @DIFFREPPRICEUFRS,
              @OUTCOSTUFRS, @OUTCOSTCURRUFRS, @DIFFPRCOSTUFRS, @DIFFPRCRCOSTUFRS, @RETCOSTUFRS,
              @RETCOSTCURRUFRS, @OUTREMCOSTUFRS, @OUTREMCOSTCURRUFRS, @INFIDXUFRS, @ADJPRICEUFRS,
              @ADJREPPRICEUFRS, @ADJPRCOSTUFRS, @ADJPRCRCOSTUFRS, @COSTDISTPRICEUFRS,
              @COSTDISTREPPRICEUFRS, @PURCHACCREFUFRS, @PURCHCENTREFUFRS, @COSACCREFUFRS,
              @COSCNTREFUFRS, @PROUTCOSTUFRSDIFF, @PROUTCOSTCRUFRSDIFF, @UNDERDEDUCTLIMIT,
              @GLOBALID, @DEDUCTIONPART1, @DEDUCTIONPART2, @GUID, @SPECODE2, @OFFERREF,
              @OFFTRANSREF, @VATEXCEPTREASON, @PLNDEFSERILOTNO, @PLNUNRSRVAMOUNT,
              @PORDCLSPLNUNRSRVAMNT, @LPRODRSRVSTAT, @FALINKTYPE, @DEDUCTCODE, @UPDTHISLINE,
              @VATEXCEPTCODE, @PORDERFICHENO, @QPRODFCREF, @RELTRANSFCREF, @ATAXEXCEPTREASON,
              @ATAXEXCEPTCODE, @PRODORDERTYP, @SUBCONTORDERREF, @QPRODFCTYP, @PRDORDSLPLNRESERVE,
              @DESTSTATUS, @REGTYPREF, @FAPROFITACCREF, @FAPROFITCENTREF, @FALOSSACCREF,
              @FALOSSCENTREF, @CPACODE, @GTIPCODE, @PUBLICCOUNTRYREF, @QPRODITEMTYPE,
              @FUTMONTHCNT, @FUTMONTHBEGDATE, @QCTRANSFERREF, @QCTRANSFERAMNT, @KKEGACCREF,
              @KKEGCENTREF, @MNTORDERFREF, @FAKKEGAMOUNT, @MIDDLEMANEXPTYP, @EXPRACCREF,
              @EXPRCNTRREF, @KKEGVATACCREF, @KKEGVATCENTREF, @MARKINGTAGNO, @OWNER, @TCKTAXNR,
              @ADDTAXVATACCREF, @ADDTAXVATCENREF, @EXPDAYS, @CANCELLEDINVREF1, @CANCELLEDINVREF2,
              @CANCELLEDINVREF3, @CANCELLEDINVREF4, @FROMINTEGTYPE, @FROMINTEGREF,
              @QCTRANSFERREF2, @QCTRANSFERAMNT2, @EISRVDSTADDTAXINC, @TAXFREEACCREF,
              @TAXFREECNTRREF, @ADDTAXEFFECTKDV, @ADDTAXINLINENET, @ITMDISC, @ADDTAXREF,
              @COSCNTREFINFL, @PROUTCOSTINFLDIFF, @PROUTCOSTCRINFLDIFF, @COSACCREFINFL,
              @ORDFICHECMREF, @PURCHACCREFINFL, @PURCHCENTREFINFL, @DIIBLINECODE,
              @RETSOURCELINK, @ORGPRICE
            )
          `)

        consola.info(`STLINE satırı eklendi. Satır: ${lineNo}, STOCKREF: ${stockRef}`)
      }

      consola.success(`Tüm STLINE satırları başarıyla eklendi. Toplam: ${lines.length}`)
      return true
    }
    catch (error) {
      consola.error('Logo STLINE tablosuna ekleme sırasında hata oluştu:', error)
      return false
    }
  },
}

export default LogoErpIntegration
