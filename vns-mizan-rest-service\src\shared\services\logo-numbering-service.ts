import type { IResult } from 'mssql'
import consola from 'consola'
import sql from 'mssql'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

interface FicheNoResult {
  FICHENO: string
}

interface FormatFicheNoResult {
  FICHENO: string
  PREFIX: string
  NUMBER_PART: string
}

/**
 * Service for document numbering operations in Logo ERP
 */
const LogoNumberingService = {
  /**
   * Generates a new FICHENO for the specified table and transaction code.
   */
  generateNewFicheNo: async ({
    trcode,
    tableName,
    veritabaniId,
  }: {
    trcode: number
    tableName: string
    veritabaniId: string
  }): Promise<string> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Get the last used number for this transaction type
      const query = `
        SELECT TOP 1 FICHENO
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_${tableName}
        WHERE TRCODE = @trcode
        ORDER BY LOGICALREF DESC
      `
      const result: IResult<FicheNoResult[]> = await logoConnection.request()
        .input('trcode', sql.SmallInt, trcode)
        .query(query)

      let newFicheNo = '00000001'

      if (result.recordset.length > 0 && result.recordset[0] && result.recordset[0].FICHENO) {
        const lastFicheNo = result.recordset[0].FICHENO
        // Extract the numeric part and increment
        const numericPart = lastFicheNo.replace(/\D/g, '')
        if (numericPart) {
          const nextNumber = Number.parseInt(numericPart, 10) + 1
          newFicheNo = nextNumber.toString().padStart(8, '0')
        }
      }

      return newFicheNo
    }
    catch (error) {
      consola.error(`Yeni fiş numarası oluşturulurken hata oluştu, tablo: ${tableName}, işlem kodu: ${trcode}:`, error)
      // Return a default value in case of error
      return `${new Date().getTime().toString().slice(-8)}`
    }
  },

  /**
   * Generates a new FICHENO based on the provided format.
   * Format uses "_" as a placeholder for numeric characters.
   * Also supports date placeholders:
   * - [gg] or [dd] for day (01-31)
   * - [aa] or [mm] for month (01-12)
   * - [yyyy] for 4-digit year (e.g., 2025)
   * - [yy] for 2-digit year (e.g., 25)
   *
   * Examples:
   * - "ABC____" will generate "ABC0001", "ABC0002", etc.
   * - "INV[yyyy]____" might generate "INV20250001"
   * - "DSP[mm][dd]___" might generate "DSP05150001"
   */
  generateFormattedFicheNo: async ({
    trcode,
    tableName,
    format,
    veritabaniId,
  }: {
    trcode: number
    tableName: string
    format: string
    veritabaniId: string
  }): Promise<string> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Replace date placeholders with actual date values
      const now = new Date()
      const processedFormat = format
        .replace(/\[yyyy\]/g, now.getFullYear().toString())
        .replace(/\[yy\]/g, now.getFullYear().toString().slice(-2))
        .replace(/\[mm\]|\[aa\]/g, (now.getMonth() + 1).toString().padStart(2, '0'))
        .replace(/\[dd\]|\[gg\]/g, now.getDate().toString().padStart(2, '0'))

      // Count the number of underscores to determine the numeric part length
      const underscoreCount = (processedFormat.match(/_/g) || []).length
      if (underscoreCount === 0) {
        throw new Error('Format must contain at least one underscore (_) for the numeric part')
      }

      // Create the prefix by removing underscores
      const prefix = processedFormat.replace(/_/g, '')

      // Get the last used number for this format
      const query = `
        SELECT TOP 1 FICHENO
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_${tableName}
        WHERE TRCODE = @trcode
        AND FICHENO LIKE @prefix + '%'
        ORDER BY LOGICALREF DESC
      `
      const result: IResult<FormatFicheNoResult[]> = await logoConnection.request()
        .input('trcode', sql.SmallInt, trcode)
        .input('prefix', sql.VarChar, prefix)
        .query(query)

      let nextNumber = 1

      if (result.recordset.length > 0 && result.recordset[0] && result.recordset[0].FICHENO) {
        const lastFicheNo = result.recordset[0].FICHENO
        // Extract the numeric part from the end
        const numericPart = lastFicheNo.slice(prefix.length)
        if (numericPart && /^\d+$/.test(numericPart)) {
          nextNumber = Number.parseInt(numericPart, 10) + 1
        }
      }

      // Generate the new FICHENO
      const numericPart = nextNumber.toString().padStart(underscoreCount, '0')
      return prefix + numericPart
    }
    catch (error) {
      consola.error(`Formatlı fiş numarası oluşturulurken hata oluştu, format: ${format}, tablo: ${tableName}:`, error)
      // Return a fallback value
      const timestamp = new Date().getTime().toString().slice(-6)
      return `ERR${timestamp}`
    }
  },

  /**
   * Validates if a FICHENO format is valid
   */
  validateFicheNoFormat: (format: string): { isValid: boolean, error?: string } => {
    if (!format) {
      return { isValid: false, error: 'Format boş olamaz' }
    }

    // Check if format contains at least one underscore
    if (!format.includes('_')) {
      return { isValid: false, error: 'Format en az bir alt çizgi (_) içermelidir' }
    }

    // Check for valid date placeholders
    const validDatePlaceholders = /\[(?:yyyy|yy|mm|aa|dd|gg)\]/g
    const invalidPlaceholders = format.match(/\[[^\]]*\]/g)?.filter(placeholder =>
      !validDatePlaceholders.test(placeholder),
    )

    if (invalidPlaceholders && invalidPlaceholders.length > 0) {
      return {
        isValid: false,
        error: `Geçersiz tarih yer tutucuları: ${invalidPlaceholders.join(', ')}. Geçerli olanlar: [yyyy], [yy], [mm], [aa], [dd], [gg]`,
      }
    }

    return { isValid: true }
  },
}

export default LogoNumberingService
