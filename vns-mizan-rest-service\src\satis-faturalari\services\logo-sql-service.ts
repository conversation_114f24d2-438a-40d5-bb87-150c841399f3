import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import DatabaseService from './database-service.ts'
import LogoSqlDirectProcessor from './logo-sql-direct-processor.ts'
import LogoSqlLookup from './logo-sql-lookup.ts'
import LogoSqlValidation from './logo-sql-validation.ts'
import SatisFaturaValidator from './satis-fatura-validator.ts'

/**
 * Simplified Logo SQL Service that coordinates other services
 * This replaces the massive original logo-sql-service.ts file
 */
const LogoSqlService = {

  // Re-export lookup functions
  getExchangeRate: LogoSqlLookup.getExchangeRate,
  getExchangeRateByType: LogoSqlLookup.getExchangeRateByType,
  getUnitCodeForItem: LogoSqlLookup.getUnitCodeForItem,
  getUnitCodeForSrvcard: LogoSqlLookup.getUnitCodeForSrvcard,
  getItemCode: LogoSqlLookup.getItemCode,
  getCustomerVknOrTckn: LogoSqlLookup.getCustomerVknOrTckn,
  getInvoiceFichenoByLogicalref: LogoSqlLookup.getInvoiceFichenoByLogicalref,
  getSourceCostGrp: LogoSqlLookup.getSourceCostGrp,

  // Re-export validation functions
  checkServiceCodeExists: LogoSqlValidation.checkServiceCodeExists,
  checkCustomerExists: LogoSqlValidation.checkCustomerExists,
  checkMasrafMerkeziExists: LogoSqlValidation.checkMasrafMerkeziExists,
  checkProjeKoduExists: LogoSqlValidation.checkProjeKoduExists,
  checkWarehouseExists: LogoSqlValidation.checkWarehouseExists,
  checkDivisionExists: LogoSqlValidation.checkDivisionExists,
  checkDepartmentExists: LogoSqlValidation.checkDepartmentExists,
  checkFactoryExists: LogoSqlValidation.checkFactoryExists,
  checkInvoiceNumberExists: LogoSqlValidation.checkInvoiceNumberExists,
  checkDispatchNumberExists: LogoSqlValidation.checkDispatchNumberExists,
  getUseRestFlag: LogoSqlValidation.getUseRestFlag,

  // Re-export validation service
  validateSatisFaturaRequest: SatisFaturaValidator.validateSatisFaturaRequest,

  // Re-export direct processor
  processDirectSql: LogoSqlDirectProcessor.processDirectSql,

  // Re-export database service functions
  insertSatisFatura: DatabaseService.insertSatisFatura,
  insertSatisFaturaSatirlari: DatabaseService.insertSatisFaturaSatirlari,
  insertLogoSatisFatura: DatabaseService.insertLogoSatisFatura,
  insertLogoSatisFaturaIrsaliyesi: DatabaseService.insertLogoSatisFaturaIrsaliyesi,
  insertLogoSatisFaturaSatirlari: DatabaseService.insertLogoSatisFaturaSatirlari,

  /**
   * Get user reference from L_CAPIUSER table
   */
  getUserRefFromCapiuser: async ({
    kullaniciAdi,
    veritabaniId,
  }: {
    kullaniciAdi: string
    veritabaniId: string
  }): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const { recordset } = await logoConnection
        .request()
        .input('kullaniciAdi', kullaniciAdi)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}..L_CAPIUSER
          WHERE NAME = @kullaniciAdi
        `)

      return recordset?.[0]?.NR
    }
    catch (error) {
      consola.error(`Logo kullanıcı referansı alınırken hata oluştu:`, error)
      return undefined
    }
  },

  /**
   * Get fiche number from logical reference
   */
  getFichenoFromLogicalRef: async (logicalRef: number, veritabaniId: string): Promise<string | undefined> => {
    try {
      const result = await LogoSqlLookup.getInvoiceFichenoByLogicalref({ logicalref: logicalRef, veritabaniId })

      if (result) {
        return result.ficheno
      }

      consola.warn(`${logicalRef} referans numaralı fatura bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`Fatura numarası alınırken hata oluştu (LOGICALREF: ${logicalRef}):`, error)
      return undefined
    }
  },

  /**
   * Get dispatch fiche number from logical reference
   */
  getStficheNoFromLogicalRef: async (logicalRef: number, veritabaniId: string): Promise<string | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT FICHENO
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
        WHERE LOGICALREF = @logicalRef
      `

      const result = await logoConnection.request()
        .input('logicalRef', sql.Int, logicalRef)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].FICHENO
      }

      consola.warn(`${logicalRef} referans numaralı irsaliye bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`İrsaliye numarası alınırken hata oluştu (LOGICALREF: ${logicalRef}):`, error)
      return undefined
    }
  },

  /**
   * Get logical reference from dispatch fiche number
   */
  getLogicalRefFromStficheNo: async (ficheNo: string, veritabaniId: string): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
        WHERE FICHENO = @ficheNo
      `

      const result = await logoConnection.request()
        .input('ficheNo', sql.VarChar, ficheNo)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }

      consola.warn(`${ficheNo} numaralı irsaliye bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`İrsaliye LOGICALREF alınırken hata oluştu (FICHENO: ${ficheNo}):`, error)
      return undefined
    }
  },
}

export default LogoSqlService
