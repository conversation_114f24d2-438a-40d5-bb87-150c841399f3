import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import DatabaseService from './database-service.ts'
import LogoSqlDirectProcessor from './logo-sql-direct-processor.ts'
import SatisFaturaRestValidator from '../validators/rest-validator.ts'
import SatisFaturaSqlValidator from '../validators/sql-validator.ts'

/**
 * Consolidated Logo SQL Service for Sales Invoices
 * Combines lookup, validation, and utility functions for SQL-based Logo ERP integration
 * Preserves all database operations and column mappings from original services
 */
const LogoSqlService = {

  // ============================================================================
  // LOOKUP OPERATIONS - Functions that query Logo ERP for reference data
  // ============================================================================

  /**
   * Re-export shared exchange rate functions from LogoSqlUtils
   */
  getExchangeRate: LogoSqlUtils.getExchangeRate,
  getExchangeRateByType: LogoSqlUtils.getExchangeRateByType,

  /**
   * Get unit code for item from Logo ERP
   * Tables: LG_{FIRMA}_ITEMS, LG_{FIRMA}_UNITSETF, LG_{FIRMA}_UNITSETL
   * Columns: items.CODE, items.UNITSETREF, unitsetf.LOGICALREF, unitsetl.CODE, unitsetl.MAINUNIT
   */
  getUnitCodeForItem: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const query = `
          SELECT unitsetl.CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS as items
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
              ON items.UNITSETREF = unitsetf.LOGICALREF
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
              ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
              AND unitsetl.MAINUNIT = 1
          WHERE items.CODE = @code
      `

      const { recordset } = await logoConnection.request().input('code', code).query(query)

      return recordset?.[0]?.CODE || 'ADET'
    }
    catch (error) {
      consola.error('Malzeme birim kodu alınırken hata oluştu:', error)
      return 'ADET'
    }
  },

  /**
   * Get unit code for service card from Logo ERP
   * Tables: LG_{FIRMA}_SRVCARD, LG_{FIRMA}_UNITSETF, LG_{FIRMA}_UNITSETL
   * Columns: srvcard.CODE, srvcard.UNITSETREF, unitsetf.LOGICALREF, unitsetl.CODE, unitsetl.MAINUNIT
   */
  getUnitCodeForSrvcard: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const query = `
          SELECT unitsetl.CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
              ON srvcard.UNITSETREF = unitsetf.LOGICALREF
          LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
              ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
              AND unitsetl.MAINUNIT = 1
          WHERE srvcard.CODE = @code
      `

      const { recordset } = await logoConnection.request().input('code', code).query(query)

      return recordset?.[0]?.CODE || 'ADET'
    }
    catch (error) {
      consola.error('Hizmet birim seti kodu alınırken hata oluştu:', error)
      return 'ADET'
    }
  },

  /**
   * Get item code from Logo ERP
   * Tables: LG_{FIRMA}_ITEMS
   * Columns: CODE
   */
  getItemCode: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS
            WHERE CODE = @code
        `)

      if (!recordset || recordset.length === 0) {
        throw new Error(`"${code}" kodlu malzeme Logo'da bulunamadı. Lütfen malzeme kartını kontrol edin.`)
      }

      return recordset[0].CODE
    }
    catch (error) {
      consola.error('Malzeme kodu alınırken hata oluştu:', error)
      throw error
    }
  },

  /**
   * Get customer VKN or TCKN from Logo ERP
   * Tables: LG_{FIRMA}_CLCARD
   * Columns: ISPERSCOMP, TCKNO, TAXNR, CODE
   */
  getCustomerVknOrTckn: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string | undefined> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('arpCode', code)
        .query<{ vknoveyatckno: string }>(`
            SELECT IIF(clcard.ISPERSCOMP=1, clcard.TCKNO, clcard.TAXNR) as vknoveyatckno
            FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD as clcard
            WHERE clcard.CODE = @arpCode
        `)

      if (!recordset || recordset.length === 0) {
        throw new Error('Cari hesap sistemde kayıtlı değil')
      }

      return recordset[0]?.vknoveyatckno
    }
    catch (error) {
      consola.error('Cari hesap VKN/TCKN bilgisi alınırken hata oluştu:', error)
      throw error
    }
  },

  /**
   * Get invoice fiche number by logical reference
   * Tables: LG_{FIRMA}_{DONEM}_INVOICE
   * Columns: FICHENO, LOGICALREF
   */
  getInvoiceFichenoByLogicalref: async ({ logicalref, veritabaniId }: { logicalref: number, veritabaniId: string }): Promise<{ ficheno: string, logicalref: number } | undefined> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('logicalref', logicalref)
        .query(`
          SELECT FICHENO, LOGICALREF
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE LOGICALREF = @logicalref
        `)

      if (recordset && recordset.length > 0) {
        return {
          ficheno: recordset[0].FICHENO,
          logicalref: recordset[0].LOGICALREF,
        }
      }

      return undefined
    }
    catch (error) {
      consola.error(`Fatura numarası Logo'dan alınırken hata oluştu:`, error)
      return undefined
    }
  },

  /**
   * Re-export shared source cost group function from LogoSqlUtils
   * Tables: L_CAPIWHOUSE
   * Columns: COSTGRP, NR, FIRMNR
   */
  getSourceCostGrp: LogoSqlUtils.getSourceCostGrp,

  /**
   * Get user reference from L_CAPIUSER table
   * Tables: L_CAPIUSER (master db)
   * Columns: NR, NAME
   */
  getUserRefFromCapiuser: async ({
    kullaniciAdi,
    veritabaniId,
  }: {
    kullaniciAdi: string
    veritabaniId: string
  }): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const { recordset } = await logoConnection
        .request()
        .input('kullaniciAdi', kullaniciAdi)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}..L_CAPIUSER
          WHERE NAME = @kullaniciAdi
        `)

      return recordset?.[0]?.NR
    }
    catch (error) {
      consola.error(`Logo kullanıcı referansı alınırken hata oluştu:`, error)
      return undefined
    }
  },

  /**
   * Get fiche number from logical reference
   * Delegates to getInvoiceFichenoByLogicalref for consistency
   */
  getFichenoFromLogicalRef: async (logicalRef: number, veritabaniId: string): Promise<string | undefined> => {
    try {
      const result = await LogoSqlService.getInvoiceFichenoByLogicalref({ logicalref: logicalRef, veritabaniId })

      if (result) {
        return result.ficheno
      }

      consola.warn(`${logicalRef} referans numaralı fatura bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`Fatura numarası alınırken hata oluştu (LOGICALREF: ${logicalRef}):`, error)
      return undefined
    }
  },

  /**
   * Get dispatch fiche number from logical reference
   * Tables: LG_{FIRMA}_{DONEM}_STFICHE
   * Columns: FICHENO, LOGICALREF
   */
  getStficheNoFromLogicalRef: async (logicalRef: number, veritabaniId: string): Promise<string | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT FICHENO
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
        WHERE LOGICALREF = @logicalRef
      `

      const result = await logoConnection.request()
        .input('logicalRef', sql.Int, logicalRef)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].FICHENO
      }

      consola.warn(`${logicalRef} referans numaralı irsaliye bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`İrsaliye numarası alınırken hata oluştu (LOGICALREF: ${logicalRef}):`, error)
      return undefined
    }
  },

  /**
   * Get logical reference from dispatch fiche number
   * Tables: LG_{FIRMA}_{DONEM}_STFICHE
   * Columns: LOGICALREF, FICHENO
   */
  getLogicalRefFromStficheNo: async (ficheNo: string, veritabaniId: string): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
        WHERE FICHENO = @ficheNo
      `

      const result = await logoConnection.request()
        .input('ficheNo', sql.VarChar, ficheNo)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }

      consola.warn(`${ficheNo} numaralı irsaliye bulunamadı.`)
      return undefined
    }
    catch (error) {
      consola.error(`İrsaliye LOGICALREF alınırken hata oluştu (FICHENO: ${ficheNo}):`, error)
      return undefined
    }
  },

  // ============================================================================
  // VALIDATION OPERATIONS - Functions that validate data against Logo ERP
  // ============================================================================

  /**
   * Check if service code exists in Logo ERP
   * Tables: LG_{FIRMA}_SRVCARD
   * Columns: CODE
   */
  checkServiceCodeExists: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
            WHERE srvcard.CODE = @code
        `)

      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Hizmet kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if customer exists in Logo ERP
   * Tables: LG_{FIRMA}_CLCARD
   * Columns: CODE
   */
  checkCustomerExists: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', code)
        .query(`
            SELECT CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
            WHERE CODE = @code
        `)

      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Cari hesap kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if cost center exists in Logo ERP
   * Tables: LG_{FIRMA}_EMCENTER
   * Columns: CODE
   */
  checkMasrafMerkeziExists: async ({ masraf_merkezi_kodu, veritabaniId }: { masraf_merkezi_kodu: string, veritabaniId: string }): Promise<boolean> => {
    if (!masraf_merkezi_kodu) {
      return true
    }
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', masraf_merkezi_kodu)
        .query(`
          SELECT CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_EMCENTER
          WHERE CODE = @code
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Masraf merkezi kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if project code exists in Logo ERP
   * Tables: LG_{FIRMA}_PROJECT
   * Columns: CODE
   */
  checkProjeKoduExists: async ({ proje_kodu, veritabaniId }: { proje_kodu: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('code', proje_kodu)
        .query(`
          SELECT CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_PROJECT
          WHERE CODE = @code
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Proje kodu kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if warehouse exists in Logo ERP
   * Tables: L_CAPIWHOUSE (master db)
   * Columns: NR, FIRMNR
   */
  checkWarehouseExists: async ({ ambar, veritabaniId }: { ambar: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', ambar)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIWHOUSE
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Ambar kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if division exists in Logo ERP
   * Tables: L_CAPIDIV (master db)
   * Columns: NR, FIRMNR
   */
  checkDivisionExists: async ({ isyeri, veritabaniId }: { isyeri: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', isyeri)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDIV
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('İşyeri kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if department exists in Logo ERP
   * Tables: L_CAPIDEPT (master db)
   * Columns: NR, FIRMNR
   */
  checkDepartmentExists: async ({ bolum, veritabaniId }: { bolum: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', bolum)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDEPT
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Bölüm kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if factory exists in Logo ERP
   * Tables: L_CAPIFACTORY (master db)
   * Columns: NR, FIRMNR
   */
  checkFactoryExists: async ({ fabrika, veritabaniId }: { fabrika: number, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('firmnr', logoConfig.erp.firma_numarasi)
        .input('nr', fabrika)
        .query(`
          SELECT NR
          FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIFACTORY
          WHERE FIRMNR = @firmnr AND NR = @nr
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Fabrika kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if invoice number exists in Logo ERP
   * Tables: LG_{FIRMA}_{DONEM}_INVOICE
   * Columns: FICHENO, TRCODE
   */
  checkInvoiceNumberExists: async ({
    fatura_no,
    trcode,
    veritabaniId,
  }: {
    fatura_no: string
    trcode: number
    veritabaniId: string
  }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('ficheno', fatura_no)
        .input('trcode', trcode)
        .query(`
          SELECT FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE FICHENO = @ficheno AND TRCODE = @trcode
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('Fatura numarası kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  /**
   * Check if dispatch number exists in Logo ERP
   * Tables: LG_{FIRMA}_{DONEM}_STFICHE
   * Columns: FICHENO, TRCODE
   */
  checkDispatchNumberExists: async ({
    irsaliye_no,
    veritabaniId,
  }: {
    irsaliye_no: string
    veritabaniId: string
  }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const { recordset } = await logoConnection
        .request()
        .input('ficheno', irsaliye_no)
        .input('trcode', 7) // 7 is for sales invoice in STFICHE
        .query(`
          SELECT FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE
          WHERE FICHENO = @ficheno AND TRCODE = @trcode
        `)
      return recordset?.length > 0
    }
    catch (error) {
      consola.error('İrsaliye numarası kontrolü sırasında hata oluştu:', error)
      return false
    }
  },

  // ============================================================================
  // UTILITY OPERATIONS - Configuration and coordination functions
  // ============================================================================

  /**
   * Get use REST flag from configuration
   * Re-export from LogoConfigService
   */
  getUseRestFlag: LogoConfigService.getUseRestFlag,

  /**
   * Comprehensive validation service for sales invoice requests
   * Coordinates between REST and SQL validators based on integration method
   */
  validateSatisFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: any
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // Check if REST API is used
      const useRest = await LogoConfigService.getUseRestFlag(veritabaniId)

      // Use appropriate validator based on integration method
      if (useRest) {
        return await SatisFaturaRestValidator.validateSatisFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
      else {
        return await SatisFaturaSqlValidator.validateSatisFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
    }
    catch (error: any) {
      consola.error('Fatura doğrulama sırasında hata oluştu:', error)
      return {
        isValid: false,
        error: `Fatura doğrulama sırasında beklenmeyen bir hata oluştu: ${error.message}`,
      }
    }
  },

  // ============================================================================
  // DELEGATED OPERATIONS - Functions that delegate to other services
  // ============================================================================

  /**
   * Process direct SQL operations to Logo ERP
   * Delegates to LogoSqlDirectProcessor
   */
  processDirectSql: LogoSqlDirectProcessor.processDirectSql,

  /**
   * Database service functions for application database operations
   * Delegates to DatabaseService
   */
  insertSatisFatura: DatabaseService.insertSatisFatura,
  insertSatisFaturaSatirlari: DatabaseService.insertSatisFaturaSatirlari,
  insertLogoSatisFatura: DatabaseService.insertLogoSatisFatura,
  insertLogoSatisFaturaIrsaliyesi: DatabaseService.insertLogoSatisFaturaIrsaliyesi,
  insertLogoSatisFaturaSatirlari: DatabaseService.insertLogoSatisFaturaSatirlari,
}

export default LogoSqlService
