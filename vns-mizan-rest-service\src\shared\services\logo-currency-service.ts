import type { IResult } from 'mssql'
import consola from 'consola'
import sql from 'mssql'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

interface CurrencyTypeResult {
  CURTYPE: number
}

interface ExchangeRateResult {
  RATES1: number
}

interface FirmRepCurrResult {
  FIRMREPCURR: number
}

/**
 * Service for currency-related Logo ERP operations
 */
const LogoCurrencyService = {
  /**
   * Fetches the CURTYPE from L_CURRENCYLIST based on currency code.
   */
  getCurrencyTypeFromCode: async (
    currencyCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!currencyCode) {
      return 0 // Default to 0 (TL) if no code provided?
    }
    const upperCurrencyCode = currencyCode.toUpperCase()

    if (upperCurrencyCode === 'TL' || upperCurrencyCode === 'TRY') {
      return 0
    }

    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically
      const tableName = `${logoConfig.erp.logodb_master || 'LG'}_CURRENCYLIST`

      const query = `
        SELECT TOP 1 CURTYPE
        FROM ${tableName}
        WHERE CURCODE = @currencyCode
        AND FIRMNR = ${logoConfig.erp.firma_numarasi}
      `
      // Removed FIRMNR from WHERE clause as L_CURRENCYLIST might be global or handled differently
      // Check Logo documentation if FIRMNR is needed here
      const result: IResult<CurrencyTypeResult[]> = await logoConnection.request()
        .input('currencyCode', sql.VarChar, upperCurrencyCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].CURTYPE
      }
      consola.warn(`Döviz tipi bulunamadı, döviz kodu: ${upperCurrencyCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Döviz tipi alınırken hata oluştu, döviz kodu: ${upperCurrencyCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the FIRMREPCURR from L_CAPIFIRM based on firm number.
   */
  getFirmRepCurr: async (veritabaniId: string): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const tableName = `${logoConfig.erp.logodb_master}..L_CAPIFIRM`

      const query = `
        SELECT TOP 1 FIRMREPCURR
        FROM ${tableName}
        WHERE NR = @firmaNr
      `
      const result: IResult<FirmRepCurrResult[]> = await logoConnection.request()
        .input('firmaNr', sql.Int, logoConfig.erp.firma_numarasi)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].FIRMREPCURR
      }
      consola.warn(`Firma raporlama para birimi bulunamadı, firma no: ${logoConfig.erp.firma_numarasi}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Firma raporlama para birimi alınırken hata oluştu, veritabanı ID: ${veritabaniId}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the exchange rate (RATES1) from L_DAILYEXCHANGES based on date and currency type.
   */
  getExchangeRate: async (
    date: Date | string | undefined,
    currencyType: number | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!date || currencyType === undefined) {
      return undefined
    }

    // Convert string date to Date object if needed
    const dateObj = typeof date === 'string' ? new Date(date) : date

    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Determine which table to use for exchange rates
      const { recordset: tableExists } = await logoConnection.request().query(`
          SELECT OBJECT_ID('LG_EXCHANGE_${logoConfig.erp.firma_numarasi}') as table_exists
      `)

      const exchangeTableName = tableExists[0]?.table_exists
        ? `LG_EXCHANGE_${logoConfig.erp.firma_numarasi}`
        : `${logoConfig.erp.logodb_master}..L_DAILYEXCHANGES`

      const query = `
        SELECT TOP 1 RATES1
        FROM ${exchangeTableName}
        WHERE EDATE = @date
        AND CRTYPE = @currencyType
      `
      const result: IResult<ExchangeRateResult[]> = await logoConnection.request()
        .input('date', sql.Date, dateObj)
        .input('currencyType', sql.Int, currencyType)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].RATES1
      }
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Döviz kuru alınırken hata oluştu, tarih: ${dateObj?.toISOString()}, döviz tipi: ${currencyType}:`, error)
      return undefined
    }
  },
}

export default LogoCurrencyService
