import { copyFile, mkdir, readFile } from 'node:fs/promises'
import { consola } from 'consola'
import { defineConfig } from 'tsup'
import { dependencies } from './package.json'
import generateOpenApiSpec from './src/scripts/generate-openapi'

const openapiJsonPlugin = {
  name: 'openapi-json',
  setup(build) {
    build.onEnd(async () => {
      try {
        await generateOpenApiSpec()
      }
      catch (error) {
        consola.error('Openapi dokümantasyonu oluşturulurken hata alındı:', error)
      }
    })
  },
}

const importMetaPlugin = {
  name: 'import-meta',
  setup(build) {
    build.onLoad({ filter: /\.[jt]s$/ }, async (args) => {
      const contents = await readFile(args.path, 'utf8')
      const modifiedContents = contents.replace(/import\.meta\.dirname/g, '__dirname')
      const loader = args.path.endsWith('.ts') ? 'ts' : 'js'
      return { contents: modifiedContents, loader }
    })
  },
}

export default defineConfig({
  entry: ['index.ts', 'src/scripts/generate-certificates.ts'],
  splitting: false,
  clean: true,
  minify: true,
  format: ['cjs'],
  shims: true,
  noExternal: Object.keys(dependencies),
  esbuildPlugins: [
    openapiJsonPlugin,
    importMetaPlugin,
  ],
  async onSuccess() {
    await mkdir('./dist', { recursive: true })
    await copyFile(
      './src/scripts/openapi.json',
      './dist/openapi.json',
    )
    consola.success('OpenAPI dokümantasyonu dist klasörüne kopyalandı.')
  },
})
