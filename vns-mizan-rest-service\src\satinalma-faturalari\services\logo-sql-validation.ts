import type { z } from 'zod'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'
import { consola } from 'consola'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import SatinalmaFaturaRestValidator from '../validators/rest-validator.ts'
import SatinalmaFaturaSqlValidator from '../validators/sql-validator.ts'

/**
 * Service for Logo SQL validation operations for purchase invoices
 * Coordinates between REST and SQL validators based on integration method
 */
const LogoSqlValidation = {
  // Re-export shared configuration function
  getUseRestFlag: LogoConfigService.getUseRestFlag,

  /**
   * Validates purchase invoice request data against Logo ERP
   * Uses appropriate validator based on integration method
   */
  validateSatinalmaFaturaRequest: async ({
    requestData,
    veritabaniId,
  }: {
    requestData: z.infer<typeof satinalmaFaturalariSchema>
    veritabaniId: string
  }): Promise<{ isValid: boolean, error?: string }> => {
    try {
      // Check if REST API is used
      const useRest = await LogoConfigService.getUseRestFlag(veritabaniId)

      // Use appropriate validator based on integration method
      if (useRest) {
        return await SatinalmaFaturaRestValidator.validateSatinalmaFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
      else {
        return await SatinalmaFaturaSqlValidator.validateSatinalmaFaturaRequest({
          requestData,
          veritabaniId,
        })
      }
    }
    catch (error) {
      consola.error('Satınalma faturası doğrulanırken hata oluştu:', error)
      return { isValid: false, error: `Doğrulama hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}` }
    }
  },
}

export default LogoSqlValidation
