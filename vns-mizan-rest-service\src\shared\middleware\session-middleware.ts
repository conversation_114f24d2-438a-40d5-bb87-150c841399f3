import type { NextFunction, Request, Response } from 'express'
import type { SessionData } from 'express-session'
import type { SessionCallback, SessionWithCookie } from '../types/session.ts'
import consola from 'consola'
import session from 'express-session'
import sql from 'mssql'
import DbService from '../services/db-service.ts'
import { readConfig } from '../utils/config-utils.ts'

/**
 * Convert minutes to milliseconds
 */
const minutesToMs = (minutes: number): number => minutes * 60 * 1000

/**
 * Interface for session data including user information
 */
declare module 'express-session' {
  interface SessionData {
    userId?: number
    email?: string
  }
}

/**
 * Custom session store using MSSQL
 */
class MSSQLSessionStore extends session.Store {
  private cleanupInterval: NodeJS.Timeout
  private readonly defaultMaxAge: number

  constructor(maxAgeMinutes: number) {
    super()
    this.defaultMaxAge = minutesToMs(maxAgeMinutes)
    // Clean expired sessions every hour
    this.cleanupInterval = setInterval(() => this.cleanup(), 60 * 60 * 1000)
  }

  /**
   * Get session data
   */
  get(sid: string, callback: SessionCallback): void {
    ;(async () => {
      try {
        const pool = await DbService.getConnection('db')
        const result = await pool.request().input('sid', sql.VarChar, sid).query('SELECT session FROM sessions WHERE sid = @sid AND expires > GETDATE()')

        if (!result.recordset[0]) {
          return callback(null, null)
        }

        callback(null, JSON.parse(result.recordset[0].session))
      }
      catch (err) {
        callback(err as Error, null)
      }
    })()
  }

  /**
   * Save session data
   */
  set(sid: string, session: SessionData, callback?: (err?: Error) => void): void {
    ;(async () => {
      try {
        const pool = await DbService.getConnection('db')
        const sessionWithCookie = session as SessionWithCookie
        const maxAge = sessionWithCookie.cookie?.maxAge || this.defaultMaxAge
        const expires = new Date(Date.now() + maxAge)

        await pool
          .request()
          .input('sid', sql.VarChar, sid)
          .input('session', sql.NVarChar, JSON.stringify(session))
          .input('expires', sql.DateTimeOffset, expires)
          .query(`
            MERGE sessions AS target
            USING (SELECT @sid as sid) AS source
            ON target.sid = source.sid
            WHEN MATCHED THEN
              UPDATE SET session = @session, expires = @expires
            WHEN NOT MATCHED THEN
              INSERT (sid, session, expires)
              VALUES (@sid, @session, @expires);
          `)

        if (callback)
          callback()
      }
      catch (err) {
        if (callback)
          callback(err as Error)
      }
    })()
  }

  /**
   * Destroy session data
   */
  destroy(sid: string, callback?: (err?: Error) => void): void {
    ;(async () => {
      try {
        const pool = await DbService.getConnection('db')
        await pool.request().input('sid', sql.VarChar, sid).query('DELETE FROM sessions WHERE sid = @sid')

        if (callback)
          callback()
      }
      catch (err) {
        if (callback)
          callback(err as Error)
      }
    })()
  }

  /**
   * Clean up expired sessions
   */
  private async cleanup() {
    try {
      const pool = await DbService.getConnection('db')
      await pool.request().query('DELETE FROM sessions WHERE expires <= GETDATE()')
    }
    catch (err) {
      consola.error('Session cleanup error:', err)
    }
  }
}

/**
 * Configure session middleware
 */
export async function sessionMiddleware() {
  const config = await readConfig()
  const { session: sessionConfig, https: httpsConfig } = config.project_settings

  // Use HTTPS configuration to determine if cookies should be secure
  const useSecureCookies = httpsConfig?.enabled || sessionConfig.cookie.secure

  return session({
    store: new MSSQLSessionStore(sessionConfig.cookie.max_age),
    secret: sessionConfig.secret,
    resave: false,
    saveUninitialized: false,
    name: 'sessionId',
    cookie: {
      secure: useSecureCookies,
      httpOnly: true,
      maxAge: minutesToMs(sessionConfig.cookie.max_age),
    },
  })
}

/**
 * Check if session is expired
 */
function isSessionExpired(req: Request): boolean {
  if (!req.session?.cookie?.expires) {
    return true
  }
  return new Date() > new Date(req.session.cookie.expires)
}

/**
 * Type guard to check if session exists and has userId
 */
function hasValidSession(req: Request): req is Request & { session: { userId: number, email?: string } } {
  return req.session !== undefined && typeof req.session.userId === 'number' && !isSessionExpired(req)
}

/**
 * Middleware to check if user is authenticated
 */
export async function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (!req.session?.userId) {
    res.status(401).json({
      error: 'Oturum Bulunamadı',
      message: 'Lütfen giriş yapınız',
    })
    return
  }

  if (isSessionExpired(req)) {
    clearUserSession(req)
    res.status(401).json({
      error: 'Oturum Süresi Doldu',
      message: 'Oturumunuzun süresi doldu, lütfen tekrar giriş yapınız',
    })
    return
  }

  if (hasValidSession(req)) {
    // Extend session if it's close to expiring (optional)
    const timeUntilExpiry = req.session.cookie.expires ? new Date(req.session.cookie.expires).getTime() - Date.now() : 0

    const config = await readConfig()
    if (timeUntilExpiry < minutesToMs(5)) {
      // Less than 5 minutes until expiry
      req.session.cookie.maxAge = minutesToMs(config.project_settings.session.cookie.max_age)
    }

    return next()
  }

  res.status(401).json({
    error: 'Geçersiz Oturum',
    message: 'Lütfen tekrar giriş yapınız',
  })
}

/**
 * Middleware to set user session
 */
export function setUserSession(req: Request, userId: number, email: string) {
  if (req.session) {
    req.session.userId = userId
    req.session.email = email
  }
}

/**
 * Middleware to clear user session
 */
export function clearUserSession(req: Request) {
  if (req.session) {
    req.session.destroy((err: Error) => {
      if (err) {
        consola.error('Error destroying session:', err)
      }
    })
  }
}
