import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import * as LogoSqlUtils from '../../shared/services/logo-sql-utils.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo SQL lookup operations for purchase invoices
 */
const LogoSqlLookup = {
  /**
   * Gets cost group from warehouse number
   */
  getSourceCostGrp: async ({ nr, veritabaniId }: { nr: number, veritabaniId: string }): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const result = await logoConnection.request()
        .input('nr', sql.Int, nr)
        .query(`
          SELECT COSTGRP FROM L_CAPIWHOUSE
          WHERE NR = @nr AND FIRMNR = ${logoConfig.erp.firma_numarasi}
        `)

      if (result.recordset.length > 0) {
        return result.recordset[0].COSTGRP
      }
      return undefined
    }
    catch (error) {
      consola.error('Maliyet grubu alınırken hata oluştu:', error)
      return undefined
    }
  },

  /**
   * Gets invoice fiche number by logical reference
   */
  getInvoiceFichenoByLogicalref: async ({ logicalref, veritabaniId }: { logicalref: number, veritabaniId: string }): Promise<{ ficheno: string } | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const result = await logoConnection.request()
        .input('logicalref', sql.Int, logicalref)
        .query(`
          SELECT FICHENO FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE LOGICALREF = @logicalref
        `)

      if (result.recordset.length > 0) {
        return { ficheno: result.recordset[0].FICHENO }
      }
      return undefined
    }
    catch (error) {
      consola.error('Fatura numarası alınırken hata oluştu:', error)
      return undefined
    }
  },

  // Re-export shared exchange rate function
  getExchangeRateByType: LogoSqlUtils.getExchangeRateByType,
}

export default LogoSqlLookup
