# VNS Mizan Refactoring Guidelines

## Core Principle: No Backup Files

**IMPORTANT**: This codebase follows a strict **no-backup-files** policy. All refactoring operations must directly replace existing files without creating backup copies.

### ❌ Prohibited Backup Patterns
- `*-original.ts`
- `*_original.ts`
- `*-backup.ts`
- `*_backup.ts`
- `*-old.ts`
- `*_old.ts`
- `*-copy.ts`
- `*_copy.ts`

### ✅ Approved Refactoring Approach

#### 1. Direct File Replacement
```bash
# ❌ Wrong - Creates backup
mv service.ts service-original.ts
# Create new service.ts

# ✅ Correct - Direct replacement
# Edit service.ts directly using str-replace-editor tool
```

#### 2. Git-Based Version Control
- **Use Git for history**: All changes are tracked in git history
- **Commit frequently**: Make atomic commits during refactoring
- **Use branches**: Create feature branches for major refactoring work
- **Rollback capability**: Use `git revert` or `git reset` for rollbacks

#### 3. Refactoring Process

##### Service File Refactoring
1. **Analyze** the large service file
2. **Plan** the breakdown into smaller modules
3. **Extract** functions/classes using str-replace-editor
4. **Create** new files for extracted modules
5. **Update** imports in the original file
6. **Replace** the original file content directly
7. **Test** functionality after each step

##### Validator Architecture Changes
1. **Identify** validation logic patterns
2. **Create** separate REST and SQL validators
3. **Extract** common validation utilities
4. **Update** service files to use new validators
5. **Remove** old validation code directly from files

##### Utility Consolidation
1. **Find** duplicate utility functions
2. **Create** shared utility modules
3. **Update** all import statements
4. **Remove** duplicate code directly from files

### 4. File Organization Standards

#### Before Refactoring
```
src/module/
├── services/
│   └── large-service.ts (800+ lines)
```

#### After Refactoring
```
src/module/
├── services/
│   ├── main-service.ts (<200 lines)
│   ├── business-logic.ts (<200 lines)
│   └── data-processor.ts (<200 lines)
├── validators/
│   ├── rest-validator.ts
│   └── sql-validator.ts
├── transformers/
│   ├── rest-transformer.ts
│   └── sql-transformer.ts
└── utils/
    └── module-utils.ts
```

### 5. Quality Assurance

#### Code Quality Checks
- **Line limits**: Keep files under 200 lines for services
- **Single responsibility**: Each file has one clear purpose
- **Turkish compliance**: All messages in natural Turkish
- **Error handling**: Comprehensive error handling maintained
- **Type safety**: Full TypeScript compliance

#### Testing Strategy
- **Functional testing**: Ensure all existing functionality works
- **Integration testing**: Test module interactions
- **Error scenarios**: Test error handling paths
- **Performance**: Verify no performance degradation

### 6. Documentation Requirements

#### Code Documentation
- **Function comments**: Clear Turkish descriptions
- **Type definitions**: Comprehensive TypeScript types
- **Usage examples**: Include usage patterns
- **Error handling**: Document error scenarios

#### Architectural Documentation
- **Module structure**: Document new file organization
- **Dependencies**: Update dependency diagrams
- **Integration points**: Document service interactions
- **Configuration**: Update configuration guides

### 7. Rollback Strategy

#### Git-Based Rollback
```bash
# Rollback specific commit
git revert <commit-hash>

# Rollback to previous state
git reset --hard HEAD~1

# Rollback specific file
git checkout HEAD~1 -- path/to/file.ts
```

#### Emergency Recovery
- **Branch protection**: Keep working branches
- **Incremental commits**: Small, reversible changes
- **Testing checkpoints**: Test after each major change
- **Documentation**: Track what was changed and why

### 8. Implementation Examples

#### Example 1: Service Refactoring
```typescript
// Before: large-service.ts (800 lines)
// After: Split into multiple focused files

import { transformData } from '../transformers/rest-transformer.ts'
// main-service.ts
import { validateRequest } from '../validators/rest-validator.ts'
import { processBusinessLogic } from './business-logic.ts'

// business-logic.ts
export async function processBusinessLogic(data) {
  // Focused business logic only
}
```

#### Example 2: Validator Split
```typescript
// Before: single validator with use_rest conditions
// After: Separate validators

// rest-validator.ts
export async function validateRestRequest(data) {
  // REST-specific validation only
}

// sql-validator.ts
export async function validateSqlRequest(data) {
  // SQL-specific validation only
}
```

### 9. Monitoring and Maintenance

#### Continuous Improvement
- **Regular reviews**: Monthly code quality reviews
- **Refactoring opportunities**: Identify files growing too large
- **Performance monitoring**: Track performance impacts
- **Developer feedback**: Collect team feedback on structure

#### Metrics Tracking
- **File sizes**: Monitor line counts per file
- **Complexity**: Track cyclomatic complexity
- **Dependencies**: Monitor coupling between modules
- **Test coverage**: Maintain high test coverage

---

## Summary

This refactoring approach ensures:
- ✅ **Clean codebase**: No backup file clutter
- ✅ **Git history**: Full version control tracking
- ✅ **Maintainability**: Smaller, focused files
- ✅ **Functionality**: All existing features preserved
- ✅ **Quality**: High code quality standards maintained
- ✅ **Turkish compliance**: Natural language consistency
- ✅ **Rollback capability**: Safe recovery options via Git

**Remember**: Trust Git for version control, not backup files!
