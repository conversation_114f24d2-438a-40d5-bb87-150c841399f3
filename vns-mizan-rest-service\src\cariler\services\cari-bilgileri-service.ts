import type { IR<PERSON><PERSON> } from 'mssql'
import type { CariBilgisi } from '../models/cari-bilgileri.ts'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Get cari hesap bilgileri from Logo database
 */
export async function getCariBilgileri({ veritabaniId }: { veritabaniId: string }): Promise<CariBilgisi[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        clcard.LOGICALREF AS id,
        clcard.CODE AS kodu,
        clcard.CAPIBLOCK_CREADEDDATE AS create_date,
        clcard.CAPIBLOCK_MODIFIEDDATE AS update_date,
        clcard.DEFINITION_ AS unvan1,
        clcard.DEFINITION2 AS unvan2,
        clcard.SPECODE AS muhasebe_kodu,
        clcard.CCURRENCY AS doviz1,
        ISNULL(currencylist.CURCODE, '') AS doviz1_adi,
        clcard.TAXOFFICE AS vergi_dairesi,
        clcard.TAXNR AS vergi_no,
        ISNULL(sectormain.EXPLANATION, '') AS sektor,
        clcard.SPECODE AS bolge,
        clcard.SPECODE2 AS grup,
        ISNULL(slsman.DEFINITION_, '') AS temsilci,
        0 AS fiyat_listesi,
        CASE WHEN clcard.ACTIVE = 0 THEN 1 ELSE 0 END AS kilitli,
        ISNULL(clcard.ACCEPTEINV, 0) AS efatura_mi,
        clcard.EMAILADDR AS mail,
        clcard.TELNRS1 AS telefon,
        clcard.CITY AS il,
        clcard.TOWN AS ilce,
        clcard.COUNTRY AS ulke,
        clcard.ADDR1 AS adres1,
        clcard.ADDR2 AS adres2,
        1 AS adresNo,
        clcard.ADRESSNO AS adresKodu,
        clcard.POSTCODE AS postaKodu,
        ISNULL(slsman.CODE, '') AS temsilciNo,
        clcard.POSTLABELCODE AS eFaturaAlias,
        clcard.DEFINITION_ AS cariUnvan,
        0 AS hareket_tipi,
        '${veritabaniId}' as veritabani_id
      FROM 
        LG_${logoConfig.erp.firma_numarasi}_CLCARD as clcard
      LEFT JOIN 
        ${logoConfig.erp.logodb_master}..L_CURRENCYLIST as currencylist 
          ON currencylist.CURTYPE = clcard.CCURRENCY 
          and currencylist.FIRMNR=${logoConfig.erp.firma_numarasi}
      LEFT JOIN
        LG_${logoConfig.erp.firma_numarasi}_SECTORMAIN as sectormain 
          ON sectormain.LOGICALREF = clcard.SECTORMAINREF
      LEFT JOIN
        LG_${logoConfig.erp.firma_numarasi}_SLSCLREL as slsclrel 
          ON slsclrel.CLIENTREF = clcard.LOGICALREF 
          and slsclrel.CLLINENO_=1
      LEFT JOIN
        ${logoConfig.erp.logodb_master}..LG_SLSMAN as slsman 
          ON slsman.LOGICALREF = slsclrel.SALESMANREF 
          and slsman.FIRMNR in (${logoConfig.erp.firma_numarasi},-1)
      WHERE 
        clcard.CARDTYPE <> 22
      ORDER BY 
        clcard.CODE
    `
    const result: IResult<CariBilgisi[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Cari bilgileri sorgulanırken hata:', error)
    throw error
  }
}
