import type { IResult } from 'mssql'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

interface CariPersonel {
  id: number
  kodu: string
  adi: string
  update_date: Date
  veritabani_id: string
}

/**
 * Get cari personel (satış elemanları) from Logo database
 */
export async function getCariPersonel({ veritabaniId }: { veritabaniId: string }): Promise<CariPersonel[]> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const query = `
      SELECT 
        slsman.LOGICALREF AS id,
        slsman.CODE AS kodu,
        slsman.DEFINITION_ AS adi,
        slsman.CAPIBLOCK_MODIFIEDDATE AS update_date,
        '${veritabaniId}' as veritabani_id
      FROM 
        ${logoConfig.erp.logodb_master}..LG_SLSMAN as slsman
      WHERE 
        slsman.FIRMNR in (${logoConfig.erp.firma_numarasi},-1)
        and slsman.ACTIVE = 0
      ORDER BY 
        slsman.CODE
    `
    const result: IResult<CariPersonel[]> = await logoConnection.request().query(query)
    return result.recordset
  }
  catch (error) {
    consola.error('Cari personel sorgulanırken hata:', error)
    throw error
  }
}
