/**
 * Interface for cari hesap bilgileri
 */
export interface CariBilgisi {
  id: number
  kodu: string
  create_date: Date
  update_date: Date
  unvan1: string
  unvan2: string
  muhasebe_kodu: string
  doviz1: number
  doviz1_adi: string
  vergi_dairesi: string
  vergi_no: string
  sektor: string
  bolge: string
  grup: string
  temsilci: string
  fiyat_listesi: number
  kilitli: boolean
  efatura_mi: boolean
  mail: string
  telefon: string
  il: string
  ilce: string
  ulke: string
  adres1: string
  adres2: string
  adresNo: number
  adresKodu: string
  postaKodu: string
  temsilciNo: string
  eFaturaAlias: string
  cariUnvan: string
  hareket_tipi: number
  veritabani_id: string
}
