import type { Request<PERSON><PERSON><PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getCariPersonel } from '../services/cari-personel-service.ts'

const router = Router()

/**
 * Get cari personel validation schema
 */
const getCariPersonelSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /cari-personel:
 *   get:
 *     tags:
 *       - Cari Personel
 *     summary: Logo veritabanından cari personel bilgilerini listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: number
 *                   kodu:
 *                     type: string
 *                   adi:
 *                     type: string
 *                   update_date:
 *                     type: string
 *                     format: date-time
 *                   veritabani_id:
 *                     type: string
 *       400:
 *         description: Geçersiz istek
 *       500:
 *         description: Sunucu hatası
 */
const getCariPersonelHandler: RequestHandler = async (req, res) => {
  try {
    const result = getCariPersonelSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const cariPersonel = await getCariPersonel({ veritabaniId: veritabani_id })
    res.json(cariPersonel)
  }
  catch (error) {
    consola.error('Cari personel alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Cari personel alınırken bir hata oluştu',
    })
  }
}

router.get('/', getCariPersonelHandler)

export default router
