import type { RequestHandler } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getLogoKullanicilari } from '../services/logo-kullanicilari-service.ts'

const router = Router()

const getLogoKullanicilariSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * /logo-kullanicilari:
 *   get:
 *     tags: [Logo Kullanıcıları]
 *     summary: Logo veritabanındaki kullanıcıları listeler
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID'si
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   numara:
 *                     type: number
 *                     description: Logo kullanıcı numarası
 *                   kullanici_adi:
 *                     type: string
 *                     description: Kullanıcı adı
 *                   adi:
 *                     type: string
 *                     description: Kullanıcının adı
 *                   soyadi:
 *                     type: string
 *                     description: Kullanıcının soyadı
 *                   aktif:
 *                     type: number
 *                     description: Kullanıcı aktif mi? (1=Aktif, 0=Pasif)
 *                   veritabani_id:
 *                     type: string
 *                     description: Logo veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *       401:
 *         description: Yetkisiz erişim
 *       500:
 *         description: Sunucu hatası
 */
const getLogoKullanicilariHandler: RequestHandler = async (req, res) => {
  try {
    const result = getLogoKullanicilariSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const logoKullanicilari = await getLogoKullanicilari({ veritabaniId: veritabani_id })
    res.json(logoKullanicilari)
  }
  catch (error) {
    consola.error('Logo kullanıcıları alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Logo kullanıcıları alınırken bir hata oluştu',
    })
  }
}

router.get('/', getLogoKullanicilariHandler)

export default router
