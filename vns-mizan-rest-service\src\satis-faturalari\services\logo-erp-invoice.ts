import type { SatisFaturaHeader } from '../models/sales-invoice.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for Logo ERP INVOICE table operations
 * Handles insertions into LG_{FIRMA}_{DONEM}_INVOICE table with comprehensive column mapping
 * Preserves all 150+ database columns and field assignments from original logo-erp-integration.ts
 */
const LogoErpInvoice = {
  /**
   * Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
   * Preserves all database column mappings and SQL operations from original implementation
   */
  insertLogoActualInvoice: async ({
    invoice,
    veritabaniId,
    requestData,
    totals,
    logoCredentials,
  }: {
    invoice: SatisFaturaHeader
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId,
        })
      }

      // Generate a unique fiche number
      let ficheno
      // Check if a format is provided for fatura_no
      const faturaFormat = requestData?.fatura_numarasi_formati

      if (faturaFormat && faturaFormat.includes('_')) {
        // Use formatted fiche number generation
        ficheno = await LogoLookupService.generateFormattedFicheNo({
          trcode: invoice.TYPE,
          tableName: 'INVOICE',
          format: faturaFormat,
          veritabaniId,
        })
      }
      else {
        // Use default fiche number generation
        ficheno = await LogoLookupService.generateNewFicheNo({
          trcode: invoice.TYPE,
          tableName: 'INVOICE',
          veritabaniId,
        })
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided
      const totalVat = totals?.totalVat || 0
      const totalNet = totals?.totalNet || 0
      const totalGross = totals?.totalGross || 0
      const totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(
          invoice.DATE,
          invoice.CURR_INVOICE,
          veritabaniId,
        )
        tcXrate = exchangeRate || 1
        rcXrate = exchangeRate || 1
      }

      // Get source cost group
      let sourceCostGrp = 0
      if (invoice.SOURCE_WH) {
        sourceCostGrp = await LogoLookupService.getSourceCostGrp(invoice.SOURCE_WH, veritabaniId) || 0
      }

      consola.info(`Logo INVOICE tablosuna ekleme başlatılıyor. Fatura No: ${ficheno}`)

      // Map invoice fields to Logo INVOICE table structure based on the provided JSON example
      await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, invoice.TYPE)
        .input('FICHENO', sql.VarChar(17), ficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('TIME_', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE || '')
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('CLIENTREF', sql.Int, clientRef || 0)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('ACCOUNTREF', sql.Int, 0) // Account reference
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCECOSTGRP', sql.SmallInt, sourceCostGrp)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('ACCOUNTED', sql.SmallInt, 0)
        .input('PAIDINCASH', sql.SmallInt, 0)
        .input('FROMKASA', sql.SmallInt, 0)
        .input('ENTEGSET', sql.SmallInt, 247) // Based on example
        .input('VAT', sql.Float, totalVat)
        .input('ADDDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTED', sql.Float, totalDiscounted)
        .input('ADDEXPENSES', sql.Float, 0)
        .input('TOTALEXPENSES', sql.Float, 0)
        .input('DISTEXPENSE', sql.Float, 0)
        .input('TOTALDEPOZITO', sql.Float, 0)
        .input('TOTALPROMOTIONS', sql.Float, 0)
        .input('VATINCGROSS', sql.SmallInt, 0) // Based on example
        .input('TOTALVAT', sql.Float, totalVat)
        .input('GROSSTOTAL', sql.Float, totalGross)
        .input('NETTOTAL', sql.Float, totalNet)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1 || '')
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2 || '')
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3 || '')
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4 || '')
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5 || '')
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6 || '')
        .input('INTERESTAPP', sql.SmallInt, 0)
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('TRNET', sql.Float, totalNet / tcXrate)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, totalNet / rcXrate)
        .input('ONLYONEPAYLINE', sql.SmallInt, 0)
        .input('KASTRANSREF', sql.Int, 0)
        .input('PAYDEFREF', sql.Int, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('GVATINC', sql.SmallInt, 0) // Based on example
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('ACCFICHEREF', sql.Int, 0)
        .input('ADDEXPACCREF', sql.Int, 0)
        .input('ADDEXPCENTREF', sql.Int, 0)
        .input('DECPRDIFF', sql.Float, 0)
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREADEDDATE', sql.DateTime, now) // Note: CREADEDDATE not CREATEDDATE
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .input('SALESMANREF', sql.Int, salesmanRef || 0)
        .input('CANCELLEDACC', sql.SmallInt, 0)
        .input('SHPTYPCOD', sql.VarChar(13), '')
        .input('SHPAGNCOD', sql.VarChar(13), '')
        .input('TRACKNR', sql.VarChar(17), '')
        .input('GENEXCTYP', sql.SmallInt, 0)
        .input('LINEEXCTYP', sql.SmallInt, 0)
        .input('TRADINGGRP', sql.VarChar(17), '')
        .input('TEXTINC', sql.SmallInt, 0)
        .input('SITEID', sql.SmallInt, 0)
        .input('RECSTATUS', sql.SmallInt, 0)
        .input('ORGLOGICREF', sql.Int, 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('WFSTATUS', sql.SmallInt, 0)
        .input('SHIPINFOREF', sql.Int, 0)
        .input('DISTORDERREF', sql.Int, 0)
        .input('SENDCNT', sql.SmallInt, 0)
        .input('DLVCLIENT', sql.Int, 0)
        .input('COSTOFSALEFCREF', sql.Int, 0)
        .input('OPSTAT', sql.SmallInt, 0)
        .input('DOCTRACKINGNR', sql.VarChar(33), invoice.DOC_NUMBER || '')
        .input('TOTALADDTAX', sql.Float, 0)
        .input('PAYMENTTYPE', sql.SmallInt, 0)
        .input('INFIDX', sql.SmallInt, 0)
        .input('ACCOUNTEDCNT', sql.SmallInt, 0)
        .input('ORGLOGOID', sql.VarChar(25), '')
        .input('FROMEXIM', sql.SmallInt, 0)
        .input('FRGTYPCOD', sql.VarChar(17), '')
        .input('EXIMFCTYPE', sql.SmallInt, 0)
        .input('FROMORDWITHPAY', sql.SmallInt, 0)
        .input('PROJECTREF', sql.Int, projectRef || 0)
        .input('WFLOWCRDREF', sql.Int, 0)
        .input('STATUS', sql.SmallInt, 0)
        .input('DEDUCTIONPART1', sql.SmallInt, 0)
        .input('DEDUCTIONPART2', sql.SmallInt, 0)
        .input('TOTALEXADDTAX', sql.Float, 0)
        .input('EXACCOUNTED', sql.SmallInt, 0)
        .input('FROMBANK', sql.SmallInt, 0)
        .input('BNTRANSREF', sql.Int, 0)
        .input('AFFECTCOLLATRL', sql.SmallInt, 0)
        .input('GRPFIRMTRANS', sql.SmallInt, 0)
        .input('AFFECTRISK', sql.SmallInt, 0)
        .input('CONTROLINFO', sql.SmallInt, 0)
        .input('POSTRANSFERINFO', sql.SmallInt, 0)
        .input('TAXFREECHX', sql.SmallInt, 0)
        .input('PASSPORTNO', sql.VarChar(51), '')
        .input('CREDITCARDNO', sql.VarChar(51), '')
        .input('INEFFECTIVECOST', sql.SmallInt, 0)
        .input('REFLECTED', sql.SmallInt, 0)
        .input('REFLACCFICHEREF', sql.Int, 0)
        .input('CANCELLEDREFLACC', sql.SmallInt, 0)
        .input('CREDITCARDNUM', sql.VarChar(51), '')
        .input('APPROVE', sql.SmallInt, 0)
        .input('CANTCREDEDUCT', sql.SmallInt, 0)
        .input('ENTRUST', sql.SmallInt, 0)
        .input('EINVOICE', sql.SmallInt, 0)
        .input('PROFILEID', sql.SmallInt, 0)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('ESTATUS', sql.SmallInt, 0)
        .input('EDESCRIPTION', sql.VarChar(51), '')
        .input('EDURATION', sql.SmallInt, 0)
        .input('EDURATIONTYPE', sql.SmallInt, 0)
        .input('DEVIR', sql.SmallInt, 0)
        .input('DISTADJPRICEUFRS', sql.Float, 0)
        .input('COSFCREFUFRS', sql.Int, 0)
        .input('GLOBALID', sql.VarChar(51), '')
        .input('TOTALSERVICES', sql.Float, 0)
        .input('FROMLEASING', sql.SmallInt, 0)
        .input('CANCELEXP', sql.VarChar(51), '')
        .input('UNDOEXP', sql.VarChar(51), '')
        .input('VATEXCEPTREASON', sql.VarChar(51), '')
        .input('CAMPAIGNCODE', sql.VarChar(51), '')
        .input('CANCELDESPSINV', sql.SmallInt, 0)
        .input('FROMEXCHDIFF', sql.SmallInt, 0)
        .input('EXIMVAT', sql.SmallInt, 0)
        .input('SERIALCODE', sql.VarChar(51), '')
        .input('APPCLDEDUCTLIM', sql.SmallInt, 0)
        .input('EINVOICETYP', sql.SmallInt, 0)
        .input('VATEXCEPTCODE', sql.VarChar(51), '')
        .input('OFFERREF', sql.Int, 0)
        .input('ATAXEXCEPTREASON', sql.VarChar(51), '')
        .input('ATAXEXCEPTCODE', sql.VarChar(51), '')
        .input('FROMSTAFFOTHEREX', sql.SmallInt, 0)
        .input('NOCALCULATE', sql.SmallInt, 0)
        .input('INSTEADOFDESP', sql.SmallInt, 0)
        .input('OKCFICHE', sql.SmallInt, 0)
        .input('FRGTYPDESC', sql.VarChar(51), '')
        .input('MARKREF', sql.Int, 0)
        .input('DELIVERCODE', sql.VarChar(51), '')
        .input('ACCEPTEINVPUBLIC', sql.SmallInt, 0)
        .input('PUBLICBNACCREF', sql.Int, 0)
        .input('TYPECODE', sql.VarChar(51), '')
        .input('FUTMNTHYREXPINC', sql.SmallInt, 0)
        .input('DOCDETAIL', sql.VarChar(51), '')
        .input('CALCADDTAXVATSEP', sql.SmallInt, 0)
        .input('ELECTDOC', sql.SmallInt, 0)
        .input('NOTIFYCRDREF', sql.Int, 0)
        .input('GIBACCFICHEREF', sql.Int, 0)
        .input('FROMINTEGTYPE', sql.SmallInt, 0)
        .input('EPRINTCNT', sql.SmallInt, 0)
        .input('RIGHTOFRETURNTYP', sql.SmallInt, 0)
        .input('CLNOTREFLACNTRREF', sql.Int, 0)
        .input('CLNOTREFLAACCREF', sql.Int, 0)
        .input('COSFCREFINFL', sql.Int, 0)
        .input('ORDFICHECMREF', sql.Int, 0)
        .input('ESENDTIME', sql.Int, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE (
            GRPCODE, TRCODE, FICHENO, DATE_, TIME_, DOCODE, SPECODE, CYPHCODE,
            CLIENTREF, RECVREF, CENTERREF, ACCOUNTREF, SOURCEINDEX, SOURCECOSTGRP,
            CANCELLED, ACCOUNTED, PAIDINCASH, FROMKASA, ENTEGSET, VAT,
            ADDDISCOUNTS, TOTALDISCOUNTS, TOTALDISCOUNTED, ADDEXPENSES, TOTALEXPENSES,
            DISTEXPENSE, TOTALDEPOZITO, TOTALPROMOTIONS, VATINCGROSS, TOTALVAT,
            GROSSTOTAL, NETTOTAL, GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6,
            INTERESTAPP, TRCURR, TRRATE, TRNET, REPORTRATE, REPORTNET,
            ONLYONEPAYLINE, KASTRANSREF, PAYDEFREF, PRINTCNT, GVATINC, BRANCH, DEPARTMENT,
            ACCFICHEREF, ADDEXPACCREF, ADDEXPCENTREF, DECPRDIFF,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREADEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC, SALESMANREF, CANCELLEDACC, SHPTYPCOD, SHPAGNCOD,
            TRACKNR, GENEXCTYP, LINEEXCTYP, TRADINGGRP, TEXTINC, SITEID, RECSTATUS,
            ORGLOGICREF, FACTORYNR, WFSTATUS, SHIPINFOREF, DISTORDERREF, SENDCNT,
            DLVCLIENT, COSTOFSALEFCREF, OPSTAT, DOCTRACKINGNR, TOTALADDTAX,
            PAYMENTTYPE, INFIDX, ACCOUNTEDCNT, ORGLOGOID, FROMEXIM, FRGTYPCOD,
            EXIMFCTYPE, FROMORDWITHPAY, PROJECTREF, WFLOWCRDREF, STATUS,
            DEDUCTIONPART1, DEDUCTIONPART2, TOTALEXADDTAX, EXACCOUNTED, FROMBANK,
            BNTRANSREF, AFFECTCOLLATRL, GRPFIRMTRANS, AFFECTRISK, CONTROLINFO,
            POSTRANSFERINFO, TAXFREECHX, PASSPORTNO, CREDITCARDNO, INEFFECTIVECOST,
            REFLECTED, REFLACCFICHEREF, CANCELLEDREFLACC, CREDITCARDNUM, APPROVE,
            CANTCREDEDUCT, ENTRUST, EINVOICE, PROFILEID, GUID, ESTATUS, EDESCRIPTION,
            EDURATION, EDURATIONTYPE, DEVIR, DISTADJPRICEUFRS, COSFCREFUFRS,
            GLOBALID, TOTALSERVICES, FROMLEASING, CANCELEXP, UNDOEXP, VATEXCEPTREASON,
            CAMPAIGNCODE, CANCELDESPSINV, FROMEXCHDIFF, EXIMVAT, SERIALCODE,
            APPCLDEDUCTLIM, EINVOICETYP, VATEXCEPTCODE, OFFERREF, ATAXEXCEPTREASON,
            ATAXEXCEPTCODE, FROMSTAFFOTHEREX, NOCALCULATE, INSTEADOFDESP, OKCFICHE,
            FRGTYPDESC, MARKREF, DELIVERCODE, ACCEPTEINVPUBLIC, PUBLICBNACCREF,
            TYPECODE, FUTMNTHYREXPINC, DOCDETAIL, CALCADDTAXVATSEP, ELECTDOC,
            NOTIFYCRDREF, GIBACCFICHEREF, FROMINTEGTYPE, EPRINTCNT, RIGHTOFRETURNTYP,
            CLNOTREFLACNTRREF, CLNOTREFLAACCREF, COSFCREFINFL, ORDFICHECMREF, ESENDTIME
          )
          VALUES (
            @GRPCODE, @TRCODE, @FICHENO, @DATE_, @TIME_, @DOCODE, @SPECODE, @CYPHCODE,
            @CLIENTREF, @RECVREF, @CENTERREF, @ACCOUNTREF, @SOURCEINDEX, @SOURCECOSTGRP,
            @CANCELLED, @ACCOUNTED, @PAIDINCASH, @FROMKASA, @ENTEGSET, @VAT,
            @ADDDISCOUNTS, @TOTALDISCOUNTS, @TOTALDISCOUNTED, @ADDEXPENSES, @TOTALEXPENSES,
            @DISTEXPENSE, @TOTALDEPOZITO, @TOTALPROMOTIONS, @VATINCGROSS, @TOTALVAT,
            @GROSSTOTAL, @NETTOTAL, @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6,
            @INTERESTAPP, @TRCURR, @TRRATE, @TRNET, @REPORTRATE, @REPORTNET,
            @ONLYONEPAYLINE, @KASTRANSREF, @PAYDEFREF, @PRINTCNT, @GVATINC, @BRANCH, @DEPARTMENT,
            @ACCFICHEREF, @ADDEXPACCREF, @ADDEXPCENTREF, @DECPRDIFF,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREADEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC, @SALESMANREF, @CANCELLEDACC, @SHPTYPCOD, @SHPAGNCOD,
            @TRACKNR, @GENEXCTYP, @LINEEXCTYP, @TRADINGGRP, @TEXTINC, @SITEID, @RECSTATUS,
            @ORGLOGICREF, @FACTORYNR, @WFSTATUS, @SHIPINFOREF, @DISTORDERREF, @SENDCNT,
            @DLVCLIENT, @COSTOFSALEFCREF, @OPSTAT, @DOCTRACKINGNR, @TOTALADDTAX,
            @PAYMENTTYPE, @INFIDX, @ACCOUNTEDCNT, @ORGLOGOID, @FROMEXIM, @FRGTYPCOD,
            @EXIMFCTYPE, @FROMORDWITHPAY, @PROJECTREF, @WFLOWCRDREF, @STATUS,
            @DEDUCTIONPART1, @DEDUCTIONPART2, @TOTALEXADDTAX, @EXACCOUNTED, @FROMBANK,
            @BNTRANSREF, @AFFECTCOLLATRL, @GRPFIRMTRANS, @AFFECTRISK, @CONTROLINFO,
            @POSTRANSFERINFO, @TAXFREECHX, @PASSPORTNO, @CREDITCARDNO, @INEFFECTIVECOST,
            @REFLECTED, @REFLACCFICHEREF, @CANCELLEDREFLACC, @CREDITCARDNUM, @APPROVE,
            @CANTCREDEDUCT, @ENTRUST, @EINVOICE, @PROFILEID, @GUID, @ESTATUS, @EDESCRIPTION,
            @EDURATION, @EDURATIONTYPE, @DEVIR, @DISTADJPRICEUFRS, @COSFCREFUFRS,
            @GLOBALID, @TOTALSERVICES, @FROMLEASING, @CANCELEXP, @UNDOEXP, @VATEXCEPTREASON,
            @CAMPAIGNCODE, @CANCELDESPSINV, @FROMEXCHDIFF, @EXIMVAT, @SERIALCODE,
            @APPCLDEDUCTLIM, @EINVOICETYP, @VATEXCEPTCODE, @OFFERREF, @ATAXEXCEPTREASON,
            @ATAXEXCEPTCODE, @FROMSTAFFOTHEREX, @NOCALCULATE, @INSTEADOFDESP, @OKCFICHE,
            @FRGTYPDESC, @MARKREF, @DELIVERCODE, @ACCEPTEINVPUBLIC, @PUBLICBNACCREF,
            @TYPECODE, @FUTMNTHYREXPINC, @DOCDETAIL, @CALCADDTAXVATSEP, @ELECTDOC,
            @NOTIFYCRDREF, @GIBACCFICHEREF, @FROMINTEGTYPE, @EPRINTCNT, @RIGHTOFRETURNTYP,
            @CLNOTREFLACNTRREF, @CLNOTREFLAACCREF, @COSFCREFINFL, @ORDFICHECMREF, @ESENDTIME
          )
        `)

      // Get the inserted record's LOGICALREF
      const selectResult = await logoConnection.request()
        .input('FICHENO', sql.VarChar(17), ficheno)
        .query(`
          SELECT TOP 1 LOGICALREF, FICHENO
          FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
          WHERE FICHENO = @FICHENO
          ORDER BY LOGICALREF DESC
        `)

      if (selectResult.recordset.length > 0 && selectResult.recordset[0]) {
        const insertedRecord = selectResult.recordset[0]
        consola.success(`Logo INVOICE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO,
        }
      }

      throw new Error('INVOICE tablosuna ekleme başarısız oldu - LOGICALREF alınamadı')
    }
    catch (error) {
      consola.error('Logo INVOICE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },
}

export default LogoErpInvoice
