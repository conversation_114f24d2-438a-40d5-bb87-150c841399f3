import type { RequestHand<PERSON> } from 'express'
import consola from 'consola'
import { Router } from 'express'
import { z } from 'zod'
import { getAltGruplar } from '../services/alt-gruplar-service.ts'

const router = Router()

/**
 * Get alt gruplar validation schema
 */
const getAltGruplarSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid(),
})

/**
 * @openapi
 * components:
 *   schemas:
 *     AltGrup:
 *       type: object
 *       required:
 *         - id
 *         - kodu
 *         - adi
 *         - veritabani_id
 *       properties:
 *         id:
 *           type: integer
 *           description: Alt grup benzersiz tanımlayıcısı
 *         kodu:
 *           type: string
 *           description: Alt grup kodu
 *         adi:
 *           type: string
 *           description: Alt grup adı
 *         veritabani_id:
 *           type: string
 *           description: Veritabanı adı
 *
 * /alt-gruplar:
 *   get:
 *     tags:
 *       - Alt Gruplar
 *     summary: Logo veritabanından alt grup listesini getirir
 *     security:
 *       - sessionAuth: []
 *     parameters:
 *       - in: query
 *         name: veritabani_id
 *         required: true
 *         schema:
 *           type: uuid
 *         description: Logo veritabanı ID
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AltGrup'
 *       400:
 *         description: Geçersiz parametreler
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     type: array
 *                     items:
 *                       type: string
 *       401:
 *         description: Yetkisiz erişim
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
const getAltGruplarHandler: RequestHandler = async (req, res) => {
  try {
    const result = getAltGruplarSchema.safeParse(req.query)
    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path[0] as string
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      res.status(400).json({ errors })
      return
    }

    const { veritabani_id } = result.data
    const altGruplar = await getAltGruplar({ veritabaniId: veritabani_id })
    res.json(altGruplar)
  }
  catch (error) {
    consola.error('Alt gruplar alınırken hata oluştu:', error)
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Alt gruplar alınırken bir hata oluştu',
    })
  }
}

router.get('/', getAltGruplarHandler)

export default router
