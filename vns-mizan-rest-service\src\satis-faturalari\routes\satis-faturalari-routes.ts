import type { Request<PERSON>and<PERSON> } from 'express'
import { Router } from 'express'
import { z } from 'zod'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import SatisFaturalariService from '../services/satis-faturalari-service.ts'

const router = Router()

// Date format regex (YYYY-MM-DD)
const dateFormatRegex = /^\d{4}-\d{2}-\d{2}$/

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Satış Faturası request validation schema
 */
export const satisFaturalariSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid('Geçerli bir UUID formatında veritabanı ID girilmelidir'),

  // Logo kullanıcı bilgileri
  logo: z.object({
    kullanici_adi: z.string().describe('Logo kullanıcı adı'),
    sifre: z.string().describe('Logo kullanıcı şifresi'),
  }).optional().describe('Logo kullanıcı bilgileri. Eğer belirtilmezse config.json\'daki kullanıcı bilgileri kullanılır'),

  // Fatura bilgileri
  fatura_turu: z
    .number({
      required_error: 'Fatura türü girilmelidir',
    })
    .min(7, 'Fatura türü minimum 7 olmalıdır')
    .max(8, 'Fatura türü maksimum 8 olmalıdır')
    .describe('Fatura türü: 7-Perakende Satış Faturası, 8-Toptan Satış Faturası'),

  fatura_no: z
    .string()
    .max(16, 'Fatura numarası en fazla 16 karakter olabilir')
    .optional()
    .describe(
      'Fatura numarası. Üç farklı şekilde kullanılabilir:\n'
      + '1. Belirli bir değer (örn: "00131") - Belirtilen fatura numarası kullanılır\n'
      + '2. "~" - Otomatik numara oluşturulur (bu durumda fatura_numarasi_formati zorunludur)\n'
      + '3. Boş bırakılırsa - Logo tarafından otomatik atanır',
    ),

  fatura_numarasi_formati: z
    .string()
    .max(16, 'Fatura numarası formatı en fazla 16 karakter olabilir')
    .optional()
    .describe(
      'Fatura numarası formatı. fatura_no="~" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:\n'
      + '1. "_" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: "ABC____" -> "ABC0001")\n'
      + '2. Format en az bir "_" karakteri ile bitmelidir\n'
      + '3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl\n'
      + '4. Örnek formatlar:\n'
      + '   - "ABC____" -> "ABC0001", "ABC0002", ...\n'
      + '   - "INV[yyyy]___" -> "INV2023001", "INV2023002", ... (2023 yılında)\n'
      + '   - "F[yy][mm]___" -> "F2305001", "F2305002", ... (Mayıs 2023\'te)\n'
      + '   - "FT[gg][aa]__" -> "FT0105__" (1 Mayıs\'ta)',
    ),

  tarihi: z
    .string({
      required_error: 'Fatura tarihi girilmelidir',
    })
    .regex(dateFormatRegex, 'Tarih formatı YYYY-MM-DD olmalıdır')
    .describe('Fatura tarihi (YYYY-MM-DD)'),

  saati: z
    .string({
      required_error: 'Fatura saati girilmelidir',
    })
    .regex(timeFormatRegex, 'Saat formatı HH:MM:SS olmalıdır')
    .describe('Fatura saati (HH:MM:SS)'),

  belge_no: z
    .string()
    .max(32, 'Belge numarası en fazla 32 karakter olabilir')
    .optional()
    .describe('Belge numarası'),

  ozel_kod: z
    .string()
    .max(10, 'Özel kod en fazla 10 karakter olabilir')
    .optional()
    .describe('Özel kod'),

  cari_kodu: z
    .string()
    .max(16, 'Cari kodu en fazla 16 karakter olabilir')
    .optional()
    .describe('Cari hesap kodu'),

  ambar_kodu: z
    .number()
    .min(1, 'Ambar kodu minimum 1 olmalıdır')
    .max(9999, 'Ambar kodu maksimum 9999 olabilir')
    .optional()
    .describe('Ambar kodu'),

  fabrika_kodu: z
    .number()
    .min(1, 'Fabrika kodu minimum 1 olmalıdır')
    .max(9999, 'Fabrika kodu maksimum 9999 olabilir')
    .optional()
    .describe('Fabrika kodu'),

  aciklama: z
    .string()
    .max(1800, 'Açıklama en fazla 1800 karakter olabilir')
    .optional()
    .describe('Açıklama metni, 6 parçaya bölünecek'),

  doviz_kuru: z
    .number()
    .min(0, 'Döviz kuru minimum 0 olmalıdır')
    .max(999999.9999, 'Döviz kuru maksimum 999999,9999 olabilir')
    .optional()
    .describe('Döviz kuru'),

  isyeri_kodu: z
    .number()
    .min(1, 'İşyeri kodu minimum 1 olmalıdır')
    .max(9999, 'İşyeri kodu maksimum 9999 olabilir')
    .optional()
    .describe('İşyeri kodu'),

  bolum_kodu: z
    .number()
    .min(1, 'Bölüm kodu minimum 1 olmalıdır')
    .max(9999, 'Bölüm kodu maksimum 9999 olabilir')
    .optional()
    .describe('Bölüm kodu'),

  satis_elemani: z
    .string()
    .max(24, 'Satış elemanı en fazla 24 karakter olabilir')
    .optional()
    .describe('Satış elemanı kodu'),

  proje_kodu: z
    .string()
    .max(100, 'Proje kodu en fazla 100 karakter olabilir')
    .optional()
    .describe('Proje kodu'),

  belge_tarihi: z
    .string()
    .regex(dateFormatRegex, 'Belge tarihi formatı YYYY-MM-DD olmalıdır')
    .optional()
    .describe('Belge tarihi (YYYY-MM-DD)'),

  // Fatura satırları
  fatura_satirlari: z
    .array(
      z.object({
        satir_turu: z
          .number({
            required_error: 'Satır türü girilmelidir',
          })
          .min(0, 'Satır türü minimum 0 olmalıdır')
          .max(2, 'Satır türü maksimum 2 olabilir')
          .describe('Satır türü: 0-Malzeme, 2-İskonto'),

        malzeme_kodu: z
          .string()
          .max(16, 'Malzeme kodu en fazla 16 karakter olabilir')
          .optional()
          .describe('Malzeme kodu'),

        ambar_kodu: z
          .number()
          .min(1, 'Ambar kodu minimum 1 olmalıdır')
          .max(9999, 'Ambar kodu maksimum 9999 olabilir')
          .optional()
          .describe('Ambar kodu'),

        fabrika_kodu: z
          .number()
          .min(1, 'Fabrika kodu minimum 1 olmalıdır')
          .max(9999, 'Fabrika kodu maksimum 9999 olabilir')
          .optional()
          .describe('Fabrika kodu'),

        hareket_ozel_kodu: z
          .string()
          .max(16, 'Hareket özel kodu en fazla 16 karakter olabilir')
          .optional()
          .describe('Hareket özel kodu'),

        miktar: z
          .number()
          .min(0, 'Miktar minimum 0 olmalıdır')
          .max(99999999.99, 'Miktar maksimum 99999999,99 olabilir')
          .optional()
          .describe('Miktar'),

        indirim_tutari: z
          .number()
          .min(0, 'İndirim tutarı minimum 0 olmalıdır')
          .max(99999999.99, 'İndirim tutarı maksimum 99999999,99 olabilir')
          .optional()
          .describe('İndirim tutarı'),

        birim_fiyat: z
          .number()
          .min(0, 'Birim fiyat minimum 0 olmalıdır')
          .max(99999999.99, 'Birim fiyat maksimum 99999999,99 olabilir')
          .optional()
          .describe('Birim fiyat'),

        para_birimi: z
          .string()
          .max(10, 'Para birimi en fazla 10 karakter olabilir')
          .optional()
          .describe('Para birimi'),

        dovizli_birim_fiyat: z
          .number()
          .min(0, 'Dövizli birim fiyat minimum 0 olmalıdır')
          .max(99999999.99, 'Dövizli birim fiyat maksimum 99999999,99 olabilir')
          .optional()
          .describe('Dövizli birim fiyat'),

        doviz_kuru: z
          .number()
          .min(0, 'Döviz kuru minimum 0 olmalıdır')
          .max(999999.9999, 'Döviz kuru maksimum 999999,9999 olabilir')
          .optional()
          .describe('Döviz kuru'),

        aciklama: z
          .string()
          .max(250, 'Açıklama en fazla 250 karakter olabilir')
          .optional()
          .describe('Açıklama'),

        indirim_orani: z
          .number()
          .min(0, 'İndirim oranı minimum 0 olmalıdır')
          .max(100, 'İndirim oranı maksimum 100 olabilir')
          .optional()
          .describe('İndirim oranı'),

        birim_kodu: z
          .string()
          .max(10, 'Birim kodu en fazla 10 karakter olabilir')
          .optional()
          .describe('Birim kodu'),

        kdv_orani: z
          .number()
          .min(0, 'KDV oranı minimum 0 olmalıdır')
          .max(100, 'KDV oranı maksimum 100 olabilir')
          .optional()
          .describe('KDV oranı'),

        satis_elemani: z
          .string()
          .max(24, 'Satış elemanı en fazla 24 karakter olabilir')
          .optional()
          .describe('Satış elemanı kodu'),

        proje_kodu: z
          .string()
          .max(100, 'Proje kodu en fazla 100 karakter olabilir')
          .optional()
          .describe('Proje kodu'),
      }),
    )
    .min(1, 'En az bir fatura satırı gereklidir')
    .max(9999, 'En fazla 9999 fatura satırı olabilir')
    .optional()
    .describe('Fatura satırları'),

  // İrsaliye bilgileri
  irsaliye_no: z
    .string()
    .max(16, 'İrsaliye numarası en fazla 16 karakter olabilir')
    .optional()
    .describe(
      'İrsaliye numarası. Üç farklı şekilde kullanılabilir:\n'
      + '1. Belirli bir değer (örn: "D013345") - Belirtilen irsaliye numarası kullanılır\n'
      + '2. "~" - Otomatik numara oluşturulur (bu durumda irsaliye_numarasi_formati zorunludur)\n'
      + '3. Boş bırakılırsa - Logo tarafından otomatik atanır',
    ),

  irsaliye_numarasi_formati: z
    .string()
    .max(16, 'İrsaliye numarası formatı en fazla 16 karakter olabilir')
    .optional()
    .describe(
      'İrsaliye numarası formatı. irsaliye_no="~" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:\n'
      + '1. "_" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: "XYZ____" -> "XYZ0001")\n'
      + '2. Format en az bir "_" karakteri ile bitmelidir\n'
      + '3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl\n'
      + '4. Örnek formatlar:\n'
      + '   - "XYZ____" -> "XYZ0001", "XYZ0002", ...\n'
      + '   - "DSP[yyyy]___" -> "DSP2023001", "DSP2023002", ... (2023 yılında)\n'
      + '   - "D[yy][mm]___" -> "D2305001", "D2305002", ... (Mayıs 2023\'te)\n'
      + '   - "IR[gg][aa]__" -> "IR0105__" (1 Mayıs\'ta)',
    ),

  irsaliye_tarihi: z
    .string()
    .regex(dateFormatRegex, 'İrsaliye tarihi formatı YYYY-MM-DD olmalıdır')
    .optional()
    .describe('İrsaliye tarihi (YYYY-MM-DD)'),

  irsaliye_saati: z
    .string()
    .regex(timeFormatRegex, 'İrsaliye saati formatı HH:MM:SS olmalıdır')
    .optional()
    .describe('İrsaliye saati (HH:MM:SS)'),
})
  .refine(
    (data) => {
    // Satır türü 0 olan satırlarda birim_kodu ve malzeme_kodu zorunlu olmalı
      if (data.fatura_satirlari) {
        for (const satir of data.fatura_satirlari) {
          if (satir.satir_turu === 0) {
            if (!satir.birim_kodu || !satir.malzeme_kodu) {
              return false
            }
          }
        }
      }
      return true
    },
    {
      message: 'Satır türü 0 olan satırlarda birim_kodu ve malzeme_kodu zorunludur',
      path: ['fatura_satirlari'],
    },
  )
  .refine(
    (data) => {
    // Satır türü 2 olan satırlarda indirim_orani veya indirim_tutari zorunlu olmalı
      if (data.fatura_satirlari) {
        for (const satir of data.fatura_satirlari) {
          if (satir.satir_turu === 2) {
            if (satir.indirim_orani == null && satir.indirim_tutari == null) {
              return false
            }
          }
        }
      }
      return true
    },
    {
      message: 'Satır türü 2 olan satırlarda indirim_orani veya indirim_tutari zorunludur',
      path: ['fatura_satirlari'],
    },
  )
  .refine(
    (data) => {
    // Para birimi döviz kontrolü
      if (data.fatura_satirlari) {
        for (const satir of data.fatura_satirlari) {
          const paraBirimi = satir.para_birimi?.toUpperCase()

          if (paraBirimi && paraBirimi !== 'TL' && paraBirimi !== 'TRY') {
          // Dövizli işlem
            if (satir.birim_fiyat != null && satir.dovizli_birim_fiyat == null && satir.doviz_kuru == null) {
              return false
            }

            if (satir.birim_fiyat == null && satir.dovizli_birim_fiyat == null) {
              return false
            }
          }
        }
      }
      return true
    },
    {
      message: 'Dövizli işlemlerde dovizli_birim_fiyat ve doviz_kuru alanları düzgün doldurulmalıdır',
      path: ['fatura_satirlari'],
    },
  )

/**
 * @openapi
 * /satis-faturalari:
 *   post:
 *     tags: [Satış Faturaları]
 *     summary: Yeni satış faturası oluşturur
 *     security:
 *       - sessionAuth: []
 *     description: |
 *       Logo sisteminde yeni bir satış faturası oluşturur. Fatura başlık bilgileri, kalem ve sevkiyat detayları eklenir.
 *
 *       **Önemli Notlar**:
 *
 *       1. Veritabanı ayarlarında `use_rest=false` olduğunda (doğrudan SQL entegrasyonu):
 *          - Eğer `fatura_no="~"` ise, `fatura_numarasi_formati` zorunludur ve en az bir "_" karakteri ile bitmelidir.
 *          - Eğer `irsaliye_no="~"` ise, `irsaliye_numarasi_formati` zorunludur ve en az bir "_" karakteri ile bitmelidir.
 *          - Boş fatura veya irsaliye numarası (`""`) kabul edilmez.
 *
 *       2. Fatura ve irsaliye numarası kontrolü:
 *          - Belirtilen `fatura_no` değeri, aynı `fatura_turu` için Logo veritabanında zaten mevcutsa hata döner.
 *          - Belirtilen `irsaliye_no` değeri Logo veritabanında zaten mevcutsa hata döner.
 *          - Bu kontroller, fatura ve irsaliye numaralarının benzersiz olmasını sağlar.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             title: SatisFaturaRequest
 *             type: object
 *             required:
 *               - veritabani_id
 *               - fatura_turu
 *               - tarihi
 *               - saati
 *             properties:
 *               veritabani_id:
 *                 type: string
 *                 format: uuid
 *                 description: Logo veritabanı ID'si
 *               logo:
 *                 type: object
 *                 description: Logo kullanıcı bilgileri. Eğer belirtilmezse config.json'daki kullanıcı bilgileri kullanılır
 *                 properties:
 *                   kullanici_adi:
 *                     type: string
 *                     description: Logo kullanıcı adı
 *                   sifre:
 *                     type: string
 *                     description: Logo kullanıcı şifresi
 *               fatura_turu:
 *                 type: integer
 *                 enum: [7, 8]
 *                 description: Fatura türü kodu (7=Perakende Satış, 8=Toptan Satış)
 *               fatura_no:
 *                 type: string
 *                 maxLength: 16
 *                 description: |
 *                   Fatura numarası. Üç farklı şekilde kullanılabilir:
 *                   1. Belirli bir değer (örn: "00131") - Belirtilen fatura numarası kullanılır
 *                   2. "~" - Otomatik numara oluşturulur (bu durumda fatura_numarasi_formati zorunludur)
 *                   3. Boş bırakılırsa - Logo tarafından otomatik atanır
 *               fatura_numarasi_formati:
 *                 type: string
 *                 maxLength: 16
 *                 description: |
 *                   Fatura numarası formatı. fatura_no="~" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:
 *                   1. "_" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: "ABC____" -> "ABC0001")
 *                   2. Format en az bir "_" karakteri ile bitmelidir
 *                   3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl
 *                   4. Örnek formatlar:
 *                      - "ABC____" -> "ABC0001", "ABC0002", ...
 *                      - "INV[yyyy]___" -> "INV2023001", "INV2023002", ... (2023 yılında)
 *                      - "F[yy][mm]___" -> "F2305001", "F2305002", ... (Mayıs 2023'te)
 *                      - "FT[gg][aa]__" -> "FT0105__" (1 Mayıs'ta)
 *               tarihi:
 *                 type: string
 *                 format: date
 *                 description: Fatura tarihi (YYYY-MM-DD)
 *               saati:
 *                 type: string
 *                 format: time
 *                 description: Fatura saati (HH:MM:SS)
 *               belge_no:
 *                 type: string
 *                 maxLength: 32
 *                 description: Belge numarası
 *               ozel_kod:
 *                 type: string
 *                 maxLength: 10
 *                 description: Özel kod
 *               cari_kodu:
 *                 type: string
 *                 maxLength: 16
 *                 description: Cari hesap kodu
 *               ambar_kodu:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 9999
 *                 description: Ambar kodu
 *               fabrika_kodu:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 9999
 *                 description: Fabrika kodu
 *               aciklama:
 *                 type: string
 *                 maxLength: 1800
 *                 description: Açıklama metni
 *               doviz_kuru:
 *                 type: number
 *                 description: Döviz kuru
 *               isyeri_kodu:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 9999
 *                 description: İşyeri kodu
 *               bolum_kodu:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 9999
 *                 description: Bölüm kodu
 *               satis_elemani:
 *                 type: string
 *                 maxLength: 24
 *                 description: Satış elemanı kodu
 *               proje_kodu:
 *                 type: string
 *                 maxLength: 100
 *                 description: Proje kodu
 *               belge_tarihi:
 *                 type: string
 *                 format: date
 *                 description: Belge tarihi (YYYY-MM-DD)
 *               fatura_satirlari:
 *                 type: array
 *                 minItems: 1
 *                 maxItems: 9999
 *                 description: Fatura satırları
 *                 items:
 *                   type: object
 *                   required:
 *                     - satir_turu
 *                   properties:
 *                     satir_turu:
 *                       type: integer
 *                       enum: [0, 2]
 *                       description: Satır türü (0=Malzeme, 2=İskonto)
 *                     malzeme_kodu:
 *                       type: string
 *                       maxLength: 16
 *                       description: Malzeme kodu
 *                     ambar_kodu:
 *                       type: integer
 *                       minimum: 1
 *                       maximum: 9999
 *                       description: Ambar kodu
 *                     fabrika_kodu:
 *                       type: integer
 *                       minimum: 1
 *                       maximum: 9999
 *                       description: Fabrika kodu
 *                     hareket_ozel_kodu:
 *                       type: string
 *                       maxLength: 16
 *                       description: Hareket özel kodu
 *                     miktar:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 99999999.99
 *                       description: Miktar
 *                     indirim_tutari:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 99999999.99
 *                       description: İndirim tutarı
 *                     birim_fiyat:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 99999999.99
 *                       description: Birim fiyat
 *                     para_birimi:
 *                       type: string
 *                       maxLength: 10
 *                       description: Para birimi
 *                     dovizli_birim_fiyat:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 99999999.99
 *                       description: Dövizli birim fiyat
 *                     doviz_kuru:
 *                       type: number
 *                       description: Döviz kuru
 *                     aciklama:
 *                       type: string
 *                       maxLength: 250
 *                       description: Açıklama
 *                     indirim_orani:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 100
 *                       description: İndirim oranı
 *                     birim_kodu:
 *                       type: string
 *                       maxLength: 10
 *                       description: Birim kodu
 *                     kdv_orani:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 100
 *                       description: KDV oranı
 *                     satis_elemani:
 *                       type: string
 *                       maxLength: 24
 *                       description: Satış elemanı kodu
 *                     proje_kodu:
 *                       type: string
 *                       maxLength: 100
 *                       description: Proje kodu
 *               irsaliye_no:
 *                 type: string
 *                 maxLength: 16
 *                 description: |
 *                   İrsaliye numarası. Üç farklı şekilde kullanılabilir:
 *                   1. Belirli bir değer (örn: "D013345") - Belirtilen irsaliye numarası kullanılır
 *                   2. "~" - Otomatik numara oluşturulur (bu durumda irsaliye_numarasi_formati zorunludur)
 *                   3. Boş bırakılırsa - Logo tarafından otomatik atanır
 *               irsaliye_numarasi_formati:
 *                 type: string
 *                 maxLength: 16
 *                 description: |
 *                   İrsaliye numarası formatı. irsaliye_no="~" olduğunda zorunludur ve şu özelliklere sahip olmalıdır:
 *                   1. "_" karakteri sayısal değer için yer tutucu olarak kullanılır (Örn: "XYZ____" -> "XYZ0001")
 *                   2. Format en az bir "_" karakteri ile bitmelidir
 *                   3. Tarih yer tutucuları kullanılabilir: [gg]/[dd]=gün, [aa]/[mm]=ay, [yyyy]=4 haneli yıl, [yy]=2 haneli yıl
 *                   4. Örnek formatlar:
 *                      - "XYZ____" -> "XYZ0001", "XYZ0002", ...
 *                      - "DSP[yyyy]___" -> "DSP2023001", "DSP2023002", ... (2023 yılında)
 *                      - "D[yy][mm]___" -> "D2305001", "D2305002", ... (Mayıs 2023'te)
 *                      - "IR[gg][aa]__" -> "IR0105__" (1 Mayıs'ta)
 *               irsaliye_tarihi:
 *                 type: string
 *                 format: date
 *                 description: İrsaliye tarihi (YYYY-MM-DD)
 *               irsaliye_saati:
 *                 type: string
 *                 format: time
 *                 description: İrsaliye saati (HH:MM:SS)
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: İşlem sonucu mesajı (örn. "Satış faturası başarıyla oluşturuldu" veya "Satış faturası zaten mevcut")
 *                 data:
 *                   type: object
 *                   properties:
 *                     fatura_no:
 *                       type: string
 *                       description: Logo tarafından atanan fatura numarası
 *                     fatura_tarihi:
 *                       type: string
 *                       description: Fatura tarihi (DD.MM.YYYY formatında)
 *                     logicalref:
 *                       type: integer
 *                       description: Logo'daki faturanın LOGICALREF değeri
 *                     veritabani_id:
 *                       type: string
 *                       description: İşlemin yapıldığı veritabanı ID'si
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 errors:
 *                   type: object
 *                   additionalProperties:
 *                     oneOf:
 *                       - type: array
 *                         items:
 *                           type: string
 *                       - type: string
 *                   description: Validasyon hataları
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Hata mesajı
 *                 veritabani_id:
 *                   type: string
 *                   description: İşlemin yapıldığı veritabanı ID'si
 */
const postSatisFaturalariHandler: RequestHandler = async (req, res) => {
  try {
    const result = satisFaturalariSchema.safeParse(req.body || {})

    if (!result.success) {
      const errors = result.error.issues.reduce(
        (acc, issue) => {
          const field = issue.path.join('.')
          if (!acc[field])
            acc[field] = []
          acc[field].push(issue.message)
          return acc
        },
        {} as Record<string, string[]>,
      )

      // Try to get veritabani_id from request body if available
      const veritabani_id = req.body?.veritabani_id || 'unknown'
      res.status(400).json({
        errors,
        veritabani_id,
      })
      return
    }

    // Validate that the veritabani_id exists in the config
    try {
      await getLogoConfigById(result.data.veritabani_id)
    }
    catch (error) {
      const veritabani_id = result.data.veritabani_id || 'unknown'
      res.status(400).json({
        errors: {
          veritabani_id: ['Geçersiz veritabanı ID'],
          message: error instanceof Error ? error.message : 'Bir hata oluştu.',
        },
        veritabani_id,
      })
      return
    }

    // Extract veritabani_id from the data
    const { veritabani_id } = result.data

    // Pass request data directly to the service - it will handle transformation internally
    const response = await SatisFaturalariService.handleCreateInvoice({
      veritabaniId: veritabani_id,
      requestPayload: result.data, // Pass the validated request data
      logoCredentials: result.data.logo, // Pass logo credentials if provided
    })

    // Format the response according to the new structure
    if (response.status === 'success') {
      // Format date to DD.MM.YYYY
      const date = response.data?.date || result.data.tarihi
      const formattedDate = date ? new Date(date).toLocaleDateString('tr-TR') : undefined

      res.status(200).json({
        message: response.data?.message || 'Satış faturası başarıyla oluşturuldu',
        data: {
          fatura_no: response.ficheNo,
          fatura_tarihi: formattedDate,
          logicalref: response.logoRef,
          veritabani_id,
        },
      })
    }
    else {
      // Error response
      res.status(400).json({
        message: response.error || 'Satış faturası oluşturulurken bir hata oluştu',
        veritabani_id,
      })
    }
  }
  catch (error) {
    console.error('Satış faturası kaydedilirken hata oluştu:', error)
    // Try to get veritabani_id from request body if available
    const veritabani_id = req.body?.veritabani_id || 'unknown'
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Bir hata oluştu.',
      veritabani_id,
    })
  }
}

router.post('/', postSatisFaturalariHandler)

export default router
