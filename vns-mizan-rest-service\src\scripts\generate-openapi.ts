import { writeFile } from 'node:fs/promises'
import { join } from 'node:path'
import process from 'node:process'
import { consola } from 'consola'
import swaggerJsdoc from 'swagger-jsdoc'

/**
 * Generates OpenAPI specification and saves it to a JSON file
 */
async function generateOpenApiSpec() {
  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'VNS Mizan Entegrasyon API',
        version: '1.0.0',
      },
      servers: [
        {
          url: '/v1',
          description: 'API V1',
        },
      ],
      components: {
        securitySchemes: {
          sessionAuth: {
            type: 'apiKey',
            in: 'cookie',
            name: 'connect.sid',
            description: 'Session based authentication',
          },
        },
      },
    },
    apis: ['./src/**/*.ts'],
  }
  const openapiSpecification = swaggerJsdoc(options)
  const outputPath = join(process.cwd(), 'src', 'scripts', 'openapi.json')
  await writeFile(outputPath, JSON.stringify(openapiSpecification, null, 2))
  consola.success(`OpenAPI dokümantasyonu bu klasöre oluşturuldu: ${outputPath}`)
}

export default generateOpenApiSpec
