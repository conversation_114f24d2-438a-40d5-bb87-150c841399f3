import { consola } from 'consola'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

export async function getExchangeRate({ date, currency, veritabaniId }: { date: string, currency: string, veritabaniId: string }): Promise<number | undefined> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    // Determine which table to use for exchange rates
    const { recordset: tableExists } = await logoConnection.request().query(`
            SELECT OBJECT_ID('LG_EXCHANGE_${logoConfig.erp.firma_numarasi}') as table_exists
        `)

    const exchangeTableName = tableExists[0]?.table_exists
      ? `LG_EXCHANGE_${logoConfig.erp.firma_numarasi}`
      : `${logoConfig.erp.logodb_master}.dbo.L_DAILYEXCHANGES`

    // Get the exchange rate
    const { recordset } = await logoConnection
      .request()
      .input('date', date)
      .query(`
                SELECT exchange.RATES2
                FROM ${exchangeTableName} as exchange
                LEFT JOIN ${logoConfig.erp.logodb_master}.dbo.L_CURRENCYLIST as currencylist
                    ON currencylist.CURTYPE = exchange.CRTYPE
                    AND currencylist.FIRMNR = ${logoConfig.erp.firma_numarasi}
                WHERE exchange.EDATE = @date
                AND currencylist.CURCODE = '${currency}'
            `)

    return recordset?.[0]?.RATES2
  }
  catch (error) {
    consola.error('Döviz kuru alınırken hata oluştu:', error)
    return undefined
  }
}

export async function getExchangeRateByType({ date, crtype, veritabaniId }: { date: string, crtype: number, veritabaniId: string }): Promise<number | undefined> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

    // Determine which table to use for exchange rates
    const { recordset: tableExists } = await logoConnection.request().query(`
            SELECT OBJECT_ID('LG_EXCHANGE_${logoConfig.erp.firma_numarasi}') as table_exists
        `)

    const exchangeTableName = tableExists[0]?.table_exists
      ? `LG_EXCHANGE_${logoConfig.erp.firma_numarasi}`
      : `${logoConfig.erp.logodb_master}.dbo.L_DAILYEXCHANGES`

    // Get the exchange rate directly by CRTYPE without joining with L_CURRENCYLIST
    const { recordset } = await logoConnection
      .request()
      .input('date', date)
      .input('crtype', crtype)
      .query(`
            SELECT RATES2
            FROM ${exchangeTableName}
            WHERE CONVERT(date,EDATE) = @date
            AND CRTYPE = @crtype
        `)

    return recordset?.[0]?.RATES2
  }
  catch (error) {
    consola.error('Döviz kuru alınırken hata oluştu:', error)
    return undefined
  }
}

export async function getUnitCodeForItem({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const query = `
            SELECT unitsetl.CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS as items
            LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
                ON items.UNITSETREF = unitsetf.LOGICALREF
            LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
                ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
                AND unitsetl.MAINUNIT = 1
            WHERE items.CODE = @code
        `

    const { recordset } = await logoConnection.request().input('code', code).query(query)

    return recordset?.[0]?.CODE || 'ADET'
  }
  catch (error) {
    consola.error('Malzeme birim kodu alınırken hata oluştu:', error)
    return 'ADET'
  }
}

export async function getUnitCodeForSrvcard({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const query = `
            SELECT unitsetl.CODE
            FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
            LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETF as unitsetf
                ON srvcard.UNITSETREF = unitsetf.LOGICALREF
            LEFT JOIN LG_${logoConfig.erp.firma_numarasi}_UNITSETL as unitsetl
                ON unitsetl.UNITSETREF = unitsetf.LOGICALREF
                AND unitsetl.MAINUNIT = 1
            WHERE srvcard.CODE = @code
        `

    const { recordset } = await logoConnection.request().input('code', code).query(query)

    return recordset?.[0]?.CODE || 'ADET'
  }
  catch (error) {
    consola.error('Hizmet birim seti kodu alınırken hata oluştu:', error)
    return 'ADET'
  }
}

export async function getItemCode({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const { recordset } = await logoConnection
      .request()
      .input('code', code)
      .query(`
                SELECT CODE
                FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS
                WHERE CODE = @code
            `)

    if (!recordset || recordset.length === 0) {
      throw new Error(`"${code}" kodlu malzeme Logo'da bulunamadı. Lütfen malzeme kartını kontrol edin.`)
    }

    return recordset[0].CODE
  }
  catch (error) {
    consola.error('Malzeme kodu alınırken hata oluştu:', error)
    throw error
  }
}

export async function checkServiceCodeExists({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const { recordset } = await logoConnection
      .request()
      .input('code', code)
      .query(`
                SELECT CODE
                FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD as srvcard
                WHERE srvcard.CODE = @code
            `)

    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Hizmet kodu kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkCustomerExists({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const { recordset } = await logoConnection
      .request()
      .input('code', code)
      .query(`
                SELECT CODE
                FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
                WHERE CODE = @code
            `)

    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Cari hesap kodu kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function getCustomerVknOrTckn({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<string | undefined> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const { recordset } = await logoConnection
      .request()
      .input('arpCode', code)
      .query<{ vknoveyatckno: string }>(`
                SELECT IIF(clcard.ISPERSCOMP=1, clcard.TCKNO, clcard.TAXNR) as vknoveyatckno
                FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD as clcard
                WHERE clcard.CODE = @arpCode
            `)

    if (!recordset || recordset.length === 0) {
      throw new Error('Cari hesap sistemde kayıtlı değil')
    }

    return recordset[0]?.vknoveyatckno
  }
  catch (error) {
    consola.error('Cari hesap VKN/TCKN bilgisi alınırken hata oluştu:', error)
    throw error
  }
}

export async function getInvoiceFichenoByLogicalref({ logicalref, veritabaniId }: { logicalref: number, veritabaniId: string }): Promise<{ ficheno: string, logicalref: number } | undefined> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

  try {
    const { recordset } = await logoConnection
      .request()
      .input('logicalref', logicalref)
      .query(`
        SELECT FICHENO, LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE
        WHERE LOGICALREF = @logicalref
      `)

    if (recordset && recordset.length > 0) {
      return {
        ficheno: recordset[0].FICHENO,
        logicalref: recordset[0].LOGICALREF,
      }
    }

    return undefined
  }
  catch (error) {
    consola.error(`Fatura numarası Logo'dan alınırken hata oluştu:`, error)
    return undefined
  }
}

export async function getSourceCostGrp({ nr, veritabaniId }: { nr: number, veritabaniId: string }): Promise<number | undefined> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('firmnr', logoConfig.erp.firma_numarasi)
      .input('nr', nr)
      .query(`
        SELECT COSTGRP
        FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIWHOUSE
        WHERE FIRMNR = @firmnr AND NR = @nr
      `)
    return recordset?.[0]?.COSTGRP
  }
  catch (error) {
    consola.error('Ambar maliyet grubu alınırken hata oluştu:', error)
    return undefined
  }
}

export async function checkMasrafMerkeziExists({ masraf_merkezi_kodu, veritabaniId }: { masraf_merkezi_kodu: string, veritabaniId: string }): Promise<boolean> {
  if (!masraf_merkezi_kodu) {
    return true
  }
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('code', masraf_merkezi_kodu)
      .query(`
        SELECT CODE
        FROM LG_${logoConfig.erp.firma_numarasi}_EMCENTER
        WHERE CODE = @code
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Masraf merkezi kodu kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkProjeKoduExists({ proje_kodu, veritabaniId }: { proje_kodu: string, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('code', proje_kodu)
      .query(`
        SELECT CODE
        FROM LG_${logoConfig.erp.firma_numarasi}_PROJECT
        WHERE CODE = @code
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Proje kodu kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkWarehouseExists({ ambar, veritabaniId }: { ambar: number, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('firmnr', logoConfig.erp.firma_numarasi)
      .input('nr', ambar)
      .query(`
        SELECT NR
        FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIWHOUSE
        WHERE FIRMNR = @firmnr AND NR = @nr
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Ambar kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkDivisionExists({ isyeri, veritabaniId }: { isyeri: number, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('firmnr', logoConfig.erp.firma_numarasi)
      .input('nr', isyeri)
      .query(`
        SELECT NR
        FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDIV
        WHERE FIRMNR = @firmnr AND NR = @nr
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('İşyeri kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkDepartmentExists({ bolum, veritabaniId }: { bolum: number, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('firmnr', logoConfig.erp.firma_numarasi)
      .input('nr', bolum)
      .query(`
        SELECT NR
        FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIDEPT
        WHERE FIRMNR = @firmnr AND NR = @nr
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Bölüm kontrolü sırasında hata oluştu:', error)
    return false
  }
}

export async function checkFactoryExists({ fabrika, veritabaniId }: { fabrika: number, veritabaniId: string }): Promise<boolean> {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  try {
    const { recordset } = await logoConnection
      .request()
      .input('firmnr', logoConfig.erp.firma_numarasi)
      .input('nr', fabrika)
      .query(`
        SELECT NR
        FROM ${logoConfig.erp.logodb_master}.dbo.L_CAPIFACTORY
        WHERE FIRMNR = @firmnr AND NR = @nr
      `)
    return recordset?.length > 0
  }
  catch (error) {
    consola.error('Fabrika kontrolü sırasında hata oluştu:', error)
    return false
  }
}
