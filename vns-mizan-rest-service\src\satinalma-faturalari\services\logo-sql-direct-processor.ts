import type {
  SatinalmaFaturaHeader,
  SatinalmaFaturaInput,
  SatinalmaFaturaLineItem,
} from '../models/purchase-invoice.ts'
import { consola } from 'consola'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import LogoSqlLookup from './logo-sql-lookup.ts'

/**
 * Service for processing direct SQL operations to Logo ERP for purchase invoices
 * Used when use_rest=false
 */
const LogoSqlDirectProcessor = {

  /**
   * Process direct SQL integration when use_rest=false
   * Inserts directly into Logo INVOICE table
   */
  processDirectSql: async ({
    invoice,
    veritabaniId,
    logoCredentials,
  }: {
    invoice: SatinalmaFaturaInput
    veritabaniId: string
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ success: boolean, error?: string, logoRef?: number, ficheNo?: string }> => {
    let invoiceRef: number | undefined
    let ficheNo: string | undefined

    try {
      // Calculate totals from line items
      let totalVat = 0
      let totalNet = 0
      let totalGross = 0
      let totalDiscounted = 0

      if (invoice.TRANSACTIONS?.items?.length) {
        for (const line of invoice.TRANSACTIONS.items) {
          const quantity = line.QUANTITY || 0
          const price = line.PRICE || 0
          const edtPrice = line.EDT_PRICE || 0
          const vatRate = line.VAT_RATE || 0

          // For purchase invoices (satinalma), we typically work with:
          // - PRICE: Unit price (can be in foreign currency)
          // - EDT_PRICE: Unit price in document currency
          // - QUANTITY: Quantity
          const lineTotal = quantity * (edtPrice || price)
          const lineVat = lineTotal * (vatRate / 100)
          const lineNet = lineTotal + lineVat

          totalDiscounted += lineTotal // Amount before VAT
          totalVat += lineVat
          totalNet += lineNet // Total including VAT
          totalGross += lineTotal // Same as totalDiscounted for purchase invoices
        }
      }

      // Round totals to 2 decimal places
      totalVat = Math.round(totalVat * 100) / 100
      totalNet = Math.round(totalNet * 100) / 100
      totalGross = Math.round(totalGross * 100) / 100
      totalDiscounted = Math.round(totalDiscounted * 100) / 100

      // Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
      const invoiceResult = await LogoSqlDirectProcessor.insertLogoActualPurchaseInvoice({
        invoice: invoice.INVOICE,
        veritabaniId,
        requestData: invoice.requestData,
        _totals: {
          totalVat,
          totalNet,
          totalGross,
          totalDiscounted,
        },
        _logoCredentials: logoCredentials,
      })

      if (!invoiceResult.logicalref) {
        return { success: false, error: 'Logo INVOICE tablosuna ekleme başarısız oldu' }
      }
      invoiceRef = invoiceResult.logicalref
      ficheNo = invoiceResult.ficheno || ''

      // Insert line items if any
      if (invoice.TRANSACTIONS?.items?.length) {
        const stlinesSuccess = await LogoSqlDirectProcessor.insertLogoActualPurchaseStlines({
          _invoiceRef: invoiceRef,
          _lines: invoice.TRANSACTIONS.items,
          _veritabaniId: veritabaniId,
          _invoiceDate: invoice.INVOICE.DATE,
          _stficheRef: 1, // Assuming stficheRef is 1 for purchase input
        })

        if (!stlinesSuccess) {
          return { success: false, error: 'Logo STLINE tablosuna ekleme başarısız oldu' }
        }
      }

      return {
        success: true,
        logoRef: invoiceRef,
        ficheNo,
      }
    }
    catch (error) {
      consola.error('Doğrudan SQL entegrasyonu sırasında hata oluştu:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Doğrudan SQL entegrasyonu sırasında bilinmeyen hata',
        logoRef: invoiceRef,
      }
    }
  },

  /**
   * Insert into actual Logo INVOICE table for purchase invoices
   * This is a placeholder - the actual implementation would be very large
   */
  insertLogoActualPurchaseInvoice: async ({
    invoice,
    veritabaniId,
    requestData,
    _totals,
    _logoCredentials,
  }: {
    invoice: SatinalmaFaturaHeader
    veritabaniId: string
    requestData?: any
    _totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    _logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const _logoConfig = await getLogoConfigById(veritabaniId)

      // Get client reference from ARP_CODE
      let _clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        _clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let _projectRef = null
      if (invoice.PROJECT_CODE) {
        _projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Get source cost group from SOURCE_WH
      let _sourceCostGrp = null
      if (invoice.SOURCE_WH) {
        _sourceCostGrp = await LogoLookupService.getCostGroupFromWarehouse(invoice.SOURCE_WH, veritabaniId)
      }

      // Determine currency settings
      const currInvoice = invoice.CURR_INVOICE || 0
      let rcXrate = invoice.RC_XRATE || 0

      // If no RC_XRATE provided but we have a currency, get the exchange rate
      if (currInvoice > 0 && !rcXrate) {
        rcXrate = await LogoSqlLookup.getExchangeRateByType({
          date: invoice.DATE,
          crtype: currInvoice,
          veritabaniId,
        }) || 0
      }

      // Generate a new FICHENO if not provided
      let ficheno = invoice.NUMBER

      if (invoice.NUMBER === '~') {
        // Check if a format is provided
        const format = requestData?.fatura_numarasi_formati

        if (format && format.includes('_')) {
          // Use formatted fiche number generation
          ficheno = await LogoLookupService.generateFormattedFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            format,
            veritabaniId,
          })
        }
        else {
          // Use default fiche number generation
          ficheno = await LogoLookupService.generateNewFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            veritabaniId,
          })
        }
      }

      // This is a complex implementation that would require the full Logo INVOICE table structure
      // For the refactoring, we'll implement this as a separate service
      consola.warn('insertLogoActualPurchaseInvoice: Implementation needed - this is a placeholder')
      return { logicalref: 1, ficheno: ficheno || 'TEST001' }
    }
    catch (error) {
      consola.error('Logo INVOICE tablosuna ekleme sırasında hata oluştu:', error)
      return {}
    }
  },

  /**
   * Insert into actual Logo STLINE table for purchase invoices
   * This is a placeholder - the actual implementation would be very large
   */
  insertLogoActualPurchaseStlines: async ({
    _invoiceRef,
    _lines,
    _veritabaniId,
    _invoiceDate,
    _stficheRef,
  }: {
    _invoiceRef: number
    _lines: SatinalmaFaturaLineItem[]
    _veritabaniId: string
    _invoiceDate: string
    _stficheRef: number
  }): Promise<boolean> => {
    // This is a complex implementation that would require the full Logo STLINE table structure
    // For the refactoring, we'll implement this as a separate service
    consola.warn('insertLogoActualPurchaseStlines: Implementation needed - this is a placeholder')
    return true
  },
}

export default LogoSqlDirectProcessor
