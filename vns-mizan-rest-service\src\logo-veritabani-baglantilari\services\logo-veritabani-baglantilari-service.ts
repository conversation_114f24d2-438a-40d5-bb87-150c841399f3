import type { LogoConnectionConfig } from '../../shared/utils/config-utils.ts'
import { writeFile } from 'node:fs/promises'
import consola from 'consola'
import { readConfig } from '../../shared/utils/config-utils.ts'

/**
 * Tüm Logo veritabanı bağlantılarını getirir
 */
export async function getLogoVeritabaniBaglantilari() {
  try {
    const config = await readConfig()
    return config.project_settings.logo.db_connections
  }
  catch (error) {
    consola.error('Logo veritabanı bağlantıları alınırken hata:', error)
    throw error
  }
}

/**
 * Yeni Logo veritabanı bağlantısı ekler
 */
export async function addLogoVeritabaniBaglantisi({ baglanti }: { baglanti: LogoConnectionConfig }) {
  try {
    const config = await readConfig()
    config.project_settings.logo.db_connections.push(baglanti)
    await writeFile('config.json', JSON.stringify(config, null, 2))
    return baglanti
  }
  catch (error) {
    consola.error('Logo veritabanı bağlantısı eklenirken hata:', error)
    throw error
  }
}

/**
 * Logo veritabanı bağlantısını günceller
 */
export async function updateLogoVeritabaniBaglantisi({ id, baglanti }: { id: string, baglanti: LogoConnectionConfig }) {
  try {
    const config = await readConfig()
    const index = config.project_settings.logo.db_connections.findIndex(conn => conn.id === id)
    if (index === -1)
      throw new Error(`${id} ID'li bağlantı bulunamadı`)

    config.project_settings.logo.db_connections[index] = { ...baglanti, id }
    await writeFile('config.json', JSON.stringify(config, null, 2))
    return config.project_settings.logo.db_connections[index]
  }
  catch (error) {
    consola.error('Logo veritabanı bağlantısı güncellenirken hata:', error)
    throw error
  }
}

/**
 * Logo veritabanı bağlantısını siler
 */
export async function deleteLogoVeritabaniBaglantisi({ id }: { id: string }) {
  try {
    const config = await readConfig()
    const index = config.project_settings.logo.db_connections.findIndex(conn => conn.id === id)
    if (index === -1)
      throw new Error(`${id} ID'li bağlantı bulunamadı`)

    config.project_settings.logo.db_connections.splice(index, 1)
    await writeFile('config.json', JSON.stringify(config, null, 2))
    return true
  }
  catch (error) {
    consola.error('Logo veritabanı bağlantısı silinirken hata:', error)
    throw error
  }
}

/**
 * Get Logo connection by ID
 */
export async function getLogoVeritabaniBaglantisiById({ id }: { id: string }) {
  const config = await readConfig()
  const baglanti = config.project_settings.logo.db_connections.find(conn => conn.id === id)

  if (!baglanti)
    throw new Error(`${id} ID'li bağlantı bulunamadı`)

  return baglanti
}
